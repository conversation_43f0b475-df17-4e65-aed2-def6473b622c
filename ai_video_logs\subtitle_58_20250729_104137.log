2025-07-29 10:41:37,070 - INFO - ========== 字幕 #58 处理开始 ==========
2025-07-29 10:41:37,070 - INFO - 字幕内容: 哥哥竟求摄政王娶她，称只要她成为王妃，就能救下所有人。
2025-07-29 10:41:37,071 - INFO - 字幕序号: [1221, 1227]
2025-07-29 10:41:37,071 - INFO - 音频文件详情:
2025-07-29 10:41:37,071 - INFO -   - 路径: output\58.wav
2025-07-29 10:41:37,071 - INFO -   - 时长: 4.90秒
2025-07-29 10:41:37,071 - INFO -   - 验证音频时长: 4.90秒
2025-07-29 10:41:37,072 - INFO - 字幕时间戳信息:
2025-07-29 10:41:37,072 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:37,072 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:37,072 - INFO -   - 根据生成的音频时长(4.90秒)已调整字幕时间戳
2025-07-29 10:41:37,072 - INFO - ========== 新模式：为字幕 #58 生成4套场景方案 ==========
2025-07-29 10:41:37,072 - INFO - 字幕序号列表: [1221, 1227]
2025-07-29 10:41:37,072 - INFO - 
--- 生成方案 #1：基于字幕序号 #1221 ---
2025-07-29 10:41:37,072 - INFO - 开始为单个字幕序号 #1221 匹配场景，目标时长: 4.90秒
2025-07-29 10:41:37,072 - INFO - 开始查找字幕序号 [1221] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:37,073 - INFO - 找到related_overlap场景: scene_id=1576, 字幕#1221
2025-07-29 10:41:37,075 - INFO - 找到related_between场景: scene_id=1574, 字幕#1221
2025-07-29 10:41:37,075 - INFO - 找到related_between场景: scene_id=1575, 字幕#1221
2025-07-29 10:41:37,075 - INFO - 找到related_between场景: scene_id=1577, 字幕#1221
2025-07-29 10:41:37,075 - INFO - 找到related_between场景: scene_id=1578, 字幕#1221
2025-07-29 10:41:37,075 - INFO - 找到related_between场景: scene_id=1579, 字幕#1221
2025-07-29 10:41:37,075 - INFO - 找到related_between场景: scene_id=1580, 字幕#1221
2025-07-29 10:41:37,075 - INFO - 找到related_between场景: scene_id=1581, 字幕#1221
2025-07-29 10:41:37,075 - INFO - 找到related_between场景: scene_id=1582, 字幕#1221
2025-07-29 10:41:37,075 - INFO - 找到related_between场景: scene_id=1583, 字幕#1221
2025-07-29 10:41:37,076 - INFO - 字幕 #1221 找到 1 个overlap场景, 9 个between场景
2025-07-29 10:41:37,076 - INFO - 字幕序号 #1221 找到 1 个可用overlap场景, 9 个可用between场景
2025-07-29 10:41:37,077 - INFO - 选择第一个overlap场景作为起点: scene_id=1576
2025-07-29 10:41:37,077 - INFO - 添加起点场景: scene_id=1576, 时长=2.16秒, 累计时长=2.16秒
2025-07-29 10:41:37,077 - INFO - 起点场景时长不足，需要延伸填充 2.74秒
2025-07-29 10:41:37,077 - INFO - 起点场景在原始列表中的索引: 1575
2025-07-29 10:41:37,077 - INFO - 延伸添加场景: scene_id=1577 (完整时长 1.04秒)
2025-07-29 10:41:37,078 - INFO - 累计时长: 3.20秒
2025-07-29 10:41:37,078 - INFO - 延伸添加场景: scene_id=1578 (裁剪至 1.70秒)
2025-07-29 10:41:37,078 - INFO - 累计时长: 4.90秒
2025-07-29 10:41:37,078 - INFO - 字幕序号 #1221 场景匹配完成，共选择 3 个场景，总时长: 4.90秒
2025-07-29 10:41:37,078 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:37,078 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:37,078 - INFO - 
--- 生成方案 #2：基于字幕序号 #1227 ---
2025-07-29 10:41:37,078 - INFO - 开始为单个字幕序号 #1227 匹配场景，目标时长: 4.90秒
2025-07-29 10:41:37,078 - INFO - 开始查找字幕序号 [1227] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:37,079 - INFO - 找到related_overlap场景: scene_id=1591, 字幕#1227
2025-07-29 10:41:37,081 - INFO - 字幕 #1227 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:37,081 - INFO - 字幕序号 #1227 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:37,081 - INFO - 选择第一个overlap场景作为起点: scene_id=1591
2025-07-29 10:41:37,081 - INFO - 添加起点场景: scene_id=1591, 时长=5.24秒, 累计时长=5.24秒
2025-07-29 10:41:37,081 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:37,082 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:41:37,082 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:37,082 - INFO - ========== 当前模式：为字幕 #58 生成 1 套场景方案 ==========
2025-07-29 10:41:37,082 - INFO - 开始查找字幕序号 [1221, 1227] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:37,082 - INFO - 找到related_overlap场景: scene_id=1576, 字幕#1221
2025-07-29 10:41:37,082 - INFO - 找到related_overlap场景: scene_id=1591, 字幕#1227
2025-07-29 10:41:37,083 - INFO - 找到related_between场景: scene_id=1574, 字幕#1221
2025-07-29 10:41:37,083 - INFO - 找到related_between场景: scene_id=1575, 字幕#1221
2025-07-29 10:41:37,083 - INFO - 找到related_between场景: scene_id=1577, 字幕#1221
2025-07-29 10:41:37,083 - INFO - 找到related_between场景: scene_id=1578, 字幕#1221
2025-07-29 10:41:37,083 - INFO - 找到related_between场景: scene_id=1579, 字幕#1221
2025-07-29 10:41:37,083 - INFO - 找到related_between场景: scene_id=1580, 字幕#1221
2025-07-29 10:41:37,083 - INFO - 找到related_between场景: scene_id=1581, 字幕#1221
2025-07-29 10:41:37,083 - INFO - 找到related_between场景: scene_id=1582, 字幕#1221
2025-07-29 10:41:37,083 - INFO - 找到related_between场景: scene_id=1583, 字幕#1221
2025-07-29 10:41:37,084 - INFO - 字幕 #1221 找到 1 个overlap场景, 9 个between场景
2025-07-29 10:41:37,084 - INFO - 字幕 #1227 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:37,084 - INFO - 共收集 2 个未使用的overlap场景和 9 个未使用的between场景
2025-07-29 10:41:37,084 - INFO - 开始生成方案 #1
2025-07-29 10:41:37,084 - INFO - 方案 #1: 为字幕#1221选择初始化overlap场景id=1576
2025-07-29 10:41:37,084 - INFO - 方案 #1: 为字幕#1227选择初始化overlap场景id=1591
2025-07-29 10:41:37,084 - INFO - 方案 #1: 初始选择后，当前总时长=7.40秒
2025-07-29 10:41:37,084 - INFO - 方案 #1: 额外between选择后，当前总时长=7.40秒
2025-07-29 10:41:37,084 - INFO - 方案 #1: 场景总时长(7.40秒)大于音频时长(4.90秒)，需要裁剪
2025-07-29 10:41:37,084 - INFO - 调整前总时长: 7.40秒, 目标时长: 4.90秒
2025-07-29 10:41:37,084 - INFO - 需要裁剪 2.50秒
2025-07-29 10:41:37,084 - INFO - 裁剪最长场景ID=1591：从5.24秒裁剪至2.74秒
2025-07-29 10:41:37,084 - INFO - 调整后总时长: 4.90秒，与目标时长差异: 0.00秒
2025-07-29 10:41:37,085 - INFO - 方案 #1 调整/填充后最终总时长: 4.90秒
2025-07-29 10:41:37,085 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:37,085 - INFO - ========== 当前模式：字幕 #58 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:37,085 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:37,085 - INFO - ========== 新模式：字幕 #58 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:37,085 - INFO - 
----- 处理字幕 #58 的方案 #1 -----
2025-07-29 10:41:37,085 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-29 10:41:37,085 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpynh_apo6
2025-07-29 10:41:37,086 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1576.mp4 (确认存在: True)
2025-07-29 10:41:37,086 - INFO - 添加场景ID=1576，时长=2.16秒，累计时长=2.16秒
2025-07-29 10:41:37,086 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1577.mp4 (确认存在: True)
2025-07-29 10:41:37,086 - INFO - 添加场景ID=1577，时长=1.04秒，累计时长=3.20秒
2025-07-29 10:41:37,086 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1578.mp4 (确认存在: True)
2025-07-29 10:41:37,086 - INFO - 添加场景ID=1578，时长=3.16秒，累计时长=6.36秒
2025-07-29 10:41:37,086 - INFO - 准备合并 3 个场景文件，总时长约 6.36秒
2025-07-29 10:41:37,086 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1576.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1577.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1578.mp4'

2025-07-29 10:41:37,087 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpynh_apo6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpynh_apo6\temp_combined.mp4
2025-07-29 10:41:37,266 - INFO - 合并后的视频时长: 6.43秒，目标音频时长: 4.90秒
2025-07-29 10:41:37,266 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpynh_apo6\temp_combined.mp4 -ss 0 -to 4.899 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-29 10:41:37,644 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:37,644 - INFO - 目标音频时长: 4.90秒
2025-07-29 10:41:37,644 - INFO - 实际视频时长: 4.94秒
2025-07-29 10:41:37,644 - INFO - 时长差异: 0.04秒 (0.90%)
2025-07-29 10:41:37,644 - INFO - ==========================================
2025-07-29 10:41:37,644 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:37,644 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-29 10:41:37,645 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpynh_apo6
2025-07-29 10:41:37,710 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:37,710 - INFO -   - 音频时长: 4.90秒
2025-07-29 10:41:37,710 - INFO -   - 视频时长: 4.94秒
2025-07-29 10:41:37,710 - INFO -   - 时长差异: 0.04秒 (0.90%)
2025-07-29 10:41:37,710 - INFO - 
----- 处理字幕 #58 的方案 #2 -----
2025-07-29 10:41:37,710 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-07-29 10:41:37,711 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp363jo0i6
2025-07-29 10:41:37,711 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1591.mp4 (确认存在: True)
2025-07-29 10:41:37,711 - INFO - 添加场景ID=1591，时长=5.24秒，累计时长=5.24秒
2025-07-29 10:41:37,712 - INFO - 准备合并 1 个场景文件，总时长约 5.24秒
2025-07-29 10:41:37,712 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1591.mp4'

2025-07-29 10:41:37,712 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp363jo0i6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp363jo0i6\temp_combined.mp4
2025-07-29 10:41:37,875 - INFO - 合并后的视频时长: 5.26秒，目标音频时长: 4.90秒
2025-07-29 10:41:37,875 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp363jo0i6\temp_combined.mp4 -ss 0 -to 4.899 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-07-29 10:41:38,255 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:38,255 - INFO - 目标音频时长: 4.90秒
2025-07-29 10:41:38,255 - INFO - 实际视频时长: 4.94秒
2025-07-29 10:41:38,255 - INFO - 时长差异: 0.04秒 (0.90%)
2025-07-29 10:41:38,255 - INFO - ==========================================
2025-07-29 10:41:38,255 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:38,255 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-07-29 10:41:38,256 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp363jo0i6
2025-07-29 10:41:38,320 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:38,320 - INFO -   - 音频时长: 4.90秒
2025-07-29 10:41:38,320 - INFO -   - 视频时长: 4.94秒
2025-07-29 10:41:38,321 - INFO -   - 时长差异: 0.04秒 (0.90%)
2025-07-29 10:41:38,321 - INFO - 
----- 处理字幕 #58 的方案 #3 -----
2025-07-29 10:41:38,321 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-07-29 10:41:38,321 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmph6uxdfnl
2025-07-29 10:41:38,322 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1576.mp4 (确认存在: True)
2025-07-29 10:41:38,322 - INFO - 添加场景ID=1576，时长=2.16秒，累计时长=2.16秒
2025-07-29 10:41:38,322 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1591.mp4 (确认存在: True)
2025-07-29 10:41:38,322 - INFO - 添加场景ID=1591，时长=5.24秒，累计时长=7.40秒
2025-07-29 10:41:38,322 - INFO - 场景总时长(7.40秒)已达到音频时长(4.90秒)的1.5倍，停止添加场景
2025-07-29 10:41:38,322 - INFO - 准备合并 2 个场景文件，总时长约 7.40秒
2025-07-29 10:41:38,323 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1576.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1591.mp4'

2025-07-29 10:41:38,323 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmph6uxdfnl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmph6uxdfnl\temp_combined.mp4
2025-07-29 10:41:38,485 - INFO - 合并后的视频时长: 7.45秒，目标音频时长: 4.90秒
2025-07-29 10:41:38,485 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmph6uxdfnl\temp_combined.mp4 -ss 0 -to 4.899 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-07-29 10:41:38,905 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:38,905 - INFO - 目标音频时长: 4.90秒
2025-07-29 10:41:38,905 - INFO - 实际视频时长: 4.94秒
2025-07-29 10:41:38,905 - INFO - 时长差异: 0.04秒 (0.90%)
2025-07-29 10:41:38,905 - INFO - ==========================================
2025-07-29 10:41:38,905 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:38,905 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-07-29 10:41:38,907 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmph6uxdfnl
2025-07-29 10:41:38,969 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:38,969 - INFO -   - 音频时长: 4.90秒
2025-07-29 10:41:38,969 - INFO -   - 视频时长: 4.94秒
2025-07-29 10:41:38,969 - INFO -   - 时长差异: 0.04秒 (0.90%)
2025-07-29 10:41:38,969 - INFO - 
字幕 #58 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:38,969 - INFO - 生成的视频文件:
2025-07-29 10:41:38,969 - INFO -   1. F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-07-29 10:41:38,969 - INFO -   2. F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-07-29 10:41:38,969 - INFO -   3. F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-07-29 10:41:38,969 - INFO - ========== 字幕 #58 处理结束 ==========

