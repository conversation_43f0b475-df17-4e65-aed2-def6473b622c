2025-07-29 10:40:50,305 - INFO - ========== 字幕 #27 处理开始 ==========
2025-07-29 10:40:50,305 - INFO - 字幕内容: 摄政王嘲讽她，哥哥立刻维护，并向他道谢。
2025-07-29 10:40:50,305 - INFO - 字幕序号: [146, 152]
2025-07-29 10:40:50,305 - INFO - 音频文件详情:
2025-07-29 10:40:50,305 - INFO -   - 路径: output\27.wav
2025-07-29 10:40:50,305 - INFO -   - 时长: 3.18秒
2025-07-29 10:40:50,306 - INFO -   - 验证音频时长: 3.18秒
2025-07-29 10:40:50,306 - INFO - 字幕时间戳信息:
2025-07-29 10:40:50,306 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:50,306 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:50,306 - INFO -   - 根据生成的音频时长(3.18秒)已调整字幕时间戳
2025-07-29 10:40:50,306 - INFO - ========== 新模式：为字幕 #27 生成4套场景方案 ==========
2025-07-29 10:40:50,306 - INFO - 字幕序号列表: [146, 152]
2025-07-29 10:40:50,306 - INFO - 
--- 生成方案 #1：基于字幕序号 #146 ---
2025-07-29 10:40:50,306 - INFO - 开始为单个字幕序号 #146 匹配场景，目标时长: 3.18秒
2025-07-29 10:40:50,306 - INFO - 开始查找字幕序号 [146] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:50,306 - INFO - 找到related_overlap场景: scene_id=206, 字幕#146
2025-07-29 10:40:50,307 - INFO - 找到related_between场景: scene_id=207, 字幕#146
2025-07-29 10:40:50,307 - INFO - 找到related_between场景: scene_id=208, 字幕#146
2025-07-29 10:40:50,307 - INFO - 找到related_between场景: scene_id=209, 字幕#146
2025-07-29 10:40:50,307 - INFO - 找到related_between场景: scene_id=210, 字幕#146
2025-07-29 10:40:50,307 - INFO - 字幕 #146 找到 1 个overlap场景, 4 个between场景
2025-07-29 10:40:50,307 - INFO - 字幕序号 #146 找到 1 个可用overlap场景, 4 个可用between场景
2025-07-29 10:40:50,307 - INFO - 选择第一个overlap场景作为起点: scene_id=206
2025-07-29 10:40:50,307 - INFO - 添加起点场景: scene_id=206, 时长=3.68秒, 累计时长=3.68秒
2025-07-29 10:40:50,307 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:40:50,307 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:40:50,307 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:40:50,307 - INFO - 
--- 生成方案 #2：基于字幕序号 #152 ---
2025-07-29 10:40:50,307 - INFO - 开始为单个字幕序号 #152 匹配场景，目标时长: 3.18秒
2025-07-29 10:40:50,307 - INFO - 开始查找字幕序号 [152] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:50,307 - INFO - 找到related_overlap场景: scene_id=226, 字幕#152
2025-07-29 10:40:50,307 - INFO - 找到related_overlap场景: scene_id=228, 字幕#152
2025-07-29 10:40:50,308 - INFO - 找到related_between场景: scene_id=229, 字幕#152
2025-07-29 10:40:50,308 - INFO - 字幕 #152 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:50,308 - INFO - 字幕序号 #152 找到 2 个可用overlap场景, 1 个可用between场景
2025-07-29 10:40:50,308 - INFO - 选择第一个overlap场景作为起点: scene_id=226
2025-07-29 10:40:50,308 - INFO - 添加起点场景: scene_id=226, 时长=1.84秒, 累计时长=1.84秒
2025-07-29 10:40:50,308 - INFO - 起点场景时长不足，需要延伸填充 1.34秒
2025-07-29 10:40:50,308 - INFO - 起点场景在原始列表中的索引: 225
2025-07-29 10:40:50,308 - INFO - 延伸添加场景: scene_id=227 (完整时长 1.20秒)
2025-07-29 10:40:50,308 - INFO - 累计时长: 3.04秒
2025-07-29 10:40:50,308 - INFO - 延伸添加场景: scene_id=228 (裁剪至 0.14秒)
2025-07-29 10:40:50,308 - INFO - 累计时长: 3.18秒
2025-07-29 10:40:50,308 - INFO - 字幕序号 #152 场景匹配完成，共选择 3 个场景，总时长: 3.18秒
2025-07-29 10:40:50,308 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:50,308 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:50,308 - INFO - ========== 当前模式：为字幕 #27 生成 1 套场景方案 ==========
2025-07-29 10:40:50,308 - INFO - 开始查找字幕序号 [146, 152] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:50,308 - INFO - 找到related_overlap场景: scene_id=206, 字幕#146
2025-07-29 10:40:50,308 - INFO - 找到related_overlap场景: scene_id=226, 字幕#152
2025-07-29 10:40:50,308 - INFO - 找到related_overlap场景: scene_id=228, 字幕#152
2025-07-29 10:40:50,309 - INFO - 找到related_between场景: scene_id=207, 字幕#146
2025-07-29 10:40:50,309 - INFO - 找到related_between场景: scene_id=208, 字幕#146
2025-07-29 10:40:50,309 - INFO - 找到related_between场景: scene_id=209, 字幕#146
2025-07-29 10:40:50,309 - INFO - 找到related_between场景: scene_id=210, 字幕#146
2025-07-29 10:40:50,309 - INFO - 找到related_between场景: scene_id=229, 字幕#152
2025-07-29 10:40:50,309 - INFO - 字幕 #146 找到 1 个overlap场景, 4 个between场景
2025-07-29 10:40:50,309 - INFO - 字幕 #152 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:50,309 - INFO - 共收集 3 个未使用的overlap场景和 5 个未使用的between场景
2025-07-29 10:40:50,309 - INFO - 开始生成方案 #1
2025-07-29 10:40:50,309 - INFO - 方案 #1: 为字幕#146选择初始化overlap场景id=206
2025-07-29 10:40:50,309 - INFO - 方案 #1: 为字幕#152选择初始化overlap场景id=228
2025-07-29 10:40:50,310 - INFO - 方案 #1: 初始选择后，当前总时长=5.44秒
2025-07-29 10:40:50,310 - INFO - 方案 #1: 额外between选择后，当前总时长=5.44秒
2025-07-29 10:40:50,310 - INFO - 方案 #1: 场景总时长(5.44秒)大于音频时长(3.18秒)，需要裁剪
2025-07-29 10:40:50,310 - INFO - 调整前总时长: 5.44秒, 目标时长: 3.18秒
2025-07-29 10:40:50,310 - INFO - 需要裁剪 2.26秒
2025-07-29 10:40:50,310 - INFO - 裁剪最长场景ID=206：从3.68秒裁剪至1.42秒
2025-07-29 10:40:50,310 - INFO - 调整后总时长: 3.18秒，与目标时长差异: 0.00秒
2025-07-29 10:40:50,310 - INFO - 方案 #1 调整/填充后最终总时长: 3.18秒
2025-07-29 10:40:50,310 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:50,310 - INFO - ========== 当前模式：字幕 #27 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:50,310 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:50,310 - INFO - ========== 新模式：字幕 #27 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:50,310 - INFO - 
----- 处理字幕 #27 的方案 #1 -----
2025-07-29 10:40:50,310 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-29 10:40:50,310 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu0k28n9h
2025-07-29 10:40:50,311 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\206.mp4 (确认存在: True)
2025-07-29 10:40:50,311 - INFO - 添加场景ID=206，时长=3.68秒，累计时长=3.68秒
2025-07-29 10:40:50,311 - INFO - 准备合并 1 个场景文件，总时长约 3.68秒
2025-07-29 10:40:50,311 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/206.mp4'

2025-07-29 10:40:50,311 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpu0k28n9h\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpu0k28n9h\temp_combined.mp4
2025-07-29 10:40:50,424 - INFO - 合并后的视频时长: 3.70秒，目标音频时长: 3.18秒
2025-07-29 10:40:50,424 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpu0k28n9h\temp_combined.mp4 -ss 0 -to 3.176 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-29 10:40:50,650 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:50,650 - INFO - 目标音频时长: 3.18秒
2025-07-29 10:40:50,650 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:40:50,650 - INFO - 时长差异: 0.05秒 (1.48%)
2025-07-29 10:40:50,650 - INFO - ==========================================
2025-07-29 10:40:50,650 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:50,650 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-29 10:40:50,651 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu0k28n9h
2025-07-29 10:40:50,693 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:50,693 - INFO -   - 音频时长: 3.18秒
2025-07-29 10:40:50,693 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:40:50,693 - INFO -   - 时长差异: 0.05秒 (1.48%)
2025-07-29 10:40:50,693 - INFO - 
----- 处理字幕 #27 的方案 #2 -----
2025-07-29 10:40:50,693 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-07-29 10:40:50,693 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnwkdlegk
2025-07-29 10:40:50,694 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\226.mp4 (确认存在: True)
2025-07-29 10:40:50,694 - INFO - 添加场景ID=226，时长=1.84秒，累计时长=1.84秒
2025-07-29 10:40:50,694 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\227.mp4 (确认存在: True)
2025-07-29 10:40:50,694 - INFO - 添加场景ID=227，时长=1.20秒，累计时长=3.04秒
2025-07-29 10:40:50,694 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\228.mp4 (确认存在: True)
2025-07-29 10:40:50,694 - INFO - 添加场景ID=228，时长=1.76秒，累计时长=4.80秒
2025-07-29 10:40:50,694 - INFO - 场景总时长(4.80秒)已达到音频时长(3.18秒)的1.5倍，停止添加场景
2025-07-29 10:40:50,694 - INFO - 准备合并 3 个场景文件，总时长约 4.80秒
2025-07-29 10:40:50,694 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/226.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/227.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/228.mp4'

2025-07-29 10:40:50,694 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnwkdlegk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnwkdlegk\temp_combined.mp4
2025-07-29 10:40:50,853 - INFO - 合并后的视频时长: 4.87秒，目标音频时长: 3.18秒
2025-07-29 10:40:50,853 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnwkdlegk\temp_combined.mp4 -ss 0 -to 3.176 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-07-29 10:40:51,113 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:51,113 - INFO - 目标音频时长: 3.18秒
2025-07-29 10:40:51,113 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:40:51,113 - INFO - 时长差异: 0.05秒 (1.48%)
2025-07-29 10:40:51,113 - INFO - ==========================================
2025-07-29 10:40:51,113 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:51,113 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-07-29 10:40:51,113 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnwkdlegk
2025-07-29 10:40:51,160 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:51,160 - INFO -   - 音频时长: 3.18秒
2025-07-29 10:40:51,160 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:40:51,160 - INFO -   - 时长差异: 0.05秒 (1.48%)
2025-07-29 10:40:51,160 - INFO - 
----- 处理字幕 #27 的方案 #3 -----
2025-07-29 10:40:51,160 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\27_3.mp4
2025-07-29 10:40:51,160 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxgtuo587
2025-07-29 10:40:51,162 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\206.mp4 (确认存在: True)
2025-07-29 10:40:51,162 - INFO - 添加场景ID=206，时长=3.68秒，累计时长=3.68秒
2025-07-29 10:40:51,162 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\228.mp4 (确认存在: True)
2025-07-29 10:40:51,162 - INFO - 添加场景ID=228，时长=1.76秒，累计时长=5.44秒
2025-07-29 10:40:51,162 - INFO - 场景总时长(5.44秒)已达到音频时长(3.18秒)的1.5倍，停止添加场景
2025-07-29 10:40:51,162 - INFO - 准备合并 2 个场景文件，总时长约 5.44秒
2025-07-29 10:40:51,162 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/206.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/228.mp4'

2025-07-29 10:40:51,162 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxgtuo587\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxgtuo587\temp_combined.mp4
2025-07-29 10:40:51,294 - INFO - 合并后的视频时长: 5.49秒，目标音频时长: 3.18秒
2025-07-29 10:40:51,294 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxgtuo587\temp_combined.mp4 -ss 0 -to 3.176 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\27_3.mp4
2025-07-29 10:40:51,531 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:51,531 - INFO - 目标音频时长: 3.18秒
2025-07-29 10:40:51,531 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:40:51,531 - INFO - 时长差异: 0.05秒 (1.48%)
2025-07-29 10:40:51,531 - INFO - ==========================================
2025-07-29 10:40:51,531 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:51,531 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\27_3.mp4
2025-07-29 10:40:51,532 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxgtuo587
2025-07-29 10:40:51,583 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:51,583 - INFO -   - 音频时长: 3.18秒
2025-07-29 10:40:51,583 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:40:51,583 - INFO -   - 时长差异: 0.05秒 (1.48%)
2025-07-29 10:40:51,583 - INFO - 
字幕 #27 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:51,583 - INFO - 生成的视频文件:
2025-07-29 10:40:51,583 - INFO -   1. F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-29 10:40:51,583 - INFO -   2. F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-07-29 10:40:51,583 - INFO -   3. F:/github/aicut_auto/newcut_ai\27_3.mp4
2025-07-29 10:40:51,584 - INFO - ========== 字幕 #27 处理结束 ==========

