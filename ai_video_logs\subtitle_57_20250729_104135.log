2025-07-29 10:41:35,379 - INFO - ========== 字幕 #57 处理开始 ==========
2025-07-29 10:41:35,380 - INFO - 字幕内容: 此时，宫宴上九姨娘打碎皇后御赐的镯子，面临死罪。
2025-07-29 10:41:35,380 - INFO - 字幕序号: [1217, 1220]
2025-07-29 10:41:35,380 - INFO - 音频文件详情:
2025-07-29 10:41:35,380 - INFO -   - 路径: output\57.wav
2025-07-29 10:41:35,380 - INFO -   - 时长: 4.61秒
2025-07-29 10:41:35,380 - INFO -   - 验证音频时长: 4.61秒
2025-07-29 10:41:35,381 - INFO - 字幕时间戳信息:
2025-07-29 10:41:35,381 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:35,381 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:35,381 - INFO -   - 根据生成的音频时长(4.61秒)已调整字幕时间戳
2025-07-29 10:41:35,381 - INFO - ========== 新模式：为字幕 #57 生成4套场景方案 ==========
2025-07-29 10:41:35,381 - INFO - 字幕序号列表: [1217, 1220]
2025-07-29 10:41:35,381 - INFO - 
--- 生成方案 #1：基于字幕序号 #1217 ---
2025-07-29 10:41:35,381 - INFO - 开始为单个字幕序号 #1217 匹配场景，目标时长: 4.61秒
2025-07-29 10:41:35,382 - INFO - 开始查找字幕序号 [1217] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:35,382 - INFO - 找到related_overlap场景: scene_id=1572, 字幕#1217
2025-07-29 10:41:35,385 - INFO - 字幕 #1217 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:35,385 - INFO - 字幕序号 #1217 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:35,385 - INFO - 选择第一个overlap场景作为起点: scene_id=1572
2025-07-29 10:41:35,385 - INFO - 添加起点场景: scene_id=1572, 时长=5.48秒, 累计时长=5.48秒
2025-07-29 10:41:35,385 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:35,385 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:41:35,385 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:41:35,385 - INFO - 
--- 生成方案 #2：基于字幕序号 #1220 ---
2025-07-29 10:41:35,385 - INFO - 开始为单个字幕序号 #1220 匹配场景，目标时长: 4.61秒
2025-07-29 10:41:35,385 - INFO - 开始查找字幕序号 [1220] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:35,385 - INFO - 找到related_overlap场景: scene_id=1573, 字幕#1220
2025-07-29 10:41:35,387 - INFO - 找到related_between场景: scene_id=1574, 字幕#1220
2025-07-29 10:41:35,387 - INFO - 找到related_between场景: scene_id=1575, 字幕#1220
2025-07-29 10:41:35,388 - INFO - 字幕 #1220 找到 1 个overlap场景, 2 个between场景
2025-07-29 10:41:35,388 - INFO - 字幕序号 #1220 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 10:41:35,388 - INFO - 选择第一个overlap场景作为起点: scene_id=1573
2025-07-29 10:41:35,388 - INFO - 添加起点场景: scene_id=1573, 时长=1.64秒, 累计时长=1.64秒
2025-07-29 10:41:35,388 - INFO - 起点场景时长不足，需要延伸填充 2.97秒
2025-07-29 10:41:35,388 - INFO - 起点场景在原始列表中的索引: 1572
2025-07-29 10:41:35,388 - INFO - 延伸添加场景: scene_id=1574 (完整时长 2.16秒)
2025-07-29 10:41:35,388 - INFO - 累计时长: 3.80秒
2025-07-29 10:41:35,388 - INFO - 延伸添加场景: scene_id=1575 (裁剪至 0.81秒)
2025-07-29 10:41:35,388 - INFO - 累计时长: 4.61秒
2025-07-29 10:41:35,388 - INFO - 字幕序号 #1220 场景匹配完成，共选择 3 个场景，总时长: 4.61秒
2025-07-29 10:41:35,388 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:41:35,388 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:35,388 - INFO - ========== 当前模式：为字幕 #57 生成 1 套场景方案 ==========
2025-07-29 10:41:35,388 - INFO - 开始查找字幕序号 [1217, 1220] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:35,389 - INFO - 找到related_overlap场景: scene_id=1572, 字幕#1217
2025-07-29 10:41:35,389 - INFO - 找到related_overlap场景: scene_id=1573, 字幕#1220
2025-07-29 10:41:35,390 - INFO - 找到related_between场景: scene_id=1574, 字幕#1220
2025-07-29 10:41:35,390 - INFO - 找到related_between场景: scene_id=1575, 字幕#1220
2025-07-29 10:41:35,391 - INFO - 字幕 #1217 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:35,391 - INFO - 字幕 #1220 找到 1 个overlap场景, 2 个between场景
2025-07-29 10:41:35,391 - INFO - 共收集 2 个未使用的overlap场景和 2 个未使用的between场景
2025-07-29 10:41:35,391 - INFO - 开始生成方案 #1
2025-07-29 10:41:35,392 - INFO - 方案 #1: 为字幕#1217选择初始化overlap场景id=1572
2025-07-29 10:41:35,392 - INFO - 方案 #1: 为字幕#1220选择初始化overlap场景id=1573
2025-07-29 10:41:35,392 - INFO - 方案 #1: 初始选择后，当前总时长=7.12秒
2025-07-29 10:41:35,392 - INFO - 方案 #1: 额外between选择后，当前总时长=7.12秒
2025-07-29 10:41:35,392 - INFO - 方案 #1: 场景总时长(7.12秒)大于音频时长(4.61秒)，需要裁剪
2025-07-29 10:41:35,392 - INFO - 调整前总时长: 7.12秒, 目标时长: 4.61秒
2025-07-29 10:41:35,392 - INFO - 需要裁剪 2.51秒
2025-07-29 10:41:35,392 - INFO - 裁剪最长场景ID=1572：从5.48秒裁剪至2.97秒
2025-07-29 10:41:35,392 - INFO - 调整后总时长: 4.61秒，与目标时长差异: 0.00秒
2025-07-29 10:41:35,392 - INFO - 方案 #1 调整/填充后最终总时长: 4.61秒
2025-07-29 10:41:35,392 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:35,392 - INFO - ========== 当前模式：字幕 #57 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:35,392 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:35,392 - INFO - ========== 新模式：字幕 #57 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:35,393 - INFO - 
----- 处理字幕 #57 的方案 #1 -----
2025-07-29 10:41:35,393 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-29 10:41:35,394 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmlo98pcm
2025-07-29 10:41:35,394 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1572.mp4 (确认存在: True)
2025-07-29 10:41:35,394 - INFO - 添加场景ID=1572，时长=5.48秒，累计时长=5.48秒
2025-07-29 10:41:35,394 - INFO - 准备合并 1 个场景文件，总时长约 5.48秒
2025-07-29 10:41:35,395 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1572.mp4'

2025-07-29 10:41:35,395 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmlo98pcm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmlo98pcm\temp_combined.mp4
2025-07-29 10:41:35,548 - INFO - 合并后的视频时长: 5.50秒，目标音频时长: 4.61秒
2025-07-29 10:41:35,548 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmlo98pcm\temp_combined.mp4 -ss 0 -to 4.614 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-29 10:41:35,851 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:35,851 - INFO - 目标音频时长: 4.61秒
2025-07-29 10:41:35,851 - INFO - 实际视频时长: 4.66秒
2025-07-29 10:41:35,851 - INFO - 时长差异: 0.05秒 (1.06%)
2025-07-29 10:41:35,851 - INFO - ==========================================
2025-07-29 10:41:35,851 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:35,851 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-29 10:41:35,852 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmlo98pcm
2025-07-29 10:41:35,918 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:35,918 - INFO -   - 音频时长: 4.61秒
2025-07-29 10:41:35,918 - INFO -   - 视频时长: 4.66秒
2025-07-29 10:41:35,918 - INFO -   - 时长差异: 0.05秒 (1.06%)
2025-07-29 10:41:35,918 - INFO - 
----- 处理字幕 #57 的方案 #2 -----
2025-07-29 10:41:35,919 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-29 10:41:35,919 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwhsuoaje
2025-07-29 10:41:35,920 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1573.mp4 (确认存在: True)
2025-07-29 10:41:35,920 - INFO - 添加场景ID=1573，时长=1.64秒，累计时长=1.64秒
2025-07-29 10:41:35,920 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1574.mp4 (确认存在: True)
2025-07-29 10:41:35,920 - INFO - 添加场景ID=1574，时长=2.16秒，累计时长=3.80秒
2025-07-29 10:41:35,920 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1575.mp4 (确认存在: True)
2025-07-29 10:41:35,920 - INFO - 添加场景ID=1575，时长=1.56秒，累计时长=5.36秒
2025-07-29 10:41:35,920 - INFO - 准备合并 3 个场景文件，总时长约 5.36秒
2025-07-29 10:41:35,920 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1573.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1574.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1575.mp4'

2025-07-29 10:41:35,921 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwhsuoaje\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwhsuoaje\temp_combined.mp4
2025-07-29 10:41:36,086 - INFO - 合并后的视频时长: 5.43秒，目标音频时长: 4.61秒
2025-07-29 10:41:36,086 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwhsuoaje\temp_combined.mp4 -ss 0 -to 4.614 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-29 10:41:36,454 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:36,454 - INFO - 目标音频时长: 4.61秒
2025-07-29 10:41:36,454 - INFO - 实际视频时长: 4.66秒
2025-07-29 10:41:36,454 - INFO - 时长差异: 0.05秒 (1.06%)
2025-07-29 10:41:36,454 - INFO - ==========================================
2025-07-29 10:41:36,454 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:36,454 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-29 10:41:36,455 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwhsuoaje
2025-07-29 10:41:36,521 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:36,521 - INFO -   - 音频时长: 4.61秒
2025-07-29 10:41:36,521 - INFO -   - 视频时长: 4.66秒
2025-07-29 10:41:36,521 - INFO -   - 时长差异: 0.05秒 (1.06%)
2025-07-29 10:41:36,521 - INFO - 
----- 处理字幕 #57 的方案 #3 -----
2025-07-29 10:41:36,521 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-07-29 10:41:36,522 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxjc8ppkr
2025-07-29 10:41:36,522 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1572.mp4 (确认存在: True)
2025-07-29 10:41:36,522 - INFO - 添加场景ID=1572，时长=5.48秒，累计时长=5.48秒
2025-07-29 10:41:36,522 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1573.mp4 (确认存在: True)
2025-07-29 10:41:36,522 - INFO - 添加场景ID=1573，时长=1.64秒，累计时长=7.12秒
2025-07-29 10:41:36,522 - INFO - 场景总时长(7.12秒)已达到音频时长(4.61秒)的1.5倍，停止添加场景
2025-07-29 10:41:36,523 - INFO - 准备合并 2 个场景文件，总时长约 7.12秒
2025-07-29 10:41:36,523 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1572.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1573.mp4'

2025-07-29 10:41:36,523 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxjc8ppkr\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxjc8ppkr\temp_combined.mp4
2025-07-29 10:41:36,680 - INFO - 合并后的视频时长: 7.17秒，目标音频时长: 4.61秒
2025-07-29 10:41:36,680 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxjc8ppkr\temp_combined.mp4 -ss 0 -to 4.614 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-07-29 10:41:37,004 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:37,004 - INFO - 目标音频时长: 4.61秒
2025-07-29 10:41:37,004 - INFO - 实际视频时长: 4.66秒
2025-07-29 10:41:37,004 - INFO - 时长差异: 0.05秒 (1.06%)
2025-07-29 10:41:37,004 - INFO - ==========================================
2025-07-29 10:41:37,004 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:37,004 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-07-29 10:41:37,005 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxjc8ppkr
2025-07-29 10:41:37,069 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:37,069 - INFO -   - 音频时长: 4.61秒
2025-07-29 10:41:37,070 - INFO -   - 视频时长: 4.66秒
2025-07-29 10:41:37,070 - INFO -   - 时长差异: 0.05秒 (1.06%)
2025-07-29 10:41:37,070 - INFO - 
字幕 #57 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:37,070 - INFO - 生成的视频文件:
2025-07-29 10:41:37,070 - INFO -   1. F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-07-29 10:41:37,070 - INFO -   2. F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-07-29 10:41:37,070 - INFO -   3. F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-07-29 10:41:37,070 - INFO - ========== 字幕 #57 处理结束 ==========

