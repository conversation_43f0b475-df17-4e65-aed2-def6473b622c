2025-07-29 10:41:02,354 - INFO - ========== 字幕 #36 处理开始 ==========
2025-07-29 10:41:02,354 - INFO - 字幕内容: 歹人叫嚣不怕，话音未落，摄政王竟真的现身。
2025-07-29 10:41:02,354 - INFO - 字幕序号: [209, 214]
2025-07-29 10:41:02,354 - INFO - 音频文件详情:
2025-07-29 10:41:02,354 - INFO -   - 路径: output\36.wav
2025-07-29 10:41:02,354 - INFO -   - 时长: 3.56秒
2025-07-29 10:41:02,355 - INFO -   - 验证音频时长: 3.56秒
2025-07-29 10:41:02,355 - INFO - 字幕时间戳信息:
2025-07-29 10:41:02,355 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:02,355 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:02,355 - INFO -   - 根据生成的音频时长(3.56秒)已调整字幕时间戳
2025-07-29 10:41:02,355 - INFO - ========== 新模式：为字幕 #36 生成4套场景方案 ==========
2025-07-29 10:41:02,355 - INFO - 字幕序号列表: [209, 214]
2025-07-29 10:41:02,355 - INFO - 
--- 生成方案 #1：基于字幕序号 #209 ---
2025-07-29 10:41:02,355 - INFO - 开始为单个字幕序号 #209 匹配场景，目标时长: 3.56秒
2025-07-29 10:41:02,355 - INFO - 开始查找字幕序号 [209] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:02,355 - INFO - 找到related_overlap场景: scene_id=287, 字幕#209
2025-07-29 10:41:02,356 - INFO - 字幕 #209 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:02,356 - INFO - 字幕序号 #209 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:02,356 - INFO - 选择第一个overlap场景作为起点: scene_id=287
2025-07-29 10:41:02,356 - INFO - 添加起点场景: scene_id=287, 时长=3.40秒, 累计时长=3.40秒
2025-07-29 10:41:02,356 - INFO - 起点场景时长不足，需要延伸填充 0.16秒
2025-07-29 10:41:02,357 - INFO - 起点场景在原始列表中的索引: 286
2025-07-29 10:41:02,357 - INFO - 延伸添加场景: scene_id=288 (裁剪至 0.16秒)
2025-07-29 10:41:02,357 - INFO - 累计时长: 3.56秒
2025-07-29 10:41:02,357 - INFO - 字幕序号 #209 场景匹配完成，共选择 2 个场景，总时长: 3.56秒
2025-07-29 10:41:02,357 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:02,357 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:02,357 - INFO - 
--- 生成方案 #2：基于字幕序号 #214 ---
2025-07-29 10:41:02,357 - INFO - 开始为单个字幕序号 #214 匹配场景，目标时长: 3.56秒
2025-07-29 10:41:02,357 - INFO - 开始查找字幕序号 [214] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:02,357 - INFO - 找到related_overlap场景: scene_id=295, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_overlap场景: scene_id=296, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=290, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=291, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=292, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=293, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=294, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=297, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=298, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=299, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=300, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=301, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=302, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=303, 字幕#214
2025-07-29 10:41:02,357 - INFO - 找到related_between场景: scene_id=304, 字幕#214
2025-07-29 10:41:02,357 - INFO - 字幕 #214 找到 2 个overlap场景, 13 个between场景
2025-07-29 10:41:02,357 - INFO - 字幕序号 #214 找到 2 个可用overlap场景, 13 个可用between场景
2025-07-29 10:41:02,358 - INFO - 选择第一个overlap场景作为起点: scene_id=295
2025-07-29 10:41:02,358 - INFO - 添加起点场景: scene_id=295, 时长=0.52秒, 累计时长=0.52秒
2025-07-29 10:41:02,358 - INFO - 起点场景时长不足，需要延伸填充 3.04秒
2025-07-29 10:41:02,358 - INFO - 起点场景在原始列表中的索引: 294
2025-07-29 10:41:02,358 - INFO - 延伸添加场景: scene_id=296 (完整时长 0.56秒)
2025-07-29 10:41:02,358 - INFO - 累计时长: 1.08秒
2025-07-29 10:41:02,358 - INFO - 延伸添加场景: scene_id=297 (完整时长 0.64秒)
2025-07-29 10:41:02,358 - INFO - 累计时长: 1.72秒
2025-07-29 10:41:02,358 - INFO - 延伸添加场景: scene_id=298 (完整时长 1.76秒)
2025-07-29 10:41:02,358 - INFO - 累计时长: 3.48秒
2025-07-29 10:41:02,358 - INFO - 延伸添加场景: scene_id=299 (裁剪至 0.08秒)
2025-07-29 10:41:02,358 - INFO - 累计时长: 3.56秒
2025-07-29 10:41:02,358 - INFO - 字幕序号 #214 场景匹配完成，共选择 5 个场景，总时长: 3.56秒
2025-07-29 10:41:02,358 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-07-29 10:41:02,358 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:02,358 - INFO - ========== 当前模式：为字幕 #36 生成 1 套场景方案 ==========
2025-07-29 10:41:02,358 - INFO - 开始查找字幕序号 [209, 214] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:02,358 - INFO - 找到related_overlap场景: scene_id=287, 字幕#209
2025-07-29 10:41:02,358 - INFO - 找到related_overlap场景: scene_id=295, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_overlap场景: scene_id=296, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=290, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=291, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=292, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=293, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=294, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=297, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=298, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=299, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=300, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=301, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=302, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=303, 字幕#214
2025-07-29 10:41:02,358 - INFO - 找到related_between场景: scene_id=304, 字幕#214
2025-07-29 10:41:02,358 - INFO - 字幕 #209 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:02,358 - INFO - 字幕 #214 找到 2 个overlap场景, 13 个between场景
2025-07-29 10:41:02,360 - INFO - 共收集 3 个未使用的overlap场景和 13 个未使用的between场景
2025-07-29 10:41:02,360 - INFO - 开始生成方案 #1
2025-07-29 10:41:02,360 - INFO - 方案 #1: 为字幕#209选择初始化overlap场景id=287
2025-07-29 10:41:02,360 - INFO - 方案 #1: 为字幕#214选择初始化overlap场景id=296
2025-07-29 10:41:02,360 - INFO - 方案 #1: 初始选择后，当前总时长=3.96秒
2025-07-29 10:41:02,360 - INFO - 方案 #1: 额外between选择后，当前总时长=3.96秒
2025-07-29 10:41:02,360 - INFO - 方案 #1: 场景总时长(3.96秒)大于音频时长(3.56秒)，需要裁剪
2025-07-29 10:41:02,360 - INFO - 调整前总时长: 3.96秒, 目标时长: 3.56秒
2025-07-29 10:41:02,360 - INFO - 需要裁剪 0.40秒
2025-07-29 10:41:02,360 - INFO - 裁剪最长场景ID=287：从3.40秒裁剪至3.00秒
2025-07-29 10:41:02,360 - INFO - 调整后总时长: 3.56秒，与目标时长差异: 0.00秒
2025-07-29 10:41:02,360 - INFO - 方案 #1 调整/填充后最终总时长: 3.56秒
2025-07-29 10:41:02,360 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:02,360 - INFO - ========== 当前模式：字幕 #36 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:02,360 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:02,360 - INFO - ========== 新模式：字幕 #36 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:02,360 - INFO - 
----- 处理字幕 #36 的方案 #1 -----
2025-07-29 10:41:02,360 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-29 10:41:02,360 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaiouoyrc
2025-07-29 10:41:02,361 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\287.mp4 (确认存在: True)
2025-07-29 10:41:02,361 - INFO - 添加场景ID=287，时长=3.40秒，累计时长=3.40秒
2025-07-29 10:41:02,361 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\288.mp4 (确认存在: True)
2025-07-29 10:41:02,361 - INFO - 添加场景ID=288，时长=0.96秒，累计时长=4.36秒
2025-07-29 10:41:02,361 - INFO - 准备合并 2 个场景文件，总时长约 4.36秒
2025-07-29 10:41:02,361 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/287.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/288.mp4'

2025-07-29 10:41:02,361 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpaiouoyrc\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpaiouoyrc\temp_combined.mp4
2025-07-29 10:41:02,471 - INFO - 合并后的视频时长: 4.41秒，目标音频时长: 3.56秒
2025-07-29 10:41:02,471 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpaiouoyrc\temp_combined.mp4 -ss 0 -to 3.56 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-29 10:41:02,725 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:02,725 - INFO - 目标音频时长: 3.56秒
2025-07-29 10:41:02,725 - INFO - 实际视频时长: 3.58秒
2025-07-29 10:41:02,725 - INFO - 时长差异: 0.02秒 (0.65%)
2025-07-29 10:41:02,725 - INFO - ==========================================
2025-07-29 10:41:02,725 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:02,725 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-29 10:41:02,726 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaiouoyrc
2025-07-29 10:41:02,770 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:02,770 - INFO -   - 音频时长: 3.56秒
2025-07-29 10:41:02,770 - INFO -   - 视频时长: 3.58秒
2025-07-29 10:41:02,770 - INFO -   - 时长差异: 0.02秒 (0.65%)
2025-07-29 10:41:02,770 - INFO - 
----- 处理字幕 #36 的方案 #2 -----
2025-07-29 10:41:02,770 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-29 10:41:02,770 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprrdez8wb
2025-07-29 10:41:02,771 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\295.mp4 (确认存在: True)
2025-07-29 10:41:02,771 - INFO - 添加场景ID=295，时长=0.52秒，累计时长=0.52秒
2025-07-29 10:41:02,771 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\296.mp4 (确认存在: True)
2025-07-29 10:41:02,771 - INFO - 添加场景ID=296，时长=0.56秒，累计时长=1.08秒
2025-07-29 10:41:02,771 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\297.mp4 (确认存在: True)
2025-07-29 10:41:02,771 - INFO - 添加场景ID=297，时长=0.64秒，累计时长=1.72秒
2025-07-29 10:41:02,771 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\298.mp4 (确认存在: True)
2025-07-29 10:41:02,771 - INFO - 添加场景ID=298，时长=1.76秒，累计时长=3.48秒
2025-07-29 10:41:02,771 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\299.mp4 (确认存在: True)
2025-07-29 10:41:02,771 - INFO - 添加场景ID=299，时长=2.76秒，累计时长=6.23秒
2025-07-29 10:41:02,771 - INFO - 场景总时长(6.23秒)已达到音频时长(3.56秒)的1.5倍，停止添加场景
2025-07-29 10:41:02,771 - INFO - 准备合并 5 个场景文件，总时长约 6.23秒
2025-07-29 10:41:02,771 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/295.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/296.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/297.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/298.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/299.mp4'

2025-07-29 10:41:02,772 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmprrdez8wb\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmprrdez8wb\temp_combined.mp4
2025-07-29 10:41:02,927 - INFO - 合并后的视频时长: 6.36秒，目标音频时长: 3.56秒
2025-07-29 10:41:02,927 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmprrdez8wb\temp_combined.mp4 -ss 0 -to 3.56 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-29 10:41:03,228 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:03,228 - INFO - 目标音频时长: 3.56秒
2025-07-29 10:41:03,228 - INFO - 实际视频时长: 3.58秒
2025-07-29 10:41:03,228 - INFO - 时长差异: 0.02秒 (0.65%)
2025-07-29 10:41:03,228 - INFO - ==========================================
2025-07-29 10:41:03,228 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:03,228 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-29 10:41:03,229 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprrdez8wb
2025-07-29 10:41:03,274 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:03,274 - INFO -   - 音频时长: 3.56秒
2025-07-29 10:41:03,274 - INFO -   - 视频时长: 3.58秒
2025-07-29 10:41:03,274 - INFO -   - 时长差异: 0.02秒 (0.65%)
2025-07-29 10:41:03,274 - INFO - 
----- 处理字幕 #36 的方案 #3 -----
2025-07-29 10:41:03,274 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-07-29 10:41:03,274 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpk2mznubz
2025-07-29 10:41:03,274 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\287.mp4 (确认存在: True)
2025-07-29 10:41:03,274 - INFO - 添加场景ID=287，时长=3.40秒，累计时长=3.40秒
2025-07-29 10:41:03,275 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\296.mp4 (确认存在: True)
2025-07-29 10:41:03,275 - INFO - 添加场景ID=296，时长=0.56秒，累计时长=3.96秒
2025-07-29 10:41:03,275 - INFO - 准备合并 2 个场景文件，总时长约 3.96秒
2025-07-29 10:41:03,275 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/287.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/296.mp4'

2025-07-29 10:41:03,275 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpk2mznubz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpk2mznubz\temp_combined.mp4
2025-07-29 10:41:03,396 - INFO - 合并后的视频时长: 4.01秒，目标音频时长: 3.56秒
2025-07-29 10:41:03,396 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpk2mznubz\temp_combined.mp4 -ss 0 -to 3.56 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-07-29 10:41:03,669 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:03,669 - INFO - 目标音频时长: 3.56秒
2025-07-29 10:41:03,669 - INFO - 实际视频时长: 3.58秒
2025-07-29 10:41:03,669 - INFO - 时长差异: 0.02秒 (0.65%)
2025-07-29 10:41:03,669 - INFO - ==========================================
2025-07-29 10:41:03,669 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:03,669 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-07-29 10:41:03,670 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpk2mznubz
2025-07-29 10:41:03,712 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:03,712 - INFO -   - 音频时长: 3.56秒
2025-07-29 10:41:03,712 - INFO -   - 视频时长: 3.58秒
2025-07-29 10:41:03,712 - INFO -   - 时长差异: 0.02秒 (0.65%)
2025-07-29 10:41:03,712 - INFO - 
字幕 #36 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:03,712 - INFO - 生成的视频文件:
2025-07-29 10:41:03,712 - INFO -   1. F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-29 10:41:03,712 - INFO -   2. F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-29 10:41:03,712 - INFO -   3. F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-07-29 10:41:03,712 - INFO - ========== 字幕 #36 处理结束 ==========

