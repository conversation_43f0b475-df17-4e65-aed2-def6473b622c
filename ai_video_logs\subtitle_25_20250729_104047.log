2025-07-29 10:40:47,626 - INFO - ========== 字幕 #25 处理开始 ==========
2025-07-29 10:40:47,626 - INFO - 字幕内容: 他亲自将她带回王府，哥哥赶来时她已安然无恙。
2025-07-29 10:40:47,626 - INFO - 字幕序号: [133, 139]
2025-07-29 10:40:47,626 - INFO - 音频文件详情:
2025-07-29 10:40:47,626 - INFO -   - 路径: output\25.wav
2025-07-29 10:40:47,626 - INFO -   - 时长: 2.89秒
2025-07-29 10:40:47,627 - INFO -   - 验证音频时长: 2.89秒
2025-07-29 10:40:47,627 - INFO - 字幕时间戳信息:
2025-07-29 10:40:47,627 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:47,627 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:47,627 - INFO -   - 根据生成的音频时长(2.89秒)已调整字幕时间戳
2025-07-29 10:40:47,627 - INFO - ========== 新模式：为字幕 #25 生成4套场景方案 ==========
2025-07-29 10:40:47,627 - INFO - 字幕序号列表: [133, 139]
2025-07-29 10:40:47,627 - INFO - 
--- 生成方案 #1：基于字幕序号 #133 ---
2025-07-29 10:40:47,627 - INFO - 开始为单个字幕序号 #133 匹配场景，目标时长: 2.89秒
2025-07-29 10:40:47,627 - INFO - 开始查找字幕序号 [133] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:47,627 - INFO - 找到related_overlap场景: scene_id=192, 字幕#133
2025-07-29 10:40:47,627 - INFO - 找到related_overlap场景: scene_id=193, 字幕#133
2025-07-29 10:40:47,627 - INFO - 找到related_between场景: scene_id=188, 字幕#133
2025-07-29 10:40:47,627 - INFO - 找到related_between场景: scene_id=189, 字幕#133
2025-07-29 10:40:47,627 - INFO - 找到related_between场景: scene_id=190, 字幕#133
2025-07-29 10:40:47,627 - INFO - 找到related_between场景: scene_id=191, 字幕#133
2025-07-29 10:40:47,627 - INFO - 找到related_between场景: scene_id=194, 字幕#133
2025-07-29 10:40:47,627 - INFO - 找到related_between场景: scene_id=195, 字幕#133
2025-07-29 10:40:47,627 - INFO - 找到related_between场景: scene_id=196, 字幕#133
2025-07-29 10:40:47,627 - INFO - 找到related_between场景: scene_id=197, 字幕#133
2025-07-29 10:40:47,628 - INFO - 字幕 #133 找到 2 个overlap场景, 8 个between场景
2025-07-29 10:40:47,628 - INFO - 字幕序号 #133 找到 2 个可用overlap场景, 8 个可用between场景
2025-07-29 10:40:47,628 - INFO - 选择第一个overlap场景作为起点: scene_id=192
2025-07-29 10:40:47,628 - INFO - 添加起点场景: scene_id=192, 时长=2.64秒, 累计时长=2.64秒
2025-07-29 10:40:47,628 - INFO - 起点场景时长不足，需要延伸填充 0.25秒
2025-07-29 10:40:47,628 - INFO - 起点场景在原始列表中的索引: 191
2025-07-29 10:40:47,628 - INFO - 延伸添加场景: scene_id=193 (裁剪至 0.25秒)
2025-07-29 10:40:47,628 - INFO - 累计时长: 2.89秒
2025-07-29 10:40:47,628 - INFO - 字幕序号 #133 场景匹配完成，共选择 2 个场景，总时长: 2.89秒
2025-07-29 10:40:47,628 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:47,628 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:47,628 - INFO - 
--- 生成方案 #2：基于字幕序号 #139 ---
2025-07-29 10:40:47,628 - INFO - 开始为单个字幕序号 #139 匹配场景，目标时长: 2.89秒
2025-07-29 10:40:47,628 - INFO - 开始查找字幕序号 [139] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:47,628 - INFO - 找到related_overlap场景: scene_id=203, 字幕#139
2025-07-29 10:40:47,628 - INFO - 找到related_overlap场景: scene_id=204, 字幕#139
2025-07-29 10:40:47,629 - INFO - 字幕 #139 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:47,629 - INFO - 字幕序号 #139 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:47,629 - INFO - 选择第一个overlap场景作为起点: scene_id=203
2025-07-29 10:40:47,629 - INFO - 添加起点场景: scene_id=203, 时长=1.40秒, 累计时长=1.40秒
2025-07-29 10:40:47,629 - INFO - 起点场景时长不足，需要延伸填充 1.49秒
2025-07-29 10:40:47,629 - INFO - 起点场景在原始列表中的索引: 202
2025-07-29 10:40:47,629 - INFO - 延伸添加场景: scene_id=204 (裁剪至 1.49秒)
2025-07-29 10:40:47,629 - INFO - 累计时长: 2.89秒
2025-07-29 10:40:47,629 - INFO - 字幕序号 #139 场景匹配完成，共选择 2 个场景，总时长: 2.89秒
2025-07-29 10:40:47,630 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:47,630 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:47,630 - INFO - ========== 当前模式：为字幕 #25 生成 1 套场景方案 ==========
2025-07-29 10:40:47,630 - INFO - 开始查找字幕序号 [133, 139] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:47,630 - INFO - 找到related_overlap场景: scene_id=192, 字幕#133
2025-07-29 10:40:47,630 - INFO - 找到related_overlap场景: scene_id=193, 字幕#133
2025-07-29 10:40:47,630 - INFO - 找到related_overlap场景: scene_id=203, 字幕#139
2025-07-29 10:40:47,630 - INFO - 找到related_overlap场景: scene_id=204, 字幕#139
2025-07-29 10:40:47,630 - INFO - 找到related_between场景: scene_id=188, 字幕#133
2025-07-29 10:40:47,630 - INFO - 找到related_between场景: scene_id=189, 字幕#133
2025-07-29 10:40:47,630 - INFO - 找到related_between场景: scene_id=190, 字幕#133
2025-07-29 10:40:47,630 - INFO - 找到related_between场景: scene_id=191, 字幕#133
2025-07-29 10:40:47,630 - INFO - 找到related_between场景: scene_id=194, 字幕#133
2025-07-29 10:40:47,630 - INFO - 找到related_between场景: scene_id=195, 字幕#133
2025-07-29 10:40:47,630 - INFO - 找到related_between场景: scene_id=196, 字幕#133
2025-07-29 10:40:47,630 - INFO - 找到related_between场景: scene_id=197, 字幕#133
2025-07-29 10:40:47,631 - INFO - 字幕 #133 找到 2 个overlap场景, 8 个between场景
2025-07-29 10:40:47,631 - INFO - 字幕 #139 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:47,631 - INFO - 共收集 4 个未使用的overlap场景和 8 个未使用的between场景
2025-07-29 10:40:47,631 - INFO - 开始生成方案 #1
2025-07-29 10:40:47,631 - INFO - 方案 #1: 为字幕#133选择初始化overlap场景id=192
2025-07-29 10:40:47,631 - INFO - 方案 #1: 为字幕#139选择初始化overlap场景id=204
2025-07-29 10:40:47,631 - INFO - 方案 #1: 初始选择后，当前总时长=7.16秒
2025-07-29 10:40:47,631 - INFO - 方案 #1: 额外between选择后，当前总时长=7.16秒
2025-07-29 10:40:47,631 - INFO - 方案 #1: 场景总时长(7.16秒)大于音频时长(2.89秒)，需要裁剪
2025-07-29 10:40:47,631 - INFO - 调整前总时长: 7.16秒, 目标时长: 2.89秒
2025-07-29 10:40:47,631 - INFO - 需要裁剪 4.27秒
2025-07-29 10:40:47,631 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:40:47,631 - INFO - 裁剪场景ID=204：从4.52秒裁剪至1.36秒
2025-07-29 10:40:47,631 - INFO - 裁剪场景ID=192：从2.64秒裁剪至1.54秒
2025-07-29 10:40:47,631 - INFO - 调整后总时长: 2.89秒，与目标时长差异: 0.00秒
2025-07-29 10:40:47,631 - INFO - 方案 #1 调整/填充后最终总时长: 2.89秒
2025-07-29 10:40:47,631 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:47,631 - INFO - ========== 当前模式：字幕 #25 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:47,632 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:47,632 - INFO - ========== 新模式：字幕 #25 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:47,632 - INFO - 
----- 处理字幕 #25 的方案 #1 -----
2025-07-29 10:40:47,632 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-29 10:40:47,632 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw0ljb7gz
2025-07-29 10:40:47,632 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\192.mp4 (确认存在: True)
2025-07-29 10:40:47,632 - INFO - 添加场景ID=192，时长=2.64秒，累计时长=2.64秒
2025-07-29 10:40:47,633 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\193.mp4 (确认存在: True)
2025-07-29 10:40:47,633 - INFO - 添加场景ID=193，时长=1.84秒，累计时长=4.48秒
2025-07-29 10:40:47,633 - INFO - 场景总时长(4.48秒)已达到音频时长(2.89秒)的1.5倍，停止添加场景
2025-07-29 10:40:47,633 - INFO - 准备合并 2 个场景文件，总时长约 4.48秒
2025-07-29 10:40:47,633 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/192.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/193.mp4'

2025-07-29 10:40:47,633 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpw0ljb7gz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpw0ljb7gz\temp_combined.mp4
2025-07-29 10:40:47,751 - INFO - 合并后的视频时长: 4.53秒，目标音频时长: 2.89秒
2025-07-29 10:40:47,751 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpw0ljb7gz\temp_combined.mp4 -ss 0 -to 2.893 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-29 10:40:47,984 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:47,984 - INFO - 目标音频时长: 2.89秒
2025-07-29 10:40:47,984 - INFO - 实际视频时长: 2.94秒
2025-07-29 10:40:47,984 - INFO - 时长差异: 0.05秒 (1.73%)
2025-07-29 10:40:47,984 - INFO - ==========================================
2025-07-29 10:40:47,984 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:47,984 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-29 10:40:47,984 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw0ljb7gz
2025-07-29 10:40:48,026 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:48,026 - INFO -   - 音频时长: 2.89秒
2025-07-29 10:40:48,026 - INFO -   - 视频时长: 2.94秒
2025-07-29 10:40:48,026 - INFO -   - 时长差异: 0.05秒 (1.73%)
2025-07-29 10:40:48,026 - INFO - 
----- 处理字幕 #25 的方案 #2 -----
2025-07-29 10:40:48,026 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-29 10:40:48,027 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp2s4d96_
2025-07-29 10:40:48,027 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\203.mp4 (确认存在: True)
2025-07-29 10:40:48,027 - INFO - 添加场景ID=203，时长=1.40秒，累计时长=1.40秒
2025-07-29 10:40:48,027 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\204.mp4 (确认存在: True)
2025-07-29 10:40:48,027 - INFO - 添加场景ID=204，时长=4.52秒，累计时长=5.92秒
2025-07-29 10:40:48,027 - INFO - 场景总时长(5.92秒)已达到音频时长(2.89秒)的1.5倍，停止添加场景
2025-07-29 10:40:48,027 - INFO - 准备合并 2 个场景文件，总时长约 5.92秒
2025-07-29 10:40:48,027 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/203.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/204.mp4'

2025-07-29 10:40:48,027 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpp2s4d96_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpp2s4d96_\temp_combined.mp4
2025-07-29 10:40:48,161 - INFO - 合并后的视频时长: 5.97秒，目标音频时长: 2.89秒
2025-07-29 10:40:48,161 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpp2s4d96_\temp_combined.mp4 -ss 0 -to 2.893 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-29 10:40:48,420 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:48,421 - INFO - 目标音频时长: 2.89秒
2025-07-29 10:40:48,421 - INFO - 实际视频时长: 2.94秒
2025-07-29 10:40:48,421 - INFO - 时长差异: 0.05秒 (1.73%)
2025-07-29 10:40:48,421 - INFO - ==========================================
2025-07-29 10:40:48,421 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:48,421 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-29 10:40:48,421 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp2s4d96_
2025-07-29 10:40:48,465 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:48,465 - INFO -   - 音频时长: 2.89秒
2025-07-29 10:40:48,465 - INFO -   - 视频时长: 2.94秒
2025-07-29 10:40:48,465 - INFO -   - 时长差异: 0.05秒 (1.73%)
2025-07-29 10:40:48,465 - INFO - 
----- 处理字幕 #25 的方案 #3 -----
2025-07-29 10:40:48,465 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-07-29 10:40:48,466 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5e3fc4bx
2025-07-29 10:40:48,466 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\192.mp4 (确认存在: True)
2025-07-29 10:40:48,466 - INFO - 添加场景ID=192，时长=2.64秒，累计时长=2.64秒
2025-07-29 10:40:48,466 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\204.mp4 (确认存在: True)
2025-07-29 10:40:48,466 - INFO - 添加场景ID=204，时长=4.52秒，累计时长=7.16秒
2025-07-29 10:40:48,467 - INFO - 场景总时长(7.16秒)已达到音频时长(2.89秒)的1.5倍，停止添加场景
2025-07-29 10:40:48,467 - INFO - 准备合并 2 个场景文件，总时长约 7.16秒
2025-07-29 10:40:48,467 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/192.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/204.mp4'

2025-07-29 10:40:48,467 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5e3fc4bx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5e3fc4bx\temp_combined.mp4
2025-07-29 10:40:48,593 - INFO - 合并后的视频时长: 7.21秒，目标音频时长: 2.89秒
2025-07-29 10:40:48,593 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5e3fc4bx\temp_combined.mp4 -ss 0 -to 2.893 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-07-29 10:40:48,837 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:48,837 - INFO - 目标音频时长: 2.89秒
2025-07-29 10:40:48,837 - INFO - 实际视频时长: 2.94秒
2025-07-29 10:40:48,837 - INFO - 时长差异: 0.05秒 (1.73%)
2025-07-29 10:40:48,837 - INFO - ==========================================
2025-07-29 10:40:48,837 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:48,837 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-07-29 10:40:48,837 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5e3fc4bx
2025-07-29 10:40:48,881 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:48,881 - INFO -   - 音频时长: 2.89秒
2025-07-29 10:40:48,881 - INFO -   - 视频时长: 2.94秒
2025-07-29 10:40:48,881 - INFO -   - 时长差异: 0.05秒 (1.73%)
2025-07-29 10:40:48,882 - INFO - 
字幕 #25 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:48,882 - INFO - 生成的视频文件:
2025-07-29 10:40:48,882 - INFO -   1. F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-29 10:40:48,882 - INFO -   2. F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-29 10:40:48,882 - INFO -   3. F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-07-29 10:40:48,882 - INFO - ========== 字幕 #25 处理结束 ==========

