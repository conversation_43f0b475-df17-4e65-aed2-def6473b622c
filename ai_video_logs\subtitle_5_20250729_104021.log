2025-07-29 10:40:21,434 - INFO - ========== 字幕 #5 处理开始 ==========
2025-07-29 10:40:21,434 - INFO - 字幕内容: 丫鬟解释，九姨娘的小字也叫“月”，与她同名。
2025-07-29 10:40:21,434 - INFO - 字幕序号: [16, 20]
2025-07-29 10:40:21,434 - INFO - 音频文件详情:
2025-07-29 10:40:21,434 - INFO -   - 路径: output\5.wav
2025-07-29 10:40:21,434 - INFO -   - 时长: 3.63秒
2025-07-29 10:40:21,434 - INFO -   - 验证音频时长: 3.63秒
2025-07-29 10:40:21,434 - INFO - 字幕时间戳信息:
2025-07-29 10:40:21,434 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:21,435 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:21,435 - INFO -   - 根据生成的音频时长(3.63秒)已调整字幕时间戳
2025-07-29 10:40:21,435 - INFO - ========== 新模式：为字幕 #5 生成4套场景方案 ==========
2025-07-29 10:40:21,435 - INFO - 字幕序号列表: [16, 20]
2025-07-29 10:40:21,435 - INFO - 
--- 生成方案 #1：基于字幕序号 #16 ---
2025-07-29 10:40:21,435 - INFO - 开始为单个字幕序号 #16 匹配场景，目标时长: 3.63秒
2025-07-29 10:40:21,435 - INFO - 开始查找字幕序号 [16] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:21,435 - INFO - 找到related_overlap场景: scene_id=29, 字幕#16
2025-07-29 10:40:21,436 - INFO - 字幕 #16 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:21,436 - INFO - 字幕序号 #16 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:21,436 - INFO - 选择第一个overlap场景作为起点: scene_id=29
2025-07-29 10:40:21,436 - INFO - 添加起点场景: scene_id=29, 时长=2.12秒, 累计时长=2.12秒
2025-07-29 10:40:21,436 - INFO - 起点场景时长不足，需要延伸填充 1.51秒
2025-07-29 10:40:21,436 - INFO - 起点场景在原始列表中的索引: 28
2025-07-29 10:40:21,436 - INFO - 延伸添加场景: scene_id=30 (裁剪至 1.51秒)
2025-07-29 10:40:21,436 - INFO - 累计时长: 3.63秒
2025-07-29 10:40:21,436 - INFO - 字幕序号 #16 场景匹配完成，共选择 2 个场景，总时长: 3.63秒
2025-07-29 10:40:21,436 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:21,436 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:21,436 - INFO - 
--- 生成方案 #2：基于字幕序号 #20 ---
2025-07-29 10:40:21,436 - INFO - 开始为单个字幕序号 #20 匹配场景，目标时长: 3.63秒
2025-07-29 10:40:21,436 - INFO - 开始查找字幕序号 [20] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:21,436 - INFO - 找到related_overlap场景: scene_id=34, 字幕#20
2025-07-29 10:40:21,436 - INFO - 找到related_overlap场景: scene_id=35, 字幕#20
2025-07-29 10:40:21,437 - INFO - 找到related_between场景: scene_id=33, 字幕#20
2025-07-29 10:40:21,437 - INFO - 字幕 #20 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:21,437 - INFO - 字幕序号 #20 找到 2 个可用overlap场景, 1 个可用between场景
2025-07-29 10:40:21,437 - INFO - 选择第一个overlap场景作为起点: scene_id=34
2025-07-29 10:40:21,437 - INFO - 添加起点场景: scene_id=34, 时长=2.24秒, 累计时长=2.24秒
2025-07-29 10:40:21,437 - INFO - 起点场景时长不足，需要延伸填充 1.39秒
2025-07-29 10:40:21,437 - INFO - 起点场景在原始列表中的索引: 33
2025-07-29 10:40:21,437 - INFO - 延伸添加场景: scene_id=35 (裁剪至 1.39秒)
2025-07-29 10:40:21,437 - INFO - 累计时长: 3.63秒
2025-07-29 10:40:21,437 - INFO - 字幕序号 #20 场景匹配完成，共选择 2 个场景，总时长: 3.63秒
2025-07-29 10:40:21,437 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:21,437 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:21,437 - INFO - ========== 当前模式：为字幕 #5 生成 1 套场景方案 ==========
2025-07-29 10:40:21,437 - INFO - 开始查找字幕序号 [16, 20] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:21,437 - INFO - 找到related_overlap场景: scene_id=29, 字幕#16
2025-07-29 10:40:21,438 - INFO - 找到related_overlap场景: scene_id=34, 字幕#20
2025-07-29 10:40:21,438 - INFO - 找到related_overlap场景: scene_id=35, 字幕#20
2025-07-29 10:40:21,438 - INFO - 找到related_between场景: scene_id=33, 字幕#20
2025-07-29 10:40:21,439 - INFO - 字幕 #16 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:21,439 - INFO - 字幕 #20 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:21,439 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 10:40:21,439 - INFO - 开始生成方案 #1
2025-07-29 10:40:21,439 - INFO - 方案 #1: 为字幕#16选择初始化overlap场景id=29
2025-07-29 10:40:21,439 - INFO - 方案 #1: 为字幕#20选择初始化overlap场景id=35
2025-07-29 10:40:21,439 - INFO - 方案 #1: 初始选择后，当前总时长=4.48秒
2025-07-29 10:40:21,439 - INFO - 方案 #1: 额外between选择后，当前总时长=4.48秒
2025-07-29 10:40:21,439 - INFO - 方案 #1: 场景总时长(4.48秒)大于音频时长(3.63秒)，需要裁剪
2025-07-29 10:40:21,439 - INFO - 调整前总时长: 4.48秒, 目标时长: 3.63秒
2025-07-29 10:40:21,439 - INFO - 需要裁剪 0.85秒
2025-07-29 10:40:21,439 - INFO - 裁剪最长场景ID=35：从2.36秒裁剪至1.51秒
2025-07-29 10:40:21,439 - INFO - 调整后总时长: 3.63秒，与目标时长差异: 0.00秒
2025-07-29 10:40:21,439 - INFO - 方案 #1 调整/填充后最终总时长: 3.63秒
2025-07-29 10:40:21,439 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:21,439 - INFO - ========== 当前模式：字幕 #5 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:21,439 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:21,439 - INFO - ========== 新模式：字幕 #5 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:21,439 - INFO - 
----- 处理字幕 #5 的方案 #1 -----
2025-07-29 10:40:21,439 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-29 10:40:21,439 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt71372gg
2025-07-29 10:40:21,440 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\29.mp4 (确认存在: True)
2025-07-29 10:40:21,440 - INFO - 添加场景ID=29，时长=2.12秒，累计时长=2.12秒
2025-07-29 10:40:21,440 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\30.mp4 (确认存在: True)
2025-07-29 10:40:21,440 - INFO - 添加场景ID=30，时长=2.40秒，累计时长=4.52秒
2025-07-29 10:40:21,440 - INFO - 准备合并 2 个场景文件，总时长约 4.52秒
2025-07-29 10:40:21,440 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/29.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/30.mp4'

2025-07-29 10:40:21,440 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpt71372gg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpt71372gg\temp_combined.mp4
2025-07-29 10:40:21,553 - INFO - 合并后的视频时长: 4.57秒，目标音频时长: 3.63秒
2025-07-29 10:40:21,553 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpt71372gg\temp_combined.mp4 -ss 0 -to 3.631 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-29 10:40:21,810 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:21,810 - INFO - 目标音频时长: 3.63秒
2025-07-29 10:40:21,810 - INFO - 实际视频时长: 3.66秒
2025-07-29 10:40:21,810 - INFO - 时长差异: 0.03秒 (0.88%)
2025-07-29 10:40:21,810 - INFO - ==========================================
2025-07-29 10:40:21,810 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:21,810 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-29 10:40:21,810 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt71372gg
2025-07-29 10:40:21,852 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:21,852 - INFO -   - 音频时长: 3.63秒
2025-07-29 10:40:21,852 - INFO -   - 视频时长: 3.66秒
2025-07-29 10:40:21,852 - INFO -   - 时长差异: 0.03秒 (0.88%)
2025-07-29 10:40:21,852 - INFO - 
----- 处理字幕 #5 的方案 #2 -----
2025-07-29 10:40:21,852 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-07-29 10:40:21,852 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpoile98jq
2025-07-29 10:40:21,853 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\34.mp4 (确认存在: True)
2025-07-29 10:40:21,853 - INFO - 添加场景ID=34，时长=2.24秒，累计时长=2.24秒
2025-07-29 10:40:21,853 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\35.mp4 (确认存在: True)
2025-07-29 10:40:21,853 - INFO - 添加场景ID=35，时长=2.36秒，累计时长=4.60秒
2025-07-29 10:40:21,853 - INFO - 准备合并 2 个场景文件，总时长约 4.60秒
2025-07-29 10:40:21,853 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/34.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/35.mp4'

2025-07-29 10:40:21,853 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpoile98jq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpoile98jq\temp_combined.mp4
2025-07-29 10:40:21,977 - INFO - 合并后的视频时长: 4.65秒，目标音频时长: 3.63秒
2025-07-29 10:40:21,977 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpoile98jq\temp_combined.mp4 -ss 0 -to 3.631 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-07-29 10:40:22,224 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:22,224 - INFO - 目标音频时长: 3.63秒
2025-07-29 10:40:22,224 - INFO - 实际视频时长: 3.66秒
2025-07-29 10:40:22,224 - INFO - 时长差异: 0.03秒 (0.88%)
2025-07-29 10:40:22,224 - INFO - ==========================================
2025-07-29 10:40:22,224 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:22,224 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-07-29 10:40:22,225 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpoile98jq
2025-07-29 10:40:22,268 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:22,268 - INFO -   - 音频时长: 3.63秒
2025-07-29 10:40:22,268 - INFO -   - 视频时长: 3.66秒
2025-07-29 10:40:22,268 - INFO -   - 时长差异: 0.03秒 (0.88%)
2025-07-29 10:40:22,268 - INFO - 
----- 处理字幕 #5 的方案 #3 -----
2025-07-29 10:40:22,268 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-07-29 10:40:22,268 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcvs369_4
2025-07-29 10:40:22,268 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\29.mp4 (确认存在: True)
2025-07-29 10:40:22,268 - INFO - 添加场景ID=29，时长=2.12秒，累计时长=2.12秒
2025-07-29 10:40:22,269 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\35.mp4 (确认存在: True)
2025-07-29 10:40:22,269 - INFO - 添加场景ID=35，时长=2.36秒，累计时长=4.48秒
2025-07-29 10:40:22,269 - INFO - 准备合并 2 个场景文件，总时长约 4.48秒
2025-07-29 10:40:22,269 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/29.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/35.mp4'

2025-07-29 10:40:22,269 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcvs369_4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcvs369_4\temp_combined.mp4
2025-07-29 10:40:22,378 - INFO - 合并后的视频时长: 4.53秒，目标音频时长: 3.63秒
2025-07-29 10:40:22,378 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcvs369_4\temp_combined.mp4 -ss 0 -to 3.631 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-07-29 10:40:22,693 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:22,693 - INFO - 目标音频时长: 3.63秒
2025-07-29 10:40:22,693 - INFO - 实际视频时长: 3.66秒
2025-07-29 10:40:22,693 - INFO - 时长差异: 0.03秒 (0.88%)
2025-07-29 10:40:22,693 - INFO - ==========================================
2025-07-29 10:40:22,693 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:22,693 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-07-29 10:40:22,694 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcvs369_4
2025-07-29 10:40:22,736 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:22,736 - INFO -   - 音频时长: 3.63秒
2025-07-29 10:40:22,736 - INFO -   - 视频时长: 3.66秒
2025-07-29 10:40:22,736 - INFO -   - 时长差异: 0.03秒 (0.88%)
2025-07-29 10:40:22,736 - INFO - 
字幕 #5 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:22,736 - INFO - 生成的视频文件:
2025-07-29 10:40:22,736 - INFO -   1. F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-29 10:40:22,736 - INFO -   2. F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-07-29 10:40:22,736 - INFO -   3. F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-07-29 10:40:22,736 - INFO - ========== 字幕 #5 处理结束 ==========

