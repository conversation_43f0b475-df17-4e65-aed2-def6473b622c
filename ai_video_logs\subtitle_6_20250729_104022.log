2025-07-29 10:40:22,737 - INFO - ========== 字幕 #6 处理开始 ==========
2025-07-29 10:40:22,737 - INFO - 字幕内容: 她是萧府养女，爱慕哥哥多年，却只能看他不停纳妾。
2025-07-29 10:40:22,737 - INFO - 字幕序号: [21, 24]
2025-07-29 10:40:22,737 - INFO - 音频文件详情:
2025-07-29 10:40:22,737 - INFO -   - 路径: output\6.wav
2025-07-29 10:40:22,737 - INFO -   - 时长: 5.63秒
2025-07-29 10:40:22,737 - INFO -   - 验证音频时长: 5.63秒
2025-07-29 10:40:22,738 - INFO - 字幕时间戳信息:
2025-07-29 10:40:22,738 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:22,738 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:22,738 - INFO -   - 根据生成的音频时长(5.63秒)已调整字幕时间戳
2025-07-29 10:40:22,738 - INFO - ========== 新模式：为字幕 #6 生成4套场景方案 ==========
2025-07-29 10:40:22,738 - INFO - 字幕序号列表: [21, 24]
2025-07-29 10:40:22,738 - INFO - 
--- 生成方案 #1：基于字幕序号 #21 ---
2025-07-29 10:40:22,738 - INFO - 开始为单个字幕序号 #21 匹配场景，目标时长: 5.63秒
2025-07-29 10:40:22,738 - INFO - 开始查找字幕序号 [21] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:22,738 - INFO - 找到related_overlap场景: scene_id=35, 字幕#21
2025-07-29 10:40:22,739 - INFO - 字幕 #21 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:22,739 - INFO - 字幕序号 #21 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:22,739 - INFO - 选择第一个overlap场景作为起点: scene_id=35
2025-07-29 10:40:22,739 - INFO - 添加起点场景: scene_id=35, 时长=2.36秒, 累计时长=2.36秒
2025-07-29 10:40:22,739 - INFO - 起点场景时长不足，需要延伸填充 3.28秒
2025-07-29 10:40:22,739 - INFO - 起点场景在原始列表中的索引: 34
2025-07-29 10:40:22,739 - INFO - 延伸添加场景: scene_id=36 (完整时长 2.32秒)
2025-07-29 10:40:22,739 - INFO - 累计时长: 4.68秒
2025-07-29 10:40:22,739 - INFO - 延伸添加场景: scene_id=37 (裁剪至 0.96秒)
2025-07-29 10:40:22,739 - INFO - 累计时长: 5.63秒
2025-07-29 10:40:22,739 - INFO - 字幕序号 #21 场景匹配完成，共选择 3 个场景，总时长: 5.63秒
2025-07-29 10:40:22,739 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:40:22,739 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:40:22,739 - INFO - 
--- 生成方案 #2：基于字幕序号 #24 ---
2025-07-29 10:40:22,739 - INFO - 开始为单个字幕序号 #24 匹配场景，目标时长: 5.63秒
2025-07-29 10:40:22,739 - INFO - 开始查找字幕序号 [24] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:22,739 - INFO - 找到related_overlap场景: scene_id=37, 字幕#24
2025-07-29 10:40:22,740 - INFO - 找到related_between场景: scene_id=38, 字幕#24
2025-07-29 10:40:22,740 - INFO - 字幕 #24 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:40:22,740 - INFO - 字幕序号 #24 找到 0 个可用overlap场景, 1 个可用between场景
2025-07-29 10:40:22,740 - INFO - 没有overlap场景，选择第一个between场景作为起点: scene_id=38
2025-07-29 10:40:22,740 - INFO - 添加起点场景: scene_id=38, 时长=2.48秒, 累计时长=2.48秒
2025-07-29 10:40:22,740 - INFO - 起点场景时长不足，需要延伸填充 3.15秒
2025-07-29 10:40:22,740 - INFO - 起点场景在原始列表中的索引: 37
2025-07-29 10:40:22,740 - INFO - 延伸添加场景: scene_id=39 (完整时长 3.12秒)
2025-07-29 10:40:22,740 - INFO - 累计时长: 5.60秒
2025-07-29 10:40:22,740 - INFO - 延伸添加场景: scene_id=40 (裁剪至 0.04秒)
2025-07-29 10:40:22,740 - INFO - 累计时长: 5.63秒
2025-07-29 10:40:22,740 - INFO - 字幕序号 #24 场景匹配完成，共选择 3 个场景，总时长: 5.63秒
2025-07-29 10:40:22,740 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:22,740 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:22,740 - INFO - ========== 当前模式：为字幕 #6 生成 1 套场景方案 ==========
2025-07-29 10:40:22,740 - INFO - 开始查找字幕序号 [21, 24] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:22,740 - INFO - 找到related_overlap场景: scene_id=35, 字幕#21
2025-07-29 10:40:22,740 - INFO - 找到related_overlap场景: scene_id=37, 字幕#24
2025-07-29 10:40:22,741 - INFO - 找到related_between场景: scene_id=38, 字幕#24
2025-07-29 10:40:22,741 - INFO - 字幕 #21 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:22,741 - INFO - 字幕 #24 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:40:22,741 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 10:40:22,741 - INFO - 开始生成方案 #1
2025-07-29 10:40:22,741 - INFO - 方案 #1: 为字幕#21选择初始化overlap场景id=35
2025-07-29 10:40:22,741 - INFO - 方案 #1: 为字幕#24选择初始化overlap场景id=37
2025-07-29 10:40:22,741 - INFO - 方案 #1: 初始选择后，当前总时长=4.80秒
2025-07-29 10:40:22,741 - INFO - 方案 #1: 额外between选择后，当前总时长=4.80秒
2025-07-29 10:40:22,741 - INFO - 方案 #1: 额外添加between场景id=38, 当前总时长=7.28秒
2025-07-29 10:40:22,742 - INFO - 方案 #1: 场景总时长(7.28秒)大于音频时长(5.63秒)，需要裁剪
2025-07-29 10:40:22,742 - INFO - 调整前总时长: 7.28秒, 目标时长: 5.63秒
2025-07-29 10:40:22,742 - INFO - 需要裁剪 1.64秒
2025-07-29 10:40:22,742 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:40:22,742 - INFO - 裁剪场景ID=38：从2.48秒裁剪至1.00秒
2025-07-29 10:40:22,742 - INFO - 裁剪场景ID=37：从2.44秒裁剪至2.28秒
2025-07-29 10:40:22,742 - INFO - 调整后总时长: 5.63秒，与目标时长差异: 0.00秒
2025-07-29 10:40:22,742 - INFO - 方案 #1 调整/填充后最终总时长: 5.63秒
2025-07-29 10:40:22,742 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:22,742 - INFO - ========== 当前模式：字幕 #6 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:22,742 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:22,742 - INFO - ========== 新模式：字幕 #6 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:22,742 - INFO - 
----- 处理字幕 #6 的方案 #1 -----
2025-07-29 10:40:22,742 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-29 10:40:22,742 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqtk2wstu
2025-07-29 10:40:22,743 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\35.mp4 (确认存在: True)
2025-07-29 10:40:22,743 - INFO - 添加场景ID=35，时长=2.36秒，累计时长=2.36秒
2025-07-29 10:40:22,743 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\36.mp4 (确认存在: True)
2025-07-29 10:40:22,743 - INFO - 添加场景ID=36，时长=2.32秒，累计时长=4.68秒
2025-07-29 10:40:22,743 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\37.mp4 (确认存在: True)
2025-07-29 10:40:22,743 - INFO - 添加场景ID=37，时长=2.44秒，累计时长=7.12秒
2025-07-29 10:40:22,743 - INFO - 准备合并 3 个场景文件，总时长约 7.12秒
2025-07-29 10:40:22,743 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/35.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/36.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/37.mp4'

2025-07-29 10:40:22,743 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqtk2wstu\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqtk2wstu\temp_combined.mp4
2025-07-29 10:40:22,863 - INFO - 合并后的视频时长: 7.19秒，目标音频时长: 5.63秒
2025-07-29 10:40:22,863 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqtk2wstu\temp_combined.mp4 -ss 0 -to 5.635 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-29 10:40:23,208 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:23,208 - INFO - 目标音频时长: 5.63秒
2025-07-29 10:40:23,208 - INFO - 实际视频时长: 5.66秒
2025-07-29 10:40:23,208 - INFO - 时长差异: 0.03秒 (0.50%)
2025-07-29 10:40:23,208 - INFO - ==========================================
2025-07-29 10:40:23,208 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:23,208 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-29 10:40:23,208 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqtk2wstu
2025-07-29 10:40:23,251 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:23,251 - INFO -   - 音频时长: 5.63秒
2025-07-29 10:40:23,251 - INFO -   - 视频时长: 5.66秒
2025-07-29 10:40:23,251 - INFO -   - 时长差异: 0.03秒 (0.50%)
2025-07-29 10:40:23,251 - INFO - 
----- 处理字幕 #6 的方案 #2 -----
2025-07-29 10:40:23,251 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-07-29 10:40:23,251 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgjkqgm8o
2025-07-29 10:40:23,252 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\38.mp4 (确认存在: True)
2025-07-29 10:40:23,252 - INFO - 添加场景ID=38，时长=2.48秒，累计时长=2.48秒
2025-07-29 10:40:23,252 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\39.mp4 (确认存在: True)
2025-07-29 10:40:23,252 - INFO - 添加场景ID=39，时长=3.12秒，累计时长=5.60秒
2025-07-29 10:40:23,252 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\40.mp4 (确认存在: True)
2025-07-29 10:40:23,252 - INFO - 添加场景ID=40，时长=3.08秒，累计时长=8.68秒
2025-07-29 10:40:23,252 - INFO - 场景总时长(8.68秒)已达到音频时长(5.63秒)的1.5倍，停止添加场景
2025-07-29 10:40:23,252 - INFO - 准备合并 3 个场景文件，总时长约 8.68秒
2025-07-29 10:40:23,252 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/38.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/39.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/40.mp4'

2025-07-29 10:40:23,252 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpgjkqgm8o\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpgjkqgm8o\temp_combined.mp4
2025-07-29 10:40:23,371 - INFO - 合并后的视频时长: 8.75秒，目标音频时长: 5.63秒
2025-07-29 10:40:23,371 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpgjkqgm8o\temp_combined.mp4 -ss 0 -to 5.635 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-07-29 10:40:23,726 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:23,726 - INFO - 目标音频时长: 5.63秒
2025-07-29 10:40:23,727 - INFO - 实际视频时长: 5.66秒
2025-07-29 10:40:23,727 - INFO - 时长差异: 0.03秒 (0.50%)
2025-07-29 10:40:23,727 - INFO - ==========================================
2025-07-29 10:40:23,727 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:23,727 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-07-29 10:40:23,727 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgjkqgm8o
2025-07-29 10:40:23,773 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:23,773 - INFO -   - 音频时长: 5.63秒
2025-07-29 10:40:23,773 - INFO -   - 视频时长: 5.66秒
2025-07-29 10:40:23,773 - INFO -   - 时长差异: 0.03秒 (0.50%)
2025-07-29 10:40:23,773 - INFO - 
----- 处理字幕 #6 的方案 #3 -----
2025-07-29 10:40:23,773 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\6_3.mp4
2025-07-29 10:40:23,774 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp97n4vzbt
2025-07-29 10:40:23,774 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\35.mp4 (确认存在: True)
2025-07-29 10:40:23,774 - INFO - 添加场景ID=35，时长=2.36秒，累计时长=2.36秒
2025-07-29 10:40:23,774 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\37.mp4 (确认存在: True)
2025-07-29 10:40:23,774 - INFO - 添加场景ID=37，时长=2.44秒，累计时长=4.80秒
2025-07-29 10:40:23,774 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\38.mp4 (确认存在: True)
2025-07-29 10:40:23,774 - INFO - 添加场景ID=38，时长=2.48秒，累计时长=7.28秒
2025-07-29 10:40:23,774 - INFO - 准备合并 3 个场景文件，总时长约 7.28秒
2025-07-29 10:40:23,774 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/35.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/37.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/38.mp4'

2025-07-29 10:40:23,775 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp97n4vzbt\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp97n4vzbt\temp_combined.mp4
2025-07-29 10:40:23,899 - INFO - 合并后的视频时长: 7.35秒，目标音频时长: 5.63秒
2025-07-29 10:40:23,899 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp97n4vzbt\temp_combined.mp4 -ss 0 -to 5.635 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\6_3.mp4
2025-07-29 10:40:24,243 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:24,243 - INFO - 目标音频时长: 5.63秒
2025-07-29 10:40:24,243 - INFO - 实际视频时长: 5.66秒
2025-07-29 10:40:24,243 - INFO - 时长差异: 0.03秒 (0.50%)
2025-07-29 10:40:24,243 - INFO - ==========================================
2025-07-29 10:40:24,243 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:24,243 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\6_3.mp4
2025-07-29 10:40:24,244 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp97n4vzbt
2025-07-29 10:40:24,288 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:24,288 - INFO -   - 音频时长: 5.63秒
2025-07-29 10:40:24,288 - INFO -   - 视频时长: 5.66秒
2025-07-29 10:40:24,288 - INFO -   - 时长差异: 0.03秒 (0.50%)
2025-07-29 10:40:24,288 - INFO - 
字幕 #6 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:24,288 - INFO - 生成的视频文件:
2025-07-29 10:40:24,288 - INFO -   1. F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-29 10:40:24,288 - INFO -   2. F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-07-29 10:40:24,288 - INFO -   3. F:/github/aicut_auto/newcut_ai\6_3.mp4
2025-07-29 10:40:24,288 - INFO - ========== 字幕 #6 处理结束 ==========

