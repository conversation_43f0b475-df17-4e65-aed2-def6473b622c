2025-07-29 10:41:21,437 - INFO - ========== 字幕 #49 处理开始 ==========
2025-07-29 10:41:21,437 - INFO - 字幕内容: 女人将计就计，诈出九姨娘就是幕后黑手。
2025-07-29 10:41:21,437 - INFO - 字幕序号: [282, 285]
2025-07-29 10:41:21,438 - INFO - 音频文件详情:
2025-07-29 10:41:21,438 - INFO -   - 路径: output\49.wav
2025-07-29 10:41:21,438 - INFO -   - 时长: 3.87秒
2025-07-29 10:41:21,438 - INFO -   - 验证音频时长: 3.87秒
2025-07-29 10:41:21,438 - INFO - 字幕时间戳信息:
2025-07-29 10:41:21,438 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:21,438 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:21,438 - INFO -   - 根据生成的音频时长(3.87秒)已调整字幕时间戳
2025-07-29 10:41:21,438 - INFO - ========== 新模式：为字幕 #49 生成4套场景方案 ==========
2025-07-29 10:41:21,438 - INFO - 字幕序号列表: [282, 285]
2025-07-29 10:41:21,438 - INFO - 
--- 生成方案 #1：基于字幕序号 #282 ---
2025-07-29 10:41:21,438 - INFO - 开始为单个字幕序号 #282 匹配场景，目标时长: 3.87秒
2025-07-29 10:41:21,438 - INFO - 开始查找字幕序号 [282] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:21,438 - INFO - 找到related_overlap场景: scene_id=418, 字幕#282
2025-07-29 10:41:21,439 - INFO - 字幕 #282 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:21,439 - INFO - 字幕序号 #282 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:21,439 - INFO - 选择第一个overlap场景作为起点: scene_id=418
2025-07-29 10:41:21,439 - INFO - 添加起点场景: scene_id=418, 时长=1.08秒, 累计时长=1.08秒
2025-07-29 10:41:21,439 - INFO - 起点场景时长不足，需要延伸填充 2.79秒
2025-07-29 10:41:21,439 - INFO - 起点场景在原始列表中的索引: 417
2025-07-29 10:41:21,439 - INFO - 延伸添加场景: scene_id=419 (完整时长 2.08秒)
2025-07-29 10:41:21,439 - INFO - 累计时长: 3.16秒
2025-07-29 10:41:21,439 - INFO - 延伸添加场景: scene_id=420 (裁剪至 0.71秒)
2025-07-29 10:41:21,439 - INFO - 累计时长: 3.87秒
2025-07-29 10:41:21,439 - INFO - 字幕序号 #282 场景匹配完成，共选择 3 个场景，总时长: 3.87秒
2025-07-29 10:41:21,439 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:21,439 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:21,439 - INFO - 
--- 生成方案 #2：基于字幕序号 #285 ---
2025-07-29 10:41:21,439 - INFO - 开始为单个字幕序号 #285 匹配场景，目标时长: 3.87秒
2025-07-29 10:41:21,439 - INFO - 开始查找字幕序号 [285] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:21,439 - INFO - 找到related_overlap场景: scene_id=421, 字幕#285
2025-07-29 10:41:21,439 - INFO - 找到related_overlap场景: scene_id=422, 字幕#285
2025-07-29 10:41:21,442 - INFO - 字幕 #285 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:21,442 - INFO - 字幕序号 #285 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:21,442 - INFO - 选择第一个overlap场景作为起点: scene_id=421
2025-07-29 10:41:21,442 - INFO - 添加起点场景: scene_id=421, 时长=2.48秒, 累计时长=2.48秒
2025-07-29 10:41:21,442 - INFO - 起点场景时长不足，需要延伸填充 1.39秒
2025-07-29 10:41:21,442 - INFO - 起点场景在原始列表中的索引: 420
2025-07-29 10:41:21,442 - INFO - 延伸添加场景: scene_id=422 (裁剪至 1.39秒)
2025-07-29 10:41:21,442 - INFO - 累计时长: 3.87秒
2025-07-29 10:41:21,442 - INFO - 字幕序号 #285 场景匹配完成，共选择 2 个场景，总时长: 3.87秒
2025-07-29 10:41:21,442 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:21,442 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:21,442 - INFO - ========== 当前模式：为字幕 #49 生成 1 套场景方案 ==========
2025-07-29 10:41:21,442 - INFO - 开始查找字幕序号 [282, 285] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:21,442 - INFO - 找到related_overlap场景: scene_id=418, 字幕#282
2025-07-29 10:41:21,442 - INFO - 找到related_overlap场景: scene_id=421, 字幕#285
2025-07-29 10:41:21,443 - INFO - 找到related_overlap场景: scene_id=422, 字幕#285
2025-07-29 10:41:21,444 - INFO - 字幕 #282 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:21,444 - INFO - 字幕 #285 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:21,444 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:21,445 - INFO - 开始生成方案 #1
2025-07-29 10:41:21,445 - INFO - 方案 #1: 为字幕#282选择初始化overlap场景id=418
2025-07-29 10:41:21,445 - INFO - 方案 #1: 为字幕#285选择初始化overlap场景id=422
2025-07-29 10:41:21,445 - INFO - 方案 #1: 初始选择后，当前总时长=2.80秒
2025-07-29 10:41:21,445 - INFO - 方案 #1: 额外添加overlap场景id=421, 当前总时长=5.28秒
2025-07-29 10:41:21,445 - INFO - 方案 #1: 额外between选择后，当前总时长=5.28秒
2025-07-29 10:41:21,445 - INFO - 方案 #1: 场景总时长(5.28秒)大于音频时长(3.87秒)，需要裁剪
2025-07-29 10:41:21,445 - INFO - 调整前总时长: 5.28秒, 目标时长: 3.87秒
2025-07-29 10:41:21,445 - INFO - 需要裁剪 1.41秒
2025-07-29 10:41:21,445 - INFO - 裁剪最长场景ID=421：从2.48秒裁剪至1.07秒
2025-07-29 10:41:21,445 - INFO - 调整后总时长: 3.87秒，与目标时长差异: 0.00秒
2025-07-29 10:41:21,445 - INFO - 方案 #1 调整/填充后最终总时长: 3.87秒
2025-07-29 10:41:21,445 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:21,445 - INFO - ========== 当前模式：字幕 #49 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:21,445 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:21,445 - INFO - ========== 新模式：字幕 #49 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:21,445 - INFO - 
----- 处理字幕 #49 的方案 #1 -----
2025-07-29 10:41:21,445 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-29 10:41:21,446 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiu3_ht17
2025-07-29 10:41:21,446 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\418.mp4 (确认存在: True)
2025-07-29 10:41:21,446 - INFO - 添加场景ID=418，时长=1.08秒，累计时长=1.08秒
2025-07-29 10:41:21,446 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\419.mp4 (确认存在: True)
2025-07-29 10:41:21,446 - INFO - 添加场景ID=419，时长=2.08秒，累计时长=3.16秒
2025-07-29 10:41:21,448 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\420.mp4 (确认存在: True)
2025-07-29 10:41:21,448 - INFO - 添加场景ID=420，时长=1.40秒，累计时长=4.56秒
2025-07-29 10:41:21,448 - INFO - 准备合并 3 个场景文件，总时长约 4.56秒
2025-07-29 10:41:21,448 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/418.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/419.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/420.mp4'

2025-07-29 10:41:21,449 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpiu3_ht17\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpiu3_ht17\temp_combined.mp4
2025-07-29 10:41:21,631 - INFO - 合并后的视频时长: 4.63秒，目标音频时长: 3.87秒
2025-07-29 10:41:21,631 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpiu3_ht17\temp_combined.mp4 -ss 0 -to 3.867 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-29 10:41:21,977 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:21,977 - INFO - 目标音频时长: 3.87秒
2025-07-29 10:41:21,978 - INFO - 实际视频时长: 3.90秒
2025-07-29 10:41:21,978 - INFO - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:41:21,978 - INFO - ==========================================
2025-07-29 10:41:21,978 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:21,978 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-29 10:41:21,980 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiu3_ht17
2025-07-29 10:41:22,046 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:22,046 - INFO -   - 音频时长: 3.87秒
2025-07-29 10:41:22,046 - INFO -   - 视频时长: 3.90秒
2025-07-29 10:41:22,046 - INFO -   - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:41:22,046 - INFO - 
----- 处理字幕 #49 的方案 #2 -----
2025-07-29 10:41:22,046 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-07-29 10:41:22,046 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1wigppnq
2025-07-29 10:41:22,047 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\421.mp4 (确认存在: True)
2025-07-29 10:41:22,047 - INFO - 添加场景ID=421，时长=2.48秒，累计时长=2.48秒
2025-07-29 10:41:22,047 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\422.mp4 (确认存在: True)
2025-07-29 10:41:22,047 - INFO - 添加场景ID=422，时长=1.72秒，累计时长=4.20秒
2025-07-29 10:41:22,047 - INFO - 准备合并 2 个场景文件，总时长约 4.20秒
2025-07-29 10:41:22,047 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/421.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/422.mp4'

2025-07-29 10:41:22,048 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1wigppnq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1wigppnq\temp_combined.mp4
2025-07-29 10:41:22,241 - INFO - 合并后的视频时长: 4.25秒，目标音频时长: 3.87秒
2025-07-29 10:41:22,241 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1wigppnq\temp_combined.mp4 -ss 0 -to 3.867 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-07-29 10:41:22,575 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:22,576 - INFO - 目标音频时长: 3.87秒
2025-07-29 10:41:22,576 - INFO - 实际视频时长: 3.90秒
2025-07-29 10:41:22,576 - INFO - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:41:22,576 - INFO - ==========================================
2025-07-29 10:41:22,576 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:22,576 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-07-29 10:41:22,577 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1wigppnq
2025-07-29 10:41:22,639 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:22,639 - INFO -   - 音频时长: 3.87秒
2025-07-29 10:41:22,639 - INFO -   - 视频时长: 3.90秒
2025-07-29 10:41:22,639 - INFO -   - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:41:22,639 - INFO - 
----- 处理字幕 #49 的方案 #3 -----
2025-07-29 10:41:22,639 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-07-29 10:41:22,640 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjx79o6bz
2025-07-29 10:41:22,640 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\418.mp4 (确认存在: True)
2025-07-29 10:41:22,640 - INFO - 添加场景ID=418，时长=1.08秒，累计时长=1.08秒
2025-07-29 10:41:22,641 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\422.mp4 (确认存在: True)
2025-07-29 10:41:22,641 - INFO - 添加场景ID=422，时长=1.72秒，累计时长=2.80秒
2025-07-29 10:41:22,641 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\421.mp4 (确认存在: True)
2025-07-29 10:41:22,641 - INFO - 添加场景ID=421，时长=2.48秒，累计时长=5.28秒
2025-07-29 10:41:22,641 - INFO - 准备合并 3 个场景文件，总时长约 5.28秒
2025-07-29 10:41:22,641 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/418.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/422.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/421.mp4'

2025-07-29 10:41:22,641 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjx79o6bz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjx79o6bz\temp_combined.mp4
2025-07-29 10:41:22,811 - INFO - 合并后的视频时长: 5.35秒，目标音频时长: 3.87秒
2025-07-29 10:41:22,811 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjx79o6bz\temp_combined.mp4 -ss 0 -to 3.867 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-07-29 10:41:23,158 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:23,158 - INFO - 目标音频时长: 3.87秒
2025-07-29 10:41:23,158 - INFO - 实际视频时长: 3.90秒
2025-07-29 10:41:23,158 - INFO - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:41:23,158 - INFO - ==========================================
2025-07-29 10:41:23,158 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:23,158 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-07-29 10:41:23,160 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjx79o6bz
2025-07-29 10:41:23,202 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:23,202 - INFO -   - 音频时长: 3.87秒
2025-07-29 10:41:23,202 - INFO -   - 视频时长: 3.90秒
2025-07-29 10:41:23,202 - INFO -   - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:41:23,203 - INFO - 
字幕 #49 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:23,203 - INFO - 生成的视频文件:
2025-07-29 10:41:23,203 - INFO -   1. F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-29 10:41:23,203 - INFO -   2. F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-07-29 10:41:23,203 - INFO -   3. F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-07-29 10:41:23,203 - INFO - ========== 字幕 #49 处理结束 ==========

