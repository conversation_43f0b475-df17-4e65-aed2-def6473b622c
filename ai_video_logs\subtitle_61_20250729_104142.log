2025-07-29 10:41:42,487 - INFO - ========== 字幕 #61 处理开始 ==========
2025-07-29 10:41:42,487 - INFO - 字幕内容: 摄政王一针见血，指出她想嫁的人其实是哥哥。
2025-07-29 10:41:42,487 - INFO - 字幕序号: [1239, 1243]
2025-07-29 10:41:42,487 - INFO - 音频文件详情:
2025-07-29 10:41:42,487 - INFO -   - 路径: output\61.wav
2025-07-29 10:41:42,487 - INFO -   - 时长: 4.24秒
2025-07-29 10:41:42,488 - INFO -   - 验证音频时长: 4.24秒
2025-07-29 10:41:42,488 - INFO - 字幕时间戳信息:
2025-07-29 10:41:42,488 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:42,488 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:42,488 - INFO -   - 根据生成的音频时长(4.24秒)已调整字幕时间戳
2025-07-29 10:41:42,488 - INFO - ========== 新模式：为字幕 #61 生成4套场景方案 ==========
2025-07-29 10:41:42,488 - INFO - 字幕序号列表: [1239, 1243]
2025-07-29 10:41:42,488 - INFO - 
--- 生成方案 #1：基于字幕序号 #1239 ---
2025-07-29 10:41:42,488 - INFO - 开始为单个字幕序号 #1239 匹配场景，目标时长: 4.24秒
2025-07-29 10:41:42,488 - INFO - 开始查找字幕序号 [1239] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:42,489 - INFO - 找到related_overlap场景: scene_id=1598, 字幕#1239
2025-07-29 10:41:42,489 - INFO - 找到related_overlap场景: scene_id=1599, 字幕#1239
2025-07-29 10:41:42,492 - INFO - 字幕 #1239 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:42,492 - INFO - 字幕序号 #1239 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:42,492 - INFO - 选择第一个overlap场景作为起点: scene_id=1598
2025-07-29 10:41:42,492 - INFO - 添加起点场景: scene_id=1598, 时长=1.20秒, 累计时长=1.20秒
2025-07-29 10:41:42,492 - INFO - 起点场景时长不足，需要延伸填充 3.04秒
2025-07-29 10:41:42,493 - INFO - 起点场景在原始列表中的索引: 1597
2025-07-29 10:41:42,493 - INFO - 延伸添加场景: scene_id=1599 (完整时长 0.64秒)
2025-07-29 10:41:42,493 - INFO - 累计时长: 1.84秒
2025-07-29 10:41:42,493 - INFO - 延伸添加场景: scene_id=1600 (完整时长 2.24秒)
2025-07-29 10:41:42,493 - INFO - 累计时长: 4.08秒
2025-07-29 10:41:42,493 - INFO - 延伸添加场景: scene_id=1601 (裁剪至 0.16秒)
2025-07-29 10:41:42,493 - INFO - 累计时长: 4.24秒
2025-07-29 10:41:42,493 - INFO - 字幕序号 #1239 场景匹配完成，共选择 4 个场景，总时长: 4.24秒
2025-07-29 10:41:42,493 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 10:41:42,493 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 10:41:42,493 - INFO - 
--- 生成方案 #2：基于字幕序号 #1243 ---
2025-07-29 10:41:42,493 - INFO - 开始为单个字幕序号 #1243 匹配场景，目标时长: 4.24秒
2025-07-29 10:41:42,493 - INFO - 开始查找字幕序号 [1243] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:42,494 - INFO - 找到related_overlap场景: scene_id=1602, 字幕#1243
2025-07-29 10:41:42,495 - INFO - 字幕 #1243 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:42,495 - INFO - 字幕序号 #1243 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:42,495 - INFO - 选择第一个overlap场景作为起点: scene_id=1602
2025-07-29 10:41:42,495 - INFO - 添加起点场景: scene_id=1602, 时长=2.72秒, 累计时长=2.72秒
2025-07-29 10:41:42,495 - INFO - 起点场景时长不足，需要延伸填充 1.52秒
2025-07-29 10:41:42,495 - INFO - 起点场景在原始列表中的索引: 1601
2025-07-29 10:41:42,495 - INFO - 延伸添加场景: scene_id=1603 (完整时长 1.52秒)
2025-07-29 10:41:42,496 - INFO - 累计时长: 4.24秒
2025-07-29 10:41:42,496 - INFO - 延伸添加场景: scene_id=1604 (裁剪至 0.00秒)
2025-07-29 10:41:42,496 - INFO - 累计时长: 4.24秒
2025-07-29 10:41:42,496 - INFO - 字幕序号 #1243 场景匹配完成，共选择 3 个场景，总时长: 4.24秒
2025-07-29 10:41:42,496 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:41:42,496 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:42,496 - INFO - ========== 当前模式：为字幕 #61 生成 1 套场景方案 ==========
2025-07-29 10:41:42,496 - INFO - 开始查找字幕序号 [1239, 1243] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:42,496 - INFO - 找到related_overlap场景: scene_id=1598, 字幕#1239
2025-07-29 10:41:42,496 - INFO - 找到related_overlap场景: scene_id=1599, 字幕#1239
2025-07-29 10:41:42,496 - INFO - 找到related_overlap场景: scene_id=1602, 字幕#1243
2025-07-29 10:41:42,500 - INFO - 字幕 #1239 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:42,500 - INFO - 字幕 #1243 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:42,500 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:42,500 - INFO - 开始生成方案 #1
2025-07-29 10:41:42,500 - INFO - 方案 #1: 为字幕#1239选择初始化overlap场景id=1599
2025-07-29 10:41:42,500 - INFO - 方案 #1: 为字幕#1243选择初始化overlap场景id=1602
2025-07-29 10:41:42,500 - INFO - 方案 #1: 初始选择后，当前总时长=3.36秒
2025-07-29 10:41:42,500 - INFO - 方案 #1: 额外添加overlap场景id=1598, 当前总时长=4.56秒
2025-07-29 10:41:42,500 - INFO - 方案 #1: 额外between选择后，当前总时长=4.56秒
2025-07-29 10:41:42,500 - INFO - 方案 #1: 场景总时长(4.56秒)大于音频时长(4.24秒)，需要裁剪
2025-07-29 10:41:42,500 - INFO - 调整前总时长: 4.56秒, 目标时长: 4.24秒
2025-07-29 10:41:42,500 - INFO - 需要裁剪 0.32秒
2025-07-29 10:41:42,500 - INFO - 裁剪最长场景ID=1602：从2.72秒裁剪至2.40秒
2025-07-29 10:41:42,500 - INFO - 调整后总时长: 4.24秒，与目标时长差异: 0.00秒
2025-07-29 10:41:42,500 - INFO - 方案 #1 调整/填充后最终总时长: 4.24秒
2025-07-29 10:41:42,500 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:42,500 - INFO - ========== 当前模式：字幕 #61 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:42,500 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:42,500 - INFO - ========== 新模式：字幕 #61 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:42,500 - INFO - 
----- 处理字幕 #61 的方案 #1 -----
2025-07-29 10:41:42,501 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-07-29 10:41:42,501 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7heov_jf
2025-07-29 10:41:42,502 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1598.mp4 (确认存在: True)
2025-07-29 10:41:42,502 - INFO - 添加场景ID=1598，时长=1.20秒，累计时长=1.20秒
2025-07-29 10:41:42,502 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1599.mp4 (确认存在: True)
2025-07-29 10:41:42,502 - INFO - 添加场景ID=1599，时长=0.64秒，累计时长=1.84秒
2025-07-29 10:41:42,502 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1600.mp4 (确认存在: True)
2025-07-29 10:41:42,502 - INFO - 添加场景ID=1600，时长=2.24秒，累计时长=4.08秒
2025-07-29 10:41:42,502 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1601.mp4 (确认存在: True)
2025-07-29 10:41:42,502 - INFO - 添加场景ID=1601，时长=1.96秒，累计时长=6.04秒
2025-07-29 10:41:42,503 - INFO - 准备合并 4 个场景文件，总时长约 6.04秒
2025-07-29 10:41:42,503 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1598.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1599.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1600.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1601.mp4'

2025-07-29 10:41:42,503 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7heov_jf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7heov_jf\temp_combined.mp4
2025-07-29 10:41:42,689 - INFO - 合并后的视频时长: 6.13秒，目标音频时长: 4.24秒
2025-07-29 10:41:42,690 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7heov_jf\temp_combined.mp4 -ss 0 -to 4.241 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-07-29 10:41:43,049 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:43,049 - INFO - 目标音频时长: 4.24秒
2025-07-29 10:41:43,049 - INFO - 实际视频时长: 4.30秒
2025-07-29 10:41:43,049 - INFO - 时长差异: 0.06秒 (1.46%)
2025-07-29 10:41:43,049 - INFO - ==========================================
2025-07-29 10:41:43,049 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:43,049 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-07-29 10:41:43,050 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7heov_jf
2025-07-29 10:41:43,111 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:43,111 - INFO -   - 音频时长: 4.24秒
2025-07-29 10:41:43,111 - INFO -   - 视频时长: 4.30秒
2025-07-29 10:41:43,111 - INFO -   - 时长差异: 0.06秒 (1.46%)
2025-07-29 10:41:43,111 - INFO - 
----- 处理字幕 #61 的方案 #2 -----
2025-07-29 10:41:43,112 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-07-29 10:41:43,112 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp7o0e01w
2025-07-29 10:41:43,113 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1602.mp4 (确认存在: True)
2025-07-29 10:41:43,113 - INFO - 添加场景ID=1602，时长=2.72秒，累计时长=2.72秒
2025-07-29 10:41:43,113 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1603.mp4 (确认存在: True)
2025-07-29 10:41:43,113 - INFO - 添加场景ID=1603，时长=1.52秒，累计时长=4.24秒
2025-07-29 10:41:43,113 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1604.mp4 (确认存在: True)
2025-07-29 10:41:43,113 - INFO - 添加场景ID=1604，时长=4.56秒，累计时长=8.80秒
2025-07-29 10:41:43,113 - INFO - 场景总时长(8.80秒)已达到音频时长(4.24秒)的1.5倍，停止添加场景
2025-07-29 10:41:43,113 - INFO - 准备合并 3 个场景文件，总时长约 8.80秒
2025-07-29 10:41:43,114 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1602.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1603.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1604.mp4'

2025-07-29 10:41:43,114 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpp7o0e01w\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpp7o0e01w\temp_combined.mp4
2025-07-29 10:41:43,279 - INFO - 合并后的视频时长: 8.87秒，目标音频时长: 4.24秒
2025-07-29 10:41:43,279 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpp7o0e01w\temp_combined.mp4 -ss 0 -to 4.241 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-07-29 10:41:43,618 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:43,618 - INFO - 目标音频时长: 4.24秒
2025-07-29 10:41:43,619 - INFO - 实际视频时长: 4.30秒
2025-07-29 10:41:43,619 - INFO - 时长差异: 0.06秒 (1.46%)
2025-07-29 10:41:43,619 - INFO - ==========================================
2025-07-29 10:41:43,619 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:43,619 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-07-29 10:41:43,620 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp7o0e01w
2025-07-29 10:41:43,663 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:43,663 - INFO -   - 音频时长: 4.24秒
2025-07-29 10:41:43,663 - INFO -   - 视频时长: 4.30秒
2025-07-29 10:41:43,663 - INFO -   - 时长差异: 0.06秒 (1.46%)
2025-07-29 10:41:43,663 - INFO - 
----- 处理字幕 #61 的方案 #3 -----
2025-07-29 10:41:43,663 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-07-29 10:41:43,663 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpizv0avxd
2025-07-29 10:41:43,664 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1599.mp4 (确认存在: True)
2025-07-29 10:41:43,664 - INFO - 添加场景ID=1599，时长=0.64秒，累计时长=0.64秒
2025-07-29 10:41:43,664 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1602.mp4 (确认存在: True)
2025-07-29 10:41:43,664 - INFO - 添加场景ID=1602，时长=2.72秒，累计时长=3.36秒
2025-07-29 10:41:43,664 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1598.mp4 (确认存在: True)
2025-07-29 10:41:43,664 - INFO - 添加场景ID=1598，时长=1.20秒，累计时长=4.56秒
2025-07-29 10:41:43,664 - INFO - 准备合并 3 个场景文件，总时长约 4.56秒
2025-07-29 10:41:43,664 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1599.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1602.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1598.mp4'

2025-07-29 10:41:43,665 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpizv0avxd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpizv0avxd\temp_combined.mp4
2025-07-29 10:41:43,834 - INFO - 合并后的视频时长: 4.63秒，目标音频时长: 4.24秒
2025-07-29 10:41:43,834 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpizv0avxd\temp_combined.mp4 -ss 0 -to 4.241 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-07-29 10:41:44,153 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:44,153 - INFO - 目标音频时长: 4.24秒
2025-07-29 10:41:44,153 - INFO - 实际视频时长: 4.30秒
2025-07-29 10:41:44,153 - INFO - 时长差异: 0.06秒 (1.46%)
2025-07-29 10:41:44,153 - INFO - ==========================================
2025-07-29 10:41:44,153 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:44,153 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-07-29 10:41:44,154 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpizv0avxd
2025-07-29 10:41:44,213 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:44,213 - INFO -   - 音频时长: 4.24秒
2025-07-29 10:41:44,213 - INFO -   - 视频时长: 4.30秒
2025-07-29 10:41:44,213 - INFO -   - 时长差异: 0.06秒 (1.46%)
2025-07-29 10:41:44,213 - INFO - 
字幕 #61 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:44,214 - INFO - 生成的视频文件:
2025-07-29 10:41:44,214 - INFO -   1. F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-07-29 10:41:44,214 - INFO -   2. F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-07-29 10:41:44,214 - INFO -   3. F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-07-29 10:41:44,214 - INFO - ========== 字幕 #61 处理结束 ==========

