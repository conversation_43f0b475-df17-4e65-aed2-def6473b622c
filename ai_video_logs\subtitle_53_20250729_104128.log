2025-07-29 10:41:28,809 - INFO - ========== 字幕 #53 处理开始 ==========
2025-07-29 10:41:28,809 - INFO - 字幕内容: 九姨娘恃宠而骄，女人心如死灰，他要有孩子了。
2025-07-29 10:41:28,809 - INFO - 字幕序号: [303, 307]
2025-07-29 10:41:28,809 - INFO - 音频文件详情:
2025-07-29 10:41:28,809 - INFO -   - 路径: output\53.wav
2025-07-29 10:41:28,809 - INFO -   - 时长: 4.32秒
2025-07-29 10:41:28,810 - INFO -   - 验证音频时长: 4.32秒
2025-07-29 10:41:28,810 - INFO - 字幕时间戳信息:
2025-07-29 10:41:28,810 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:28,810 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:28,811 - INFO -   - 根据生成的音频时长(4.32秒)已调整字幕时间戳
2025-07-29 10:41:28,811 - INFO - ========== 新模式：为字幕 #53 生成4套场景方案 ==========
2025-07-29 10:41:28,811 - INFO - 字幕序号列表: [303, 307]
2025-07-29 10:41:28,811 - INFO - 
--- 生成方案 #1：基于字幕序号 #303 ---
2025-07-29 10:41:28,811 - INFO - 开始为单个字幕序号 #303 匹配场景，目标时长: 4.32秒
2025-07-29 10:41:28,811 - INFO - 开始查找字幕序号 [303] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:28,811 - INFO - 找到related_overlap场景: scene_id=444, 字幕#303
2025-07-29 10:41:28,817 - INFO - 字幕 #303 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:28,817 - INFO - 字幕序号 #303 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:28,817 - INFO - 选择第一个overlap场景作为起点: scene_id=444
2025-07-29 10:41:28,817 - INFO - 添加起点场景: scene_id=444, 时长=4.92秒, 累计时长=4.92秒
2025-07-29 10:41:28,817 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:28,817 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:41:28,817 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:41:28,817 - INFO - 
--- 生成方案 #2：基于字幕序号 #307 ---
2025-07-29 10:41:28,817 - INFO - 开始为单个字幕序号 #307 匹配场景，目标时长: 4.32秒
2025-07-29 10:41:28,817 - INFO - 开始查找字幕序号 [307] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:28,818 - INFO - 找到related_overlap场景: scene_id=449, 字幕#307
2025-07-29 10:41:28,824 - INFO - 字幕 #307 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:28,824 - INFO - 字幕序号 #307 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:28,824 - INFO - 选择第一个overlap场景作为起点: scene_id=449
2025-07-29 10:41:28,824 - INFO - 添加起点场景: scene_id=449, 时长=4.28秒, 累计时长=4.28秒
2025-07-29 10:41:28,824 - INFO - 起点场景时长不足，需要延伸填充 0.04秒
2025-07-29 10:41:28,824 - INFO - 起点场景在原始列表中的索引: 448
2025-07-29 10:41:28,824 - INFO - 延伸添加场景: scene_id=450 (裁剪至 0.04秒)
2025-07-29 10:41:28,824 - INFO - 累计时长: 4.32秒
2025-07-29 10:41:28,824 - INFO - 字幕序号 #307 场景匹配完成，共选择 2 个场景，总时长: 4.32秒
2025-07-29 10:41:28,824 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:28,824 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:28,824 - INFO - ========== 当前模式：为字幕 #53 生成 1 套场景方案 ==========
2025-07-29 10:41:28,824 - INFO - 开始查找字幕序号 [303, 307] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:28,824 - INFO - 找到related_overlap场景: scene_id=444, 字幕#303
2025-07-29 10:41:28,824 - INFO - 找到related_overlap场景: scene_id=449, 字幕#307
2025-07-29 10:41:28,827 - INFO - 字幕 #303 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:28,827 - INFO - 字幕 #307 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:28,827 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:28,827 - INFO - 开始生成方案 #1
2025-07-29 10:41:28,827 - INFO - 方案 #1: 为字幕#303选择初始化overlap场景id=444
2025-07-29 10:41:28,827 - INFO - 方案 #1: 为字幕#307选择初始化overlap场景id=449
2025-07-29 10:41:28,827 - INFO - 方案 #1: 初始选择后，当前总时长=9.20秒
2025-07-29 10:41:28,827 - INFO - 方案 #1: 额外between选择后，当前总时长=9.20秒
2025-07-29 10:41:28,827 - INFO - 方案 #1: 场景总时长(9.20秒)大于音频时长(4.32秒)，需要裁剪
2025-07-29 10:41:28,828 - INFO - 调整前总时长: 9.20秒, 目标时长: 4.32秒
2025-07-29 10:41:28,828 - INFO - 需要裁剪 4.88秒
2025-07-29 10:41:28,828 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:28,828 - INFO - 裁剪场景ID=444：从4.92秒裁剪至1.48秒
2025-07-29 10:41:28,828 - INFO - 裁剪场景ID=449：从4.28秒裁剪至2.84秒
2025-07-29 10:41:28,828 - INFO - 调整后总时长: 4.32秒，与目标时长差异: 0.00秒
2025-07-29 10:41:28,828 - INFO - 方案 #1 调整/填充后最终总时长: 4.32秒
2025-07-29 10:41:28,828 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:28,828 - INFO - ========== 当前模式：字幕 #53 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:28,828 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:28,828 - INFO - ========== 新模式：字幕 #53 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:28,828 - INFO - 
----- 处理字幕 #53 的方案 #1 -----
2025-07-29 10:41:28,828 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-29 10:41:28,829 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy12irr6y
2025-07-29 10:41:28,829 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\444.mp4 (确认存在: True)
2025-07-29 10:41:28,830 - INFO - 添加场景ID=444，时长=4.92秒，累计时长=4.92秒
2025-07-29 10:41:28,830 - INFO - 准备合并 1 个场景文件，总时长约 4.92秒
2025-07-29 10:41:28,830 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/444.mp4'

2025-07-29 10:41:28,830 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpy12irr6y\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpy12irr6y\temp_combined.mp4
2025-07-29 10:41:28,988 - INFO - 合并后的视频时长: 4.94秒，目标音频时长: 4.32秒
2025-07-29 10:41:28,988 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpy12irr6y\temp_combined.mp4 -ss 0 -to 4.316 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-29 10:41:29,369 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:29,369 - INFO - 目标音频时长: 4.32秒
2025-07-29 10:41:29,369 - INFO - 实际视频时长: 4.34秒
2025-07-29 10:41:29,369 - INFO - 时长差异: 0.03秒 (0.63%)
2025-07-29 10:41:29,369 - INFO - ==========================================
2025-07-29 10:41:29,369 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:29,369 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-29 10:41:29,371 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy12irr6y
2025-07-29 10:41:29,432 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:29,432 - INFO -   - 音频时长: 4.32秒
2025-07-29 10:41:29,432 - INFO -   - 视频时长: 4.34秒
2025-07-29 10:41:29,432 - INFO -   - 时长差异: 0.03秒 (0.63%)
2025-07-29 10:41:29,432 - INFO - 
----- 处理字幕 #53 的方案 #2 -----
2025-07-29 10:41:29,433 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-07-29 10:41:29,433 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxhafgnr1
2025-07-29 10:41:29,434 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\449.mp4 (确认存在: True)
2025-07-29 10:41:29,434 - INFO - 添加场景ID=449，时长=4.28秒，累计时长=4.28秒
2025-07-29 10:41:29,434 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\450.mp4 (确认存在: True)
2025-07-29 10:41:29,434 - INFO - 添加场景ID=450，时长=4.20秒，累计时长=8.48秒
2025-07-29 10:41:29,434 - INFO - 场景总时长(8.48秒)已达到音频时长(4.32秒)的1.5倍，停止添加场景
2025-07-29 10:41:29,434 - INFO - 准备合并 2 个场景文件，总时长约 8.48秒
2025-07-29 10:41:29,435 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/449.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/450.mp4'

2025-07-29 10:41:29,435 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxhafgnr1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxhafgnr1\temp_combined.mp4
2025-07-29 10:41:29,579 - INFO - 合并后的视频时长: 8.53秒，目标音频时长: 4.32秒
2025-07-29 10:41:29,579 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxhafgnr1\temp_combined.mp4 -ss 0 -to 4.316 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-07-29 10:41:29,937 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:29,937 - INFO - 目标音频时长: 4.32秒
2025-07-29 10:41:29,939 - INFO - 实际视频时长: 4.34秒
2025-07-29 10:41:29,939 - INFO - 时长差异: 0.03秒 (0.63%)
2025-07-29 10:41:29,939 - INFO - ==========================================
2025-07-29 10:41:29,939 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:29,939 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-07-29 10:41:29,940 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxhafgnr1
2025-07-29 10:41:30,000 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:30,000 - INFO -   - 音频时长: 4.32秒
2025-07-29 10:41:30,000 - INFO -   - 视频时长: 4.34秒
2025-07-29 10:41:30,000 - INFO -   - 时长差异: 0.03秒 (0.63%)
2025-07-29 10:41:30,000 - INFO - 
----- 处理字幕 #53 的方案 #3 -----
2025-07-29 10:41:30,000 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\53_3.mp4
2025-07-29 10:41:30,000 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpn3_o19b2
2025-07-29 10:41:30,000 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\444.mp4 (确认存在: True)
2025-07-29 10:41:30,001 - INFO - 添加场景ID=444，时长=4.92秒，累计时长=4.92秒
2025-07-29 10:41:30,001 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\449.mp4 (确认存在: True)
2025-07-29 10:41:30,001 - INFO - 添加场景ID=449，时长=4.28秒，累计时长=9.20秒
2025-07-29 10:41:30,001 - INFO - 场景总时长(9.20秒)已达到音频时长(4.32秒)的1.5倍，停止添加场景
2025-07-29 10:41:30,001 - INFO - 准备合并 2 个场景文件，总时长约 9.20秒
2025-07-29 10:41:30,001 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/444.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/449.mp4'

2025-07-29 10:41:30,001 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpn3_o19b2\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpn3_o19b2\temp_combined.mp4
2025-07-29 10:41:30,145 - INFO - 合并后的视频时长: 9.25秒，目标音频时长: 4.32秒
2025-07-29 10:41:30,145 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpn3_o19b2\temp_combined.mp4 -ss 0 -to 4.316 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\53_3.mp4
2025-07-29 10:41:30,491 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:30,492 - INFO - 目标音频时长: 4.32秒
2025-07-29 10:41:30,492 - INFO - 实际视频时长: 4.34秒
2025-07-29 10:41:30,492 - INFO - 时长差异: 0.03秒 (0.63%)
2025-07-29 10:41:30,492 - INFO - ==========================================
2025-07-29 10:41:30,492 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:30,492 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\53_3.mp4
2025-07-29 10:41:30,493 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpn3_o19b2
2025-07-29 10:41:30,552 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:30,552 - INFO -   - 音频时长: 4.32秒
2025-07-29 10:41:30,552 - INFO -   - 视频时长: 4.34秒
2025-07-29 10:41:30,552 - INFO -   - 时长差异: 0.03秒 (0.63%)
2025-07-29 10:41:30,552 - INFO - 
字幕 #53 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:30,552 - INFO - 生成的视频文件:
2025-07-29 10:41:30,552 - INFO -   1. F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-07-29 10:41:30,552 - INFO -   2. F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-07-29 10:41:30,552 - INFO -   3. F:/github/aicut_auto/newcut_ai\53_3.mp4
2025-07-29 10:41:30,552 - INFO - ========== 字幕 #53 处理结束 ==========

