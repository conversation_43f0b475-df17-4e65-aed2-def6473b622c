2025-07-29 10:41:47,475 - INFO - ========== 字幕 #64 处理开始 ==========
2025-07-29 10:41:47,475 - INFO - 字幕内容: 摄政王点明和离的后果，哥哥却正好让她陪自己。
2025-07-29 10:41:47,475 - INFO - 字幕序号: [1256, 1261]
2025-07-29 10:41:47,475 - INFO - 音频文件详情:
2025-07-29 10:41:47,475 - INFO -   - 路径: output\64.wav
2025-07-29 10:41:47,475 - INFO -   - 时长: 4.41秒
2025-07-29 10:41:47,476 - INFO -   - 验证音频时长: 4.41秒
2025-07-29 10:41:47,476 - INFO - 字幕时间戳信息:
2025-07-29 10:41:47,476 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:47,485 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:47,485 - INFO -   - 根据生成的音频时长(4.41秒)已调整字幕时间戳
2025-07-29 10:41:47,485 - INFO - ========== 新模式：为字幕 #64 生成4套场景方案 ==========
2025-07-29 10:41:47,485 - INFO - 字幕序号列表: [1256, 1261]
2025-07-29 10:41:47,485 - INFO - 
--- 生成方案 #1：基于字幕序号 #1256 ---
2025-07-29 10:41:47,485 - INFO - 开始为单个字幕序号 #1256 匹配场景，目标时长: 4.41秒
2025-07-29 10:41:47,485 - INFO - 开始查找字幕序号 [1256] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:47,487 - INFO - 找到related_overlap场景: scene_id=1611, 字幕#1256
2025-07-29 10:41:47,487 - INFO - 找到related_overlap场景: scene_id=1612, 字幕#1256
2025-07-29 10:41:47,488 - INFO - 找到related_between场景: scene_id=1610, 字幕#1256
2025-07-29 10:41:47,489 - INFO - 字幕 #1256 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:41:47,489 - INFO - 字幕序号 #1256 找到 2 个可用overlap场景, 1 个可用between场景
2025-07-29 10:41:47,489 - INFO - 选择第一个overlap场景作为起点: scene_id=1611
2025-07-29 10:41:47,489 - INFO - 添加起点场景: scene_id=1611, 时长=2.64秒, 累计时长=2.64秒
2025-07-29 10:41:47,489 - INFO - 起点场景时长不足，需要延伸填充 1.77秒
2025-07-29 10:41:47,489 - INFO - 起点场景在原始列表中的索引: 1610
2025-07-29 10:41:47,489 - INFO - 延伸添加场景: scene_id=1612 (裁剪至 1.77秒)
2025-07-29 10:41:47,489 - INFO - 累计时长: 4.41秒
2025-07-29 10:41:47,489 - INFO - 字幕序号 #1256 场景匹配完成，共选择 2 个场景，总时长: 4.41秒
2025-07-29 10:41:47,489 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:47,489 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:47,489 - INFO - 
--- 生成方案 #2：基于字幕序号 #1261 ---
2025-07-29 10:41:47,489 - INFO - 开始为单个字幕序号 #1261 匹配场景，目标时长: 4.41秒
2025-07-29 10:41:47,489 - INFO - 开始查找字幕序号 [1261] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:47,490 - INFO - 找到related_overlap场景: scene_id=1613, 字幕#1261
2025-07-29 10:41:47,491 - INFO - 字幕 #1261 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:47,491 - INFO - 字幕序号 #1261 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:47,491 - INFO - 选择第一个overlap场景作为起点: scene_id=1613
2025-07-29 10:41:47,491 - INFO - 添加起点场景: scene_id=1613, 时长=5.52秒, 累计时长=5.52秒
2025-07-29 10:41:47,491 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:47,491 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:41:47,491 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:47,491 - INFO - ========== 当前模式：为字幕 #64 生成 1 套场景方案 ==========
2025-07-29 10:41:47,491 - INFO - 开始查找字幕序号 [1256, 1261] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:47,492 - INFO - 找到related_overlap场景: scene_id=1611, 字幕#1256
2025-07-29 10:41:47,492 - INFO - 找到related_overlap场景: scene_id=1612, 字幕#1256
2025-07-29 10:41:47,492 - INFO - 找到related_overlap场景: scene_id=1613, 字幕#1261
2025-07-29 10:41:47,493 - INFO - 找到related_between场景: scene_id=1610, 字幕#1256
2025-07-29 10:41:47,494 - INFO - 字幕 #1256 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:41:47,494 - INFO - 字幕 #1261 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:47,494 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 10:41:47,494 - INFO - 开始生成方案 #1
2025-07-29 10:41:47,494 - INFO - 方案 #1: 为字幕#1256选择初始化overlap场景id=1611
2025-07-29 10:41:47,494 - INFO - 方案 #1: 为字幕#1261选择初始化overlap场景id=1613
2025-07-29 10:41:47,494 - INFO - 方案 #1: 初始选择后，当前总时长=8.16秒
2025-07-29 10:41:47,494 - INFO - 方案 #1: 额外between选择后，当前总时长=8.16秒
2025-07-29 10:41:47,494 - INFO - 方案 #1: 场景总时长(8.16秒)大于音频时长(4.41秒)，需要裁剪
2025-07-29 10:41:47,494 - INFO - 调整前总时长: 8.16秒, 目标时长: 4.41秒
2025-07-29 10:41:47,494 - INFO - 需要裁剪 3.75秒
2025-07-29 10:41:47,494 - INFO - 裁剪最长场景ID=1613：从5.52秒裁剪至1.77秒
2025-07-29 10:41:47,494 - INFO - 调整后总时长: 4.41秒，与目标时长差异: 0.00秒
2025-07-29 10:41:47,494 - INFO - 方案 #1 调整/填充后最终总时长: 4.41秒
2025-07-29 10:41:47,494 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:47,494 - INFO - ========== 当前模式：字幕 #64 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:47,494 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:47,494 - INFO - ========== 新模式：字幕 #64 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:47,494 - INFO - 
----- 处理字幕 #64 的方案 #1 -----
2025-07-29 10:41:47,495 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-29 10:41:47,495 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv_av3fc8
2025-07-29 10:41:47,496 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1611.mp4 (确认存在: True)
2025-07-29 10:41:47,496 - INFO - 添加场景ID=1611，时长=2.64秒，累计时长=2.64秒
2025-07-29 10:41:47,496 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1612.mp4 (确认存在: True)
2025-07-29 10:41:47,496 - INFO - 添加场景ID=1612，时长=2.32秒，累计时长=4.96秒
2025-07-29 10:41:47,496 - INFO - 准备合并 2 个场景文件，总时长约 4.96秒
2025-07-29 10:41:47,496 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1611.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1612.mp4'

2025-07-29 10:41:47,497 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpv_av3fc8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpv_av3fc8\temp_combined.mp4
2025-07-29 10:41:47,665 - INFO - 合并后的视频时长: 5.01秒，目标音频时长: 4.41秒
2025-07-29 10:41:47,665 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpv_av3fc8\temp_combined.mp4 -ss 0 -to 4.406 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-29 10:41:48,028 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:48,028 - INFO - 目标音频时长: 4.41秒
2025-07-29 10:41:48,028 - INFO - 实际视频时长: 4.46秒
2025-07-29 10:41:48,028 - INFO - 时长差异: 0.06秒 (1.29%)
2025-07-29 10:41:48,028 - INFO - ==========================================
2025-07-29 10:41:48,029 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:48,029 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-29 10:41:48,030 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv_av3fc8
2025-07-29 10:41:48,076 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:48,076 - INFO -   - 音频时长: 4.41秒
2025-07-29 10:41:48,076 - INFO -   - 视频时长: 4.46秒
2025-07-29 10:41:48,076 - INFO -   - 时长差异: 0.06秒 (1.29%)
2025-07-29 10:41:48,076 - INFO - 
----- 处理字幕 #64 的方案 #2 -----
2025-07-29 10:41:48,076 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-29 10:41:48,077 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpseaeu1ay
2025-07-29 10:41:48,077 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1613.mp4 (确认存在: True)
2025-07-29 10:41:48,077 - INFO - 添加场景ID=1613，时长=5.52秒，累计时长=5.52秒
2025-07-29 10:41:48,077 - INFO - 准备合并 1 个场景文件，总时长约 5.52秒
2025-07-29 10:41:48,078 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1613.mp4'

2025-07-29 10:41:48,078 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpseaeu1ay\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpseaeu1ay\temp_combined.mp4
2025-07-29 10:41:48,220 - INFO - 合并后的视频时长: 5.54秒，目标音频时长: 4.41秒
2025-07-29 10:41:48,221 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpseaeu1ay\temp_combined.mp4 -ss 0 -to 4.406 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-29 10:41:48,548 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:48,548 - INFO - 目标音频时长: 4.41秒
2025-07-29 10:41:48,548 - INFO - 实际视频时长: 4.46秒
2025-07-29 10:41:48,548 - INFO - 时长差异: 0.06秒 (1.29%)
2025-07-29 10:41:48,548 - INFO - ==========================================
2025-07-29 10:41:48,548 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:48,548 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-29 10:41:48,549 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpseaeu1ay
2025-07-29 10:41:48,605 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:48,605 - INFO -   - 音频时长: 4.41秒
2025-07-29 10:41:48,605 - INFO -   - 视频时长: 4.46秒
2025-07-29 10:41:48,605 - INFO -   - 时长差异: 0.06秒 (1.29%)
2025-07-29 10:41:48,605 - INFO - 
----- 处理字幕 #64 的方案 #3 -----
2025-07-29 10:41:48,605 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-29 10:41:48,605 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjojkuo7a
2025-07-29 10:41:48,607 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1611.mp4 (确认存在: True)
2025-07-29 10:41:48,607 - INFO - 添加场景ID=1611，时长=2.64秒，累计时长=2.64秒
2025-07-29 10:41:48,607 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1613.mp4 (确认存在: True)
2025-07-29 10:41:48,607 - INFO - 添加场景ID=1613，时长=5.52秒，累计时长=8.16秒
2025-07-29 10:41:48,607 - INFO - 场景总时长(8.16秒)已达到音频时长(4.41秒)的1.5倍，停止添加场景
2025-07-29 10:41:48,607 - INFO - 准备合并 2 个场景文件，总时长约 8.16秒
2025-07-29 10:41:48,607 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1611.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1613.mp4'

2025-07-29 10:41:48,607 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjojkuo7a\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjojkuo7a\temp_combined.mp4
2025-07-29 10:41:48,761 - INFO - 合并后的视频时长: 8.21秒，目标音频时长: 4.41秒
2025-07-29 10:41:48,761 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjojkuo7a\temp_combined.mp4 -ss 0 -to 4.406 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-29 10:41:49,107 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:49,107 - INFO - 目标音频时长: 4.41秒
2025-07-29 10:41:49,107 - INFO - 实际视频时长: 4.46秒
2025-07-29 10:41:49,107 - INFO - 时长差异: 0.06秒 (1.29%)
2025-07-29 10:41:49,107 - INFO - ==========================================
2025-07-29 10:41:49,107 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:49,107 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-29 10:41:49,108 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjojkuo7a
2025-07-29 10:41:49,166 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:49,166 - INFO -   - 音频时长: 4.41秒
2025-07-29 10:41:49,166 - INFO -   - 视频时长: 4.46秒
2025-07-29 10:41:49,166 - INFO -   - 时长差异: 0.06秒 (1.29%)
2025-07-29 10:41:49,166 - INFO - 
字幕 #64 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:49,166 - INFO - 生成的视频文件:
2025-07-29 10:41:49,166 - INFO -   1. F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-07-29 10:41:49,166 - INFO -   2. F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-07-29 10:41:49,166 - INFO -   3. F:/github/aicut_auto/newcut_ai\64_3.mp4
2025-07-29 10:41:49,166 - INFO - ========== 字幕 #64 处理结束 ==========

