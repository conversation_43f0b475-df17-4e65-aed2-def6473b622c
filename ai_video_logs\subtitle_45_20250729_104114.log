2025-07-29 10:41:14,460 - INFO - ========== 字幕 #45 处理开始 ==========
2025-07-29 10:41:14,460 - INFO - 字幕内容: 哥哥看穿她的伎俩，怒斥是她阴谋算计在先。
2025-07-29 10:41:14,460 - INFO - 字幕序号: [257, 264]
2025-07-29 10:41:14,461 - INFO - 音频文件详情:
2025-07-29 10:41:14,461 - INFO -   - 路径: output\45.wav
2025-07-29 10:41:14,461 - INFO -   - 时长: 3.03秒
2025-07-29 10:41:14,461 - INFO -   - 验证音频时长: 3.03秒
2025-07-29 10:41:14,461 - INFO - 字幕时间戳信息:
2025-07-29 10:41:14,461 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:14,462 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:14,462 - INFO -   - 根据生成的音频时长(3.03秒)已调整字幕时间戳
2025-07-29 10:41:14,462 - INFO - ========== 新模式：为字幕 #45 生成4套场景方案 ==========
2025-07-29 10:41:14,462 - INFO - 字幕序号列表: [257, 264]
2025-07-29 10:41:14,462 - INFO - 
--- 生成方案 #1：基于字幕序号 #257 ---
2025-07-29 10:41:14,462 - INFO - 开始为单个字幕序号 #257 匹配场景，目标时长: 3.03秒
2025-07-29 10:41:14,462 - INFO - 开始查找字幕序号 [257] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:14,462 - INFO - 找到related_overlap场景: scene_id=390, 字幕#257
2025-07-29 10:41:14,467 - INFO - 字幕 #257 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:14,467 - INFO - 字幕序号 #257 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:14,467 - INFO - 选择第一个overlap场景作为起点: scene_id=390
2025-07-29 10:41:14,467 - INFO - 添加起点场景: scene_id=390, 时长=1.80秒, 累计时长=1.80秒
2025-07-29 10:41:14,467 - INFO - 起点场景时长不足，需要延伸填充 1.23秒
2025-07-29 10:41:14,468 - INFO - 起点场景在原始列表中的索引: 389
2025-07-29 10:41:14,468 - INFO - 延伸添加场景: scene_id=391 (裁剪至 1.23秒)
2025-07-29 10:41:14,468 - INFO - 累计时长: 3.03秒
2025-07-29 10:41:14,468 - INFO - 字幕序号 #257 场景匹配完成，共选择 2 个场景，总时长: 3.03秒
2025-07-29 10:41:14,468 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:14,468 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:14,468 - INFO - 
--- 生成方案 #2：基于字幕序号 #264 ---
2025-07-29 10:41:14,468 - INFO - 开始为单个字幕序号 #264 匹配场景，目标时长: 3.03秒
2025-07-29 10:41:14,468 - INFO - 开始查找字幕序号 [264] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:14,468 - INFO - 找到related_overlap场景: scene_id=396, 字幕#264
2025-07-29 10:41:14,468 - INFO - 找到related_overlap场景: scene_id=397, 字幕#264
2025-07-29 10:41:14,471 - INFO - 字幕 #264 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:14,471 - INFO - 字幕序号 #264 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:14,471 - INFO - 选择第一个overlap场景作为起点: scene_id=396
2025-07-29 10:41:14,471 - INFO - 添加起点场景: scene_id=396, 时长=0.64秒, 累计时长=0.64秒
2025-07-29 10:41:14,471 - INFO - 起点场景时长不足，需要延伸填充 2.39秒
2025-07-29 10:41:14,471 - INFO - 起点场景在原始列表中的索引: 395
2025-07-29 10:41:14,471 - INFO - 延伸添加场景: scene_id=397 (完整时长 1.88秒)
2025-07-29 10:41:14,471 - INFO - 累计时长: 2.52秒
2025-07-29 10:41:14,471 - INFO - 延伸添加场景: scene_id=398 (裁剪至 0.51秒)
2025-07-29 10:41:14,471 - INFO - 累计时长: 3.03秒
2025-07-29 10:41:14,471 - INFO - 字幕序号 #264 场景匹配完成，共选择 3 个场景，总时长: 3.03秒
2025-07-29 10:41:14,471 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:41:14,471 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:14,471 - INFO - ========== 当前模式：为字幕 #45 生成 1 套场景方案 ==========
2025-07-29 10:41:14,471 - INFO - 开始查找字幕序号 [257, 264] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:14,471 - INFO - 找到related_overlap场景: scene_id=390, 字幕#257
2025-07-29 10:41:14,471 - INFO - 找到related_overlap场景: scene_id=396, 字幕#264
2025-07-29 10:41:14,471 - INFO - 找到related_overlap场景: scene_id=397, 字幕#264
2025-07-29 10:41:14,474 - INFO - 字幕 #257 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:14,474 - INFO - 字幕 #264 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:14,474 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:14,474 - INFO - 开始生成方案 #1
2025-07-29 10:41:14,474 - INFO - 方案 #1: 为字幕#257选择初始化overlap场景id=390
2025-07-29 10:41:14,474 - INFO - 方案 #1: 为字幕#264选择初始化overlap场景id=397
2025-07-29 10:41:14,474 - INFO - 方案 #1: 初始选择后，当前总时长=3.68秒
2025-07-29 10:41:14,474 - INFO - 方案 #1: 额外between选择后，当前总时长=3.68秒
2025-07-29 10:41:14,474 - INFO - 方案 #1: 场景总时长(3.68秒)大于音频时长(3.03秒)，需要裁剪
2025-07-29 10:41:14,474 - INFO - 调整前总时长: 3.68秒, 目标时长: 3.03秒
2025-07-29 10:41:14,474 - INFO - 需要裁剪 0.65秒
2025-07-29 10:41:14,474 - INFO - 裁剪最长场景ID=397：从1.88秒裁剪至1.23秒
2025-07-29 10:41:14,475 - INFO - 调整后总时长: 3.03秒，与目标时长差异: 0.00秒
2025-07-29 10:41:14,475 - INFO - 方案 #1 调整/填充后最终总时长: 3.03秒
2025-07-29 10:41:14,475 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:14,475 - INFO - ========== 当前模式：字幕 #45 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:14,475 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:14,475 - INFO - ========== 新模式：字幕 #45 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:14,475 - INFO - 
----- 处理字幕 #45 的方案 #1 -----
2025-07-29 10:41:14,475 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-29 10:41:14,476 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzfs4nf5c
2025-07-29 10:41:14,476 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\390.mp4 (确认存在: True)
2025-07-29 10:41:14,476 - INFO - 添加场景ID=390，时长=1.80秒，累计时长=1.80秒
2025-07-29 10:41:14,476 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\391.mp4 (确认存在: True)
2025-07-29 10:41:14,476 - INFO - 添加场景ID=391，时长=3.04秒，累计时长=4.84秒
2025-07-29 10:41:14,476 - INFO - 场景总时长(4.84秒)已达到音频时长(3.03秒)的1.5倍，停止添加场景
2025-07-29 10:41:14,476 - INFO - 准备合并 2 个场景文件，总时长约 4.84秒
2025-07-29 10:41:14,476 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/390.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/391.mp4'

2025-07-29 10:41:14,478 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzfs4nf5c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzfs4nf5c\temp_combined.mp4
2025-07-29 10:41:14,639 - INFO - 合并后的视频时长: 4.89秒，目标音频时长: 3.03秒
2025-07-29 10:41:14,639 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzfs4nf5c\temp_combined.mp4 -ss 0 -to 3.031 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-29 10:41:14,923 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:14,923 - INFO - 目标音频时长: 3.03秒
2025-07-29 10:41:14,923 - INFO - 实际视频时长: 3.06秒
2025-07-29 10:41:14,924 - INFO - 时长差异: 0.03秒 (1.06%)
2025-07-29 10:41:14,924 - INFO - ==========================================
2025-07-29 10:41:14,924 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:14,924 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-29 10:41:14,925 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzfs4nf5c
2025-07-29 10:41:14,983 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:14,983 - INFO -   - 音频时长: 3.03秒
2025-07-29 10:41:14,983 - INFO -   - 视频时长: 3.06秒
2025-07-29 10:41:14,983 - INFO -   - 时长差异: 0.03秒 (1.06%)
2025-07-29 10:41:14,983 - INFO - 
----- 处理字幕 #45 的方案 #2 -----
2025-07-29 10:41:14,983 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-07-29 10:41:14,984 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvr6x6nwf
2025-07-29 10:41:14,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\396.mp4 (确认存在: True)
2025-07-29 10:41:14,984 - INFO - 添加场景ID=396，时长=0.64秒，累计时长=0.64秒
2025-07-29 10:41:14,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\397.mp4 (确认存在: True)
2025-07-29 10:41:14,984 - INFO - 添加场景ID=397，时长=1.88秒，累计时长=2.52秒
2025-07-29 10:41:14,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\398.mp4 (确认存在: True)
2025-07-29 10:41:14,984 - INFO - 添加场景ID=398，时长=1.44秒，累计时长=3.96秒
2025-07-29 10:41:14,984 - INFO - 准备合并 3 个场景文件，总时长约 3.96秒
2025-07-29 10:41:14,984 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/396.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/397.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/398.mp4'

2025-07-29 10:41:14,985 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpvr6x6nwf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpvr6x6nwf\temp_combined.mp4
2025-07-29 10:41:15,159 - INFO - 合并后的视频时长: 4.03秒，目标音频时长: 3.03秒
2025-07-29 10:41:15,159 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpvr6x6nwf\temp_combined.mp4 -ss 0 -to 3.031 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-07-29 10:41:15,456 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:15,456 - INFO - 目标音频时长: 3.03秒
2025-07-29 10:41:15,456 - INFO - 实际视频时长: 3.06秒
2025-07-29 10:41:15,456 - INFO - 时长差异: 0.03秒 (1.06%)
2025-07-29 10:41:15,456 - INFO - ==========================================
2025-07-29 10:41:15,456 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:15,456 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-07-29 10:41:15,456 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvr6x6nwf
2025-07-29 10:41:15,519 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:15,519 - INFO -   - 音频时长: 3.03秒
2025-07-29 10:41:15,519 - INFO -   - 视频时长: 3.06秒
2025-07-29 10:41:15,519 - INFO -   - 时长差异: 0.03秒 (1.06%)
2025-07-29 10:41:15,520 - INFO - 
----- 处理字幕 #45 的方案 #3 -----
2025-07-29 10:41:15,520 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-07-29 10:41:15,520 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppg0d7rx8
2025-07-29 10:41:15,521 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\390.mp4 (确认存在: True)
2025-07-29 10:41:15,521 - INFO - 添加场景ID=390，时长=1.80秒，累计时长=1.80秒
2025-07-29 10:41:15,521 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\397.mp4 (确认存在: True)
2025-07-29 10:41:15,521 - INFO - 添加场景ID=397，时长=1.88秒，累计时长=3.68秒
2025-07-29 10:41:15,521 - INFO - 准备合并 2 个场景文件，总时长约 3.68秒
2025-07-29 10:41:15,521 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/390.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/397.mp4'

2025-07-29 10:41:15,522 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppg0d7rx8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppg0d7rx8\temp_combined.mp4
2025-07-29 10:41:15,669 - INFO - 合并后的视频时长: 3.73秒，目标音频时长: 3.03秒
2025-07-29 10:41:15,669 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppg0d7rx8\temp_combined.mp4 -ss 0 -to 3.031 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-07-29 10:41:16,009 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:16,009 - INFO - 目标音频时长: 3.03秒
2025-07-29 10:41:16,009 - INFO - 实际视频时长: 3.06秒
2025-07-29 10:41:16,009 - INFO - 时长差异: 0.03秒 (1.06%)
2025-07-29 10:41:16,009 - INFO - ==========================================
2025-07-29 10:41:16,009 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:16,009 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-07-29 10:41:16,010 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppg0d7rx8
2025-07-29 10:41:16,059 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:16,059 - INFO -   - 音频时长: 3.03秒
2025-07-29 10:41:16,059 - INFO -   - 视频时长: 3.06秒
2025-07-29 10:41:16,059 - INFO -   - 时长差异: 0.03秒 (1.06%)
2025-07-29 10:41:16,059 - INFO - 
字幕 #45 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:16,059 - INFO - 生成的视频文件:
2025-07-29 10:41:16,059 - INFO -   1. F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-29 10:41:16,059 - INFO -   2. F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-07-29 10:41:16,059 - INFO -   3. F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-07-29 10:41:16,059 - INFO - ========== 字幕 #45 处理结束 ==========

