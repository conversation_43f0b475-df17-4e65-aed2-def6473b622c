2025-07-29 10:41:58,728 - INFO - ========== 字幕 #71 处理开始 ==========
2025-07-29 10:41:58,728 - INFO - 字幕内容: 他残忍戳破她的暗恋，说和离后便可永远留在他身边。
2025-07-29 10:41:58,728 - INFO - 字幕序号: [1324, 1329]
2025-07-29 10:41:58,729 - INFO - 音频文件详情:
2025-07-29 10:41:58,729 - INFO -   - 路径: output\71.wav
2025-07-29 10:41:58,729 - INFO -   - 时长: 4.14秒
2025-07-29 10:41:58,729 - INFO -   - 验证音频时长: 4.14秒
2025-07-29 10:41:58,730 - INFO - 字幕时间戳信息:
2025-07-29 10:41:58,730 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:58,730 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:58,730 - INFO -   - 根据生成的音频时长(4.14秒)已调整字幕时间戳
2025-07-29 10:41:58,730 - INFO - ========== 新模式：为字幕 #71 生成4套场景方案 ==========
2025-07-29 10:41:58,730 - INFO - 字幕序号列表: [1324, 1329]
2025-07-29 10:41:58,730 - INFO - 
--- 生成方案 #1：基于字幕序号 #1324 ---
2025-07-29 10:41:58,730 - INFO - 开始为单个字幕序号 #1324 匹配场景，目标时长: 4.14秒
2025-07-29 10:41:58,730 - INFO - 开始查找字幕序号 [1324] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:58,731 - INFO - 找到related_overlap场景: scene_id=1668, 字幕#1324
2025-07-29 10:41:58,731 - INFO - 找到related_overlap场景: scene_id=1669, 字幕#1324
2025-07-29 10:41:58,734 - INFO - 字幕 #1324 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:58,734 - INFO - 字幕序号 #1324 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:58,734 - INFO - 选择第一个overlap场景作为起点: scene_id=1668
2025-07-29 10:41:58,734 - INFO - 添加起点场景: scene_id=1668, 时长=3.36秒, 累计时长=3.36秒
2025-07-29 10:41:58,734 - INFO - 起点场景时长不足，需要延伸填充 0.78秒
2025-07-29 10:41:58,734 - INFO - 起点场景在原始列表中的索引: 1667
2025-07-29 10:41:58,734 - INFO - 延伸添加场景: scene_id=1669 (裁剪至 0.78秒)
2025-07-29 10:41:58,734 - INFO - 累计时长: 4.14秒
2025-07-29 10:41:58,734 - INFO - 字幕序号 #1324 场景匹配完成，共选择 2 个场景，总时长: 4.14秒
2025-07-29 10:41:58,734 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:58,734 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:58,734 - INFO - 
--- 生成方案 #2：基于字幕序号 #1329 ---
2025-07-29 10:41:58,734 - INFO - 开始为单个字幕序号 #1329 匹配场景，目标时长: 4.14秒
2025-07-29 10:41:58,734 - INFO - 开始查找字幕序号 [1329] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:58,735 - INFO - 找到related_overlap场景: scene_id=1670, 字幕#1329
2025-07-29 10:41:58,736 - INFO - 找到related_between场景: scene_id=1671, 字幕#1329
2025-07-29 10:41:58,736 - INFO - 字幕 #1329 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:58,736 - INFO - 字幕序号 #1329 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 10:41:58,737 - INFO - 选择第一个overlap场景作为起点: scene_id=1670
2025-07-29 10:41:58,737 - INFO - 添加起点场景: scene_id=1670, 时长=4.28秒, 累计时长=4.28秒
2025-07-29 10:41:58,737 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:58,737 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:41:58,737 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:58,737 - INFO - ========== 当前模式：为字幕 #71 生成 1 套场景方案 ==========
2025-07-29 10:41:58,737 - INFO - 开始查找字幕序号 [1324, 1329] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:58,737 - INFO - 找到related_overlap场景: scene_id=1668, 字幕#1324
2025-07-29 10:41:58,737 - INFO - 找到related_overlap场景: scene_id=1669, 字幕#1324
2025-07-29 10:41:58,737 - INFO - 找到related_overlap场景: scene_id=1670, 字幕#1329
2025-07-29 10:41:58,738 - INFO - 找到related_between场景: scene_id=1671, 字幕#1329
2025-07-29 10:41:58,739 - INFO - 字幕 #1324 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:58,739 - INFO - 字幕 #1329 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:58,739 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 10:41:58,739 - INFO - 开始生成方案 #1
2025-07-29 10:41:58,739 - INFO - 方案 #1: 为字幕#1324选择初始化overlap场景id=1669
2025-07-29 10:41:58,739 - INFO - 方案 #1: 为字幕#1329选择初始化overlap场景id=1670
2025-07-29 10:41:58,739 - INFO - 方案 #1: 初始选择后，当前总时长=6.40秒
2025-07-29 10:41:58,739 - INFO - 方案 #1: 额外between选择后，当前总时长=6.40秒
2025-07-29 10:41:58,739 - INFO - 方案 #1: 场景总时长(6.40秒)大于音频时长(4.14秒)，需要裁剪
2025-07-29 10:41:58,739 - INFO - 调整前总时长: 6.40秒, 目标时长: 4.14秒
2025-07-29 10:41:58,739 - INFO - 需要裁剪 2.26秒
2025-07-29 10:41:58,739 - INFO - 裁剪最长场景ID=1670：从4.28秒裁剪至2.02秒
2025-07-29 10:41:58,739 - INFO - 调整后总时长: 4.14秒，与目标时长差异: 0.00秒
2025-07-29 10:41:58,739 - INFO - 方案 #1 调整/填充后最终总时长: 4.14秒
2025-07-29 10:41:58,739 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:58,739 - INFO - ========== 当前模式：字幕 #71 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:58,739 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:58,739 - INFO - ========== 新模式：字幕 #71 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:58,740 - INFO - 
----- 处理字幕 #71 的方案 #1 -----
2025-07-29 10:41:58,740 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-07-29 10:41:58,740 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnyr45pc0
2025-07-29 10:41:58,741 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1668.mp4 (确认存在: True)
2025-07-29 10:41:58,741 - INFO - 添加场景ID=1668，时长=3.36秒，累计时长=3.36秒
2025-07-29 10:41:58,741 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1669.mp4 (确认存在: True)
2025-07-29 10:41:58,741 - INFO - 添加场景ID=1669，时长=2.12秒，累计时长=5.48秒
2025-07-29 10:41:58,741 - INFO - 准备合并 2 个场景文件，总时长约 5.48秒
2025-07-29 10:41:58,741 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1668.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1669.mp4'

2025-07-29 10:41:58,741 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnyr45pc0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnyr45pc0\temp_combined.mp4
2025-07-29 10:41:58,891 - INFO - 合并后的视频时长: 5.53秒，目标音频时长: 4.14秒
2025-07-29 10:41:58,891 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnyr45pc0\temp_combined.mp4 -ss 0 -to 4.139 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-07-29 10:41:59,197 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:59,197 - INFO - 目标音频时长: 4.14秒
2025-07-29 10:41:59,197 - INFO - 实际视频时长: 4.18秒
2025-07-29 10:41:59,197 - INFO - 时长差异: 0.04秒 (1.06%)
2025-07-29 10:41:59,197 - INFO - ==========================================
2025-07-29 10:41:59,197 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:59,197 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-07-29 10:41:59,197 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnyr45pc0
2025-07-29 10:41:59,253 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:59,254 - INFO -   - 音频时长: 4.14秒
2025-07-29 10:41:59,254 - INFO -   - 视频时长: 4.18秒
2025-07-29 10:41:59,254 - INFO -   - 时长差异: 0.04秒 (1.06%)
2025-07-29 10:41:59,254 - INFO - 
----- 处理字幕 #71 的方案 #2 -----
2025-07-29 10:41:59,254 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-07-29 10:41:59,254 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq1ey_2ws
2025-07-29 10:41:59,255 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1670.mp4 (确认存在: True)
2025-07-29 10:41:59,255 - INFO - 添加场景ID=1670，时长=4.28秒，累计时长=4.28秒
2025-07-29 10:41:59,255 - INFO - 准备合并 1 个场景文件，总时长约 4.28秒
2025-07-29 10:41:59,256 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1670.mp4'

2025-07-29 10:41:59,256 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpq1ey_2ws\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpq1ey_2ws\temp_combined.mp4
2025-07-29 10:41:59,411 - INFO - 合并后的视频时长: 4.30秒，目标音频时长: 4.14秒
2025-07-29 10:41:59,411 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpq1ey_2ws\temp_combined.mp4 -ss 0 -to 4.139 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-07-29 10:41:59,771 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:59,771 - INFO - 目标音频时长: 4.14秒
2025-07-29 10:41:59,771 - INFO - 实际视频时长: 4.18秒
2025-07-29 10:41:59,771 - INFO - 时长差异: 0.04秒 (1.06%)
2025-07-29 10:41:59,771 - INFO - ==========================================
2025-07-29 10:41:59,771 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:59,771 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-07-29 10:41:59,772 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq1ey_2ws
2025-07-29 10:41:59,837 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:59,837 - INFO -   - 音频时长: 4.14秒
2025-07-29 10:41:59,837 - INFO -   - 视频时长: 4.18秒
2025-07-29 10:41:59,837 - INFO -   - 时长差异: 0.04秒 (1.06%)
2025-07-29 10:41:59,837 - INFO - 
----- 处理字幕 #71 的方案 #3 -----
2025-07-29 10:41:59,837 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\71_3.mp4
2025-07-29 10:41:59,837 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfvcml6ih
2025-07-29 10:41:59,838 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1669.mp4 (确认存在: True)
2025-07-29 10:41:59,838 - INFO - 添加场景ID=1669，时长=2.12秒，累计时长=2.12秒
2025-07-29 10:41:59,838 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1670.mp4 (确认存在: True)
2025-07-29 10:41:59,838 - INFO - 添加场景ID=1670，时长=4.28秒，累计时长=6.40秒
2025-07-29 10:41:59,838 - INFO - 场景总时长(6.40秒)已达到音频时长(4.14秒)的1.5倍，停止添加场景
2025-07-29 10:41:59,838 - INFO - 准备合并 2 个场景文件，总时长约 6.40秒
2025-07-29 10:41:59,838 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1669.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1670.mp4'

2025-07-29 10:41:59,838 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfvcml6ih\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfvcml6ih\temp_combined.mp4
2025-07-29 10:42:00,001 - INFO - 合并后的视频时长: 6.45秒，目标音频时长: 4.14秒
2025-07-29 10:42:00,001 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfvcml6ih\temp_combined.mp4 -ss 0 -to 4.139 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\71_3.mp4
2025-07-29 10:42:00,311 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:42:00,311 - INFO - 目标音频时长: 4.14秒
2025-07-29 10:42:00,311 - INFO - 实际视频时长: 4.18秒
2025-07-29 10:42:00,311 - INFO - 时长差异: 0.04秒 (1.06%)
2025-07-29 10:42:00,311 - INFO - ==========================================
2025-07-29 10:42:00,311 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:42:00,311 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\71_3.mp4
2025-07-29 10:42:00,312 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfvcml6ih
2025-07-29 10:42:00,368 - INFO - 方案 #3 处理完成:
2025-07-29 10:42:00,368 - INFO -   - 音频时长: 4.14秒
2025-07-29 10:42:00,368 - INFO -   - 视频时长: 4.18秒
2025-07-29 10:42:00,368 - INFO -   - 时长差异: 0.04秒 (1.06%)
2025-07-29 10:42:00,368 - INFO - 
字幕 #71 处理完成，成功生成 3/3 套方案
2025-07-29 10:42:00,368 - INFO - 生成的视频文件:
2025-07-29 10:42:00,368 - INFO -   1. F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-07-29 10:42:00,368 - INFO -   2. F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-07-29 10:42:00,368 - INFO -   3. F:/github/aicut_auto/newcut_ai\71_3.mp4
2025-07-29 10:42:00,368 - INFO - ========== 字幕 #71 处理结束 ==========

