2025-07-29 10:40:32,231 - INFO - ========== 字幕 #13 处理开始 ==========
2025-07-29 10:40:32,231 - INFO - 字幕内容: 见哥哥与姨娘同车，她心酸表示要换车，被哥哥喝止。
2025-07-29 10:40:32,231 - INFO - 字幕序号: [56, 60]
2025-07-29 10:40:32,231 - INFO - 音频文件详情:
2025-07-29 10:40:32,231 - INFO -   - 路径: output\13.wav
2025-07-29 10:40:32,231 - INFO -   - 时长: 4.55秒
2025-07-29 10:40:32,231 - INFO -   - 验证音频时长: 4.55秒
2025-07-29 10:40:32,231 - INFO - 字幕时间戳信息:
2025-07-29 10:40:32,232 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:32,232 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:32,232 - INFO -   - 根据生成的音频时长(4.55秒)已调整字幕时间戳
2025-07-29 10:40:32,232 - INFO - ========== 新模式：为字幕 #13 生成4套场景方案 ==========
2025-07-29 10:40:32,232 - INFO - 字幕序号列表: [56, 60]
2025-07-29 10:40:32,232 - INFO - 
--- 生成方案 #1：基于字幕序号 #56 ---
2025-07-29 10:40:32,232 - INFO - 开始为单个字幕序号 #56 匹配场景，目标时长: 4.55秒
2025-07-29 10:40:32,232 - INFO - 开始查找字幕序号 [56] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:32,232 - INFO - 找到related_overlap场景: scene_id=71, 字幕#56
2025-07-29 10:40:32,234 - INFO - 字幕 #56 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:32,234 - INFO - 字幕序号 #56 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:32,234 - INFO - 选择第一个overlap场景作为起点: scene_id=71
2025-07-29 10:40:32,234 - INFO - 添加起点场景: scene_id=71, 时长=8.12秒, 累计时长=8.12秒
2025-07-29 10:40:32,234 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:40:32,234 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:40:32,234 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:40:32,234 - INFO - 
--- 生成方案 #2：基于字幕序号 #60 ---
2025-07-29 10:40:32,234 - INFO - 开始为单个字幕序号 #60 匹配场景，目标时长: 4.55秒
2025-07-29 10:40:32,234 - INFO - 开始查找字幕序号 [60] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:32,234 - INFO - 找到related_overlap场景: scene_id=73, 字幕#60
2025-07-29 10:40:32,235 - INFO - 找到related_between场景: scene_id=74, 字幕#60
2025-07-29 10:40:32,235 - INFO - 找到related_between场景: scene_id=75, 字幕#60
2025-07-29 10:40:32,235 - INFO - 找到related_between场景: scene_id=76, 字幕#60
2025-07-29 10:40:32,235 - INFO - 找到related_between场景: scene_id=77, 字幕#60
2025-07-29 10:40:32,235 - INFO - 找到related_between场景: scene_id=78, 字幕#60
2025-07-29 10:40:32,235 - INFO - 找到related_between场景: scene_id=79, 字幕#60
2025-07-29 10:40:32,235 - INFO - 找到related_between场景: scene_id=80, 字幕#60
2025-07-29 10:40:32,235 - INFO - 找到related_between场景: scene_id=81, 字幕#60
2025-07-29 10:40:32,235 - INFO - 找到related_between场景: scene_id=82, 字幕#60
2025-07-29 10:40:32,235 - INFO - 找到related_between场景: scene_id=83, 字幕#60
2025-07-29 10:40:32,236 - INFO - 字幕 #60 找到 1 个overlap场景, 10 个between场景
2025-07-29 10:40:32,236 - INFO - 字幕序号 #60 找到 1 个可用overlap场景, 10 个可用between场景
2025-07-29 10:40:32,236 - INFO - 选择第一个overlap场景作为起点: scene_id=73
2025-07-29 10:40:32,236 - INFO - 添加起点场景: scene_id=73, 时长=2.92秒, 累计时长=2.92秒
2025-07-29 10:40:32,236 - INFO - 起点场景时长不足，需要延伸填充 1.64秒
2025-07-29 10:40:32,236 - INFO - 起点场景在原始列表中的索引: 72
2025-07-29 10:40:32,236 - INFO - 延伸添加场景: scene_id=74 (裁剪至 1.64秒)
2025-07-29 10:40:32,236 - INFO - 累计时长: 4.55秒
2025-07-29 10:40:32,236 - INFO - 字幕序号 #60 场景匹配完成，共选择 2 个场景，总时长: 4.55秒
2025-07-29 10:40:32,236 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:32,236 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:32,236 - INFO - ========== 当前模式：为字幕 #13 生成 1 套场景方案 ==========
2025-07-29 10:40:32,236 - INFO - 开始查找字幕序号 [56, 60] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:32,236 - INFO - 找到related_overlap场景: scene_id=71, 字幕#56
2025-07-29 10:40:32,236 - INFO - 找到related_overlap场景: scene_id=73, 字幕#60
2025-07-29 10:40:32,236 - INFO - 找到related_between场景: scene_id=74, 字幕#60
2025-07-29 10:40:32,236 - INFO - 找到related_between场景: scene_id=75, 字幕#60
2025-07-29 10:40:32,236 - INFO - 找到related_between场景: scene_id=76, 字幕#60
2025-07-29 10:40:32,236 - INFO - 找到related_between场景: scene_id=77, 字幕#60
2025-07-29 10:40:32,237 - INFO - 找到related_between场景: scene_id=78, 字幕#60
2025-07-29 10:40:32,237 - INFO - 找到related_between场景: scene_id=79, 字幕#60
2025-07-29 10:40:32,237 - INFO - 找到related_between场景: scene_id=80, 字幕#60
2025-07-29 10:40:32,237 - INFO - 找到related_between场景: scene_id=81, 字幕#60
2025-07-29 10:40:32,237 - INFO - 找到related_between场景: scene_id=82, 字幕#60
2025-07-29 10:40:32,237 - INFO - 找到related_between场景: scene_id=83, 字幕#60
2025-07-29 10:40:32,237 - INFO - 字幕 #56 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:32,237 - INFO - 字幕 #60 找到 1 个overlap场景, 10 个between场景
2025-07-29 10:40:32,237 - INFO - 共收集 2 个未使用的overlap场景和 10 个未使用的between场景
2025-07-29 10:40:32,237 - INFO - 开始生成方案 #1
2025-07-29 10:40:32,237 - INFO - 方案 #1: 为字幕#56选择初始化overlap场景id=71
2025-07-29 10:40:32,237 - INFO - 方案 #1: 为字幕#60选择初始化overlap场景id=73
2025-07-29 10:40:32,237 - INFO - 方案 #1: 初始选择后，当前总时长=11.04秒
2025-07-29 10:40:32,238 - INFO - 方案 #1: 额外between选择后，当前总时长=11.04秒
2025-07-29 10:40:32,238 - INFO - 方案 #1: 场景总时长(11.04秒)大于音频时长(4.55秒)，需要裁剪
2025-07-29 10:40:32,238 - INFO - 调整前总时长: 11.04秒, 目标时长: 4.55秒
2025-07-29 10:40:32,238 - INFO - 需要裁剪 6.48秒
2025-07-29 10:40:32,238 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:40:32,238 - INFO - 裁剪场景ID=71：从8.12秒裁剪至2.44秒
2025-07-29 10:40:32,238 - INFO - 裁剪场景ID=73：从2.92秒裁剪至2.44秒
2025-07-29 10:40:32,238 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.32秒
2025-07-29 10:40:32,238 - INFO - 调整后总时长: 4.87秒，与目标时长差异: 0.32秒
2025-07-29 10:40:32,238 - INFO - 方案 #1 调整/填充后最终总时长: 4.87秒
2025-07-29 10:40:32,238 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:32,238 - INFO - ========== 当前模式：字幕 #13 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:32,238 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:32,238 - INFO - ========== 新模式：字幕 #13 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:32,238 - INFO - 
----- 处理字幕 #13 的方案 #1 -----
2025-07-29 10:40:32,238 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-29 10:40:32,238 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp19ohll4p
2025-07-29 10:40:32,239 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\71.mp4 (确认存在: True)
2025-07-29 10:40:32,239 - INFO - 添加场景ID=71，时长=8.12秒，累计时长=8.12秒
2025-07-29 10:40:32,239 - INFO - 场景总时长(8.12秒)已达到音频时长(4.55秒)的1.5倍，停止添加场景
2025-07-29 10:40:32,239 - INFO - 准备合并 1 个场景文件，总时长约 8.12秒
2025-07-29 10:40:32,239 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/71.mp4'

2025-07-29 10:40:32,239 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp19ohll4p\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp19ohll4p\temp_combined.mp4
2025-07-29 10:40:32,358 - INFO - 合并后的视频时长: 8.14秒，目标音频时长: 4.55秒
2025-07-29 10:40:32,359 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp19ohll4p\temp_combined.mp4 -ss 0 -to 4.555 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-29 10:40:32,641 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:32,641 - INFO - 目标音频时长: 4.55秒
2025-07-29 10:40:32,641 - INFO - 实际视频时长: 4.58秒
2025-07-29 10:40:32,641 - INFO - 时长差异: 0.03秒 (0.61%)
2025-07-29 10:40:32,641 - INFO - ==========================================
2025-07-29 10:40:32,641 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:32,641 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-29 10:40:32,642 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp19ohll4p
2025-07-29 10:40:32,686 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:32,686 - INFO -   - 音频时长: 4.55秒
2025-07-29 10:40:32,686 - INFO -   - 视频时长: 4.58秒
2025-07-29 10:40:32,686 - INFO -   - 时长差异: 0.03秒 (0.61%)
2025-07-29 10:40:32,686 - INFO - 
----- 处理字幕 #13 的方案 #2 -----
2025-07-29 10:40:32,686 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-07-29 10:40:32,687 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjqkutbk8
2025-07-29 10:40:32,687 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\73.mp4 (确认存在: True)
2025-07-29 10:40:32,687 - INFO - 添加场景ID=73，时长=2.92秒，累计时长=2.92秒
2025-07-29 10:40:32,687 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\74.mp4 (确认存在: True)
2025-07-29 10:40:32,687 - INFO - 添加场景ID=74，时长=1.88秒，累计时长=4.80秒
2025-07-29 10:40:32,687 - INFO - 准备合并 2 个场景文件，总时长约 4.80秒
2025-07-29 10:40:32,687 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/73.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/74.mp4'

2025-07-29 10:40:32,687 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjqkutbk8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjqkutbk8\temp_combined.mp4
2025-07-29 10:40:32,805 - INFO - 合并后的视频时长: 4.85秒，目标音频时长: 4.55秒
2025-07-29 10:40:32,805 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjqkutbk8\temp_combined.mp4 -ss 0 -to 4.555 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-07-29 10:40:33,121 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:33,121 - INFO - 目标音频时长: 4.55秒
2025-07-29 10:40:33,121 - INFO - 实际视频时长: 4.58秒
2025-07-29 10:40:33,121 - INFO - 时长差异: 0.03秒 (0.61%)
2025-07-29 10:40:33,121 - INFO - ==========================================
2025-07-29 10:40:33,121 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:33,121 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-07-29 10:40:33,122 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjqkutbk8
2025-07-29 10:40:33,163 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:33,163 - INFO -   - 音频时长: 4.55秒
2025-07-29 10:40:33,163 - INFO -   - 视频时长: 4.58秒
2025-07-29 10:40:33,163 - INFO -   - 时长差异: 0.03秒 (0.61%)
2025-07-29 10:40:33,163 - INFO - 
----- 处理字幕 #13 的方案 #3 -----
2025-07-29 10:40:33,163 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-07-29 10:40:33,164 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptqfhrn7v
2025-07-29 10:40:33,164 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\71.mp4 (确认存在: True)
2025-07-29 10:40:33,164 - INFO - 添加场景ID=71，时长=8.12秒，累计时长=8.12秒
2025-07-29 10:40:33,164 - INFO - 场景总时长(8.12秒)已达到音频时长(4.55秒)的1.5倍，停止添加场景
2025-07-29 10:40:33,164 - INFO - 准备合并 1 个场景文件，总时长约 8.12秒
2025-07-29 10:40:33,164 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/71.mp4'

2025-07-29 10:40:33,165 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmptqfhrn7v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmptqfhrn7v\temp_combined.mp4
2025-07-29 10:40:33,283 - INFO - 合并后的视频时长: 8.14秒，目标音频时长: 4.55秒
2025-07-29 10:40:33,283 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmptqfhrn7v\temp_combined.mp4 -ss 0 -to 4.555 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-07-29 10:40:33,599 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:33,599 - INFO - 目标音频时长: 4.55秒
2025-07-29 10:40:33,599 - INFO - 实际视频时长: 4.58秒
2025-07-29 10:40:33,599 - INFO - 时长差异: 0.03秒 (0.61%)
2025-07-29 10:40:33,599 - INFO - ==========================================
2025-07-29 10:40:33,599 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:33,599 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-07-29 10:40:33,599 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptqfhrn7v
2025-07-29 10:40:33,640 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:33,640 - INFO -   - 音频时长: 4.55秒
2025-07-29 10:40:33,640 - INFO -   - 视频时长: 4.58秒
2025-07-29 10:40:33,640 - INFO -   - 时长差异: 0.03秒 (0.61%)
2025-07-29 10:40:33,640 - INFO - 
字幕 #13 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:33,640 - INFO - 生成的视频文件:
2025-07-29 10:40:33,640 - INFO -   1. F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-07-29 10:40:33,640 - INFO -   2. F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-07-29 10:40:33,640 - INFO -   3. F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-07-29 10:40:33,640 - INFO - ========== 字幕 #13 处理结束 ==========

