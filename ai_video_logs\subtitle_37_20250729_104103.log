2025-07-29 10:41:03,712 - INFO - ========== 字幕 #37 处理开始 ==========
2025-07-29 10:41:03,712 - INFO - 字幕内容: 他迅速制服歹人，得知她喝了桃花酿，神色凝重。
2025-07-29 10:41:03,712 - INFO - 字幕序号: [215, 219]
2025-07-29 10:41:03,713 - INFO - 音频文件详情:
2025-07-29 10:41:03,713 - INFO -   - 路径: output\37.wav
2025-07-29 10:41:03,713 - INFO -   - 时长: 3.88秒
2025-07-29 10:41:03,713 - INFO -   - 验证音频时长: 3.88秒
2025-07-29 10:41:03,713 - INFO - 字幕时间戳信息:
2025-07-29 10:41:03,713 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:03,713 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:03,713 - INFO -   - 根据生成的音频时长(3.88秒)已调整字幕时间戳
2025-07-29 10:41:03,713 - INFO - ========== 新模式：为字幕 #37 生成4套场景方案 ==========
2025-07-29 10:41:03,713 - INFO - 字幕序号列表: [215, 219]
2025-07-29 10:41:03,713 - INFO - 
--- 生成方案 #1：基于字幕序号 #215 ---
2025-07-29 10:41:03,713 - INFO - 开始为单个字幕序号 #215 匹配场景，目标时长: 3.88秒
2025-07-29 10:41:03,713 - INFO - 开始查找字幕序号 [215] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:03,713 - INFO - 找到related_overlap场景: scene_id=305, 字幕#215
2025-07-29 10:41:03,714 - INFO - 找到related_between场景: scene_id=297, 字幕#215
2025-07-29 10:41:03,714 - INFO - 找到related_between场景: scene_id=298, 字幕#215
2025-07-29 10:41:03,714 - INFO - 找到related_between场景: scene_id=299, 字幕#215
2025-07-29 10:41:03,714 - INFO - 找到related_between场景: scene_id=300, 字幕#215
2025-07-29 10:41:03,714 - INFO - 找到related_between场景: scene_id=301, 字幕#215
2025-07-29 10:41:03,714 - INFO - 找到related_between场景: scene_id=302, 字幕#215
2025-07-29 10:41:03,714 - INFO - 找到related_between场景: scene_id=303, 字幕#215
2025-07-29 10:41:03,714 - INFO - 找到related_between场景: scene_id=304, 字幕#215
2025-07-29 10:41:03,715 - INFO - 字幕 #215 找到 1 个overlap场景, 8 个between场景
2025-07-29 10:41:03,715 - INFO - 字幕序号 #215 找到 1 个可用overlap场景, 8 个可用between场景
2025-07-29 10:41:03,715 - INFO - 选择第一个overlap场景作为起点: scene_id=305
2025-07-29 10:41:03,715 - INFO - 添加起点场景: scene_id=305, 时长=1.96秒, 累计时长=1.96秒
2025-07-29 10:41:03,715 - INFO - 起点场景时长不足，需要延伸填充 1.92秒
2025-07-29 10:41:03,715 - INFO - 起点场景在原始列表中的索引: 304
2025-07-29 10:41:03,715 - INFO - 延伸添加场景: scene_id=306 (裁剪至 1.92秒)
2025-07-29 10:41:03,715 - INFO - 累计时长: 3.88秒
2025-07-29 10:41:03,715 - INFO - 字幕序号 #215 场景匹配完成，共选择 2 个场景，总时长: 3.88秒
2025-07-29 10:41:03,715 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:03,715 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:03,715 - INFO - 
--- 生成方案 #2：基于字幕序号 #219 ---
2025-07-29 10:41:03,715 - INFO - 开始为单个字幕序号 #219 匹配场景，目标时长: 3.88秒
2025-07-29 10:41:03,715 - INFO - 开始查找字幕序号 [219] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:03,715 - INFO - 找到related_overlap场景: scene_id=308, 字幕#219
2025-07-29 10:41:03,715 - INFO - 找到related_overlap场景: scene_id=309, 字幕#219
2025-07-29 10:41:03,716 - INFO - 字幕 #219 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:03,716 - INFO - 字幕序号 #219 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:03,716 - INFO - 选择第一个overlap场景作为起点: scene_id=308
2025-07-29 10:41:03,716 - INFO - 添加起点场景: scene_id=308, 时长=1.76秒, 累计时长=1.76秒
2025-07-29 10:41:03,716 - INFO - 起点场景时长不足，需要延伸填充 2.12秒
2025-07-29 10:41:03,716 - INFO - 起点场景在原始列表中的索引: 307
2025-07-29 10:41:03,716 - INFO - 延伸添加场景: scene_id=309 (裁剪至 2.12秒)
2025-07-29 10:41:03,716 - INFO - 累计时长: 3.88秒
2025-07-29 10:41:03,716 - INFO - 字幕序号 #219 场景匹配完成，共选择 2 个场景，总时长: 3.88秒
2025-07-29 10:41:03,716 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:03,716 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:03,716 - INFO - ========== 当前模式：为字幕 #37 生成 1 套场景方案 ==========
2025-07-29 10:41:03,716 - INFO - 开始查找字幕序号 [215, 219] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:03,716 - INFO - 找到related_overlap场景: scene_id=305, 字幕#215
2025-07-29 10:41:03,716 - INFO - 找到related_overlap场景: scene_id=308, 字幕#219
2025-07-29 10:41:03,716 - INFO - 找到related_overlap场景: scene_id=309, 字幕#219
2025-07-29 10:41:03,716 - INFO - 找到related_between场景: scene_id=297, 字幕#215
2025-07-29 10:41:03,716 - INFO - 找到related_between场景: scene_id=298, 字幕#215
2025-07-29 10:41:03,716 - INFO - 找到related_between场景: scene_id=299, 字幕#215
2025-07-29 10:41:03,716 - INFO - 找到related_between场景: scene_id=300, 字幕#215
2025-07-29 10:41:03,716 - INFO - 找到related_between场景: scene_id=301, 字幕#215
2025-07-29 10:41:03,716 - INFO - 找到related_between场景: scene_id=302, 字幕#215
2025-07-29 10:41:03,716 - INFO - 找到related_between场景: scene_id=303, 字幕#215
2025-07-29 10:41:03,716 - INFO - 找到related_between场景: scene_id=304, 字幕#215
2025-07-29 10:41:03,718 - INFO - 字幕 #215 找到 1 个overlap场景, 8 个between场景
2025-07-29 10:41:03,718 - INFO - 字幕 #219 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:03,718 - INFO - 共收集 3 个未使用的overlap场景和 8 个未使用的between场景
2025-07-29 10:41:03,718 - INFO - 开始生成方案 #1
2025-07-29 10:41:03,718 - INFO - 方案 #1: 为字幕#215选择初始化overlap场景id=305
2025-07-29 10:41:03,718 - INFO - 方案 #1: 为字幕#219选择初始化overlap场景id=308
2025-07-29 10:41:03,718 - INFO - 方案 #1: 初始选择后，当前总时长=3.72秒
2025-07-29 10:41:03,718 - INFO - 方案 #1: 额外添加overlap场景id=309, 当前总时长=6.28秒
2025-07-29 10:41:03,718 - INFO - 方案 #1: 额外between选择后，当前总时长=6.28秒
2025-07-29 10:41:03,718 - INFO - 方案 #1: 场景总时长(6.28秒)大于音频时长(3.88秒)，需要裁剪
2025-07-29 10:41:03,718 - INFO - 调整前总时长: 6.28秒, 目标时长: 3.88秒
2025-07-29 10:41:03,718 - INFO - 需要裁剪 2.40秒
2025-07-29 10:41:03,718 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:03,718 - INFO - 裁剪场景ID=309：从2.56秒裁剪至1.00秒
2025-07-29 10:41:03,718 - INFO - 裁剪场景ID=305：从1.96秒裁剪至1.12秒
2025-07-29 10:41:03,718 - INFO - 调整后总时长: 3.88秒，与目标时长差异: 0.00秒
2025-07-29 10:41:03,718 - INFO - 方案 #1 调整/填充后最终总时长: 3.88秒
2025-07-29 10:41:03,718 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:03,718 - INFO - ========== 当前模式：字幕 #37 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:03,718 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:03,718 - INFO - ========== 新模式：字幕 #37 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:03,718 - INFO - 
----- 处理字幕 #37 的方案 #1 -----
2025-07-29 10:41:03,718 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-29 10:41:03,719 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5alatk7w
2025-07-29 10:41:03,719 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\305.mp4 (确认存在: True)
2025-07-29 10:41:03,719 - INFO - 添加场景ID=305，时长=1.96秒，累计时长=1.96秒
2025-07-29 10:41:03,719 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\306.mp4 (确认存在: True)
2025-07-29 10:41:03,719 - INFO - 添加场景ID=306，时长=5.04秒，累计时长=7.00秒
2025-07-29 10:41:03,719 - INFO - 场景总时长(7.00秒)已达到音频时长(3.88秒)的1.5倍，停止添加场景
2025-07-29 10:41:03,719 - INFO - 准备合并 2 个场景文件，总时长约 7.00秒
2025-07-29 10:41:03,719 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/305.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/306.mp4'

2025-07-29 10:41:03,719 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5alatk7w\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5alatk7w\temp_combined.mp4
2025-07-29 10:41:03,834 - INFO - 合并后的视频时长: 7.05秒，目标音频时长: 3.88秒
2025-07-29 10:41:03,834 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5alatk7w\temp_combined.mp4 -ss 0 -to 3.876 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-29 10:41:04,104 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:04,105 - INFO - 目标音频时长: 3.88秒
2025-07-29 10:41:04,105 - INFO - 实际视频时长: 3.90秒
2025-07-29 10:41:04,105 - INFO - 时长差异: 0.03秒 (0.70%)
2025-07-29 10:41:04,105 - INFO - ==========================================
2025-07-29 10:41:04,105 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:04,105 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-29 10:41:04,105 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5alatk7w
2025-07-29 10:41:04,146 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:04,146 - INFO -   - 音频时长: 3.88秒
2025-07-29 10:41:04,146 - INFO -   - 视频时长: 3.90秒
2025-07-29 10:41:04,146 - INFO -   - 时长差异: 0.03秒 (0.70%)
2025-07-29 10:41:04,146 - INFO - 
----- 处理字幕 #37 的方案 #2 -----
2025-07-29 10:41:04,146 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-07-29 10:41:04,148 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpa5uaj2wy
2025-07-29 10:41:04,148 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\308.mp4 (确认存在: True)
2025-07-29 10:41:04,148 - INFO - 添加场景ID=308，时长=1.76秒，累计时长=1.76秒
2025-07-29 10:41:04,148 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\309.mp4 (确认存在: True)
2025-07-29 10:41:04,148 - INFO - 添加场景ID=309，时长=2.56秒，累计时长=4.32秒
2025-07-29 10:41:04,148 - INFO - 准备合并 2 个场景文件，总时长约 4.32秒
2025-07-29 10:41:04,148 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/308.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/309.mp4'

2025-07-29 10:41:04,149 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpa5uaj2wy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpa5uaj2wy\temp_combined.mp4
2025-07-29 10:41:04,279 - INFO - 合并后的视频时长: 4.37秒，目标音频时长: 3.88秒
2025-07-29 10:41:04,280 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpa5uaj2wy\temp_combined.mp4 -ss 0 -to 3.876 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-07-29 10:41:04,541 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:04,541 - INFO - 目标音频时长: 3.88秒
2025-07-29 10:41:04,541 - INFO - 实际视频时长: 3.90秒
2025-07-29 10:41:04,541 - INFO - 时长差异: 0.03秒 (0.70%)
2025-07-29 10:41:04,541 - INFO - ==========================================
2025-07-29 10:41:04,541 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:04,541 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-07-29 10:41:04,541 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpa5uaj2wy
2025-07-29 10:41:04,584 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:04,584 - INFO -   - 音频时长: 3.88秒
2025-07-29 10:41:04,584 - INFO -   - 视频时长: 3.90秒
2025-07-29 10:41:04,584 - INFO -   - 时长差异: 0.03秒 (0.70%)
2025-07-29 10:41:04,584 - INFO - 
----- 处理字幕 #37 的方案 #3 -----
2025-07-29 10:41:04,584 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-07-29 10:41:04,584 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxq7ior6v
2025-07-29 10:41:04,585 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\305.mp4 (确认存在: True)
2025-07-29 10:41:04,585 - INFO - 添加场景ID=305，时长=1.96秒，累计时长=1.96秒
2025-07-29 10:41:04,585 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\308.mp4 (确认存在: True)
2025-07-29 10:41:04,585 - INFO - 添加场景ID=308，时长=1.76秒，累计时长=3.72秒
2025-07-29 10:41:04,585 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\309.mp4 (确认存在: True)
2025-07-29 10:41:04,585 - INFO - 添加场景ID=309，时长=2.56秒，累计时长=6.28秒
2025-07-29 10:41:04,585 - INFO - 场景总时长(6.28秒)已达到音频时长(3.88秒)的1.5倍，停止添加场景
2025-07-29 10:41:04,585 - INFO - 准备合并 3 个场景文件，总时长约 6.28秒
2025-07-29 10:41:04,585 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/305.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/308.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/309.mp4'

2025-07-29 10:41:04,585 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxq7ior6v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxq7ior6v\temp_combined.mp4
2025-07-29 10:41:04,715 - INFO - 合并后的视频时长: 6.35秒，目标音频时长: 3.88秒
2025-07-29 10:41:04,715 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxq7ior6v\temp_combined.mp4 -ss 0 -to 3.876 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-07-29 10:41:04,992 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:04,992 - INFO - 目标音频时长: 3.88秒
2025-07-29 10:41:04,992 - INFO - 实际视频时长: 3.90秒
2025-07-29 10:41:04,992 - INFO - 时长差异: 0.03秒 (0.70%)
2025-07-29 10:41:04,992 - INFO - ==========================================
2025-07-29 10:41:04,992 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:04,992 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-07-29 10:41:04,993 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxq7ior6v
2025-07-29 10:41:05,037 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:05,037 - INFO -   - 音频时长: 3.88秒
2025-07-29 10:41:05,037 - INFO -   - 视频时长: 3.90秒
2025-07-29 10:41:05,037 - INFO -   - 时长差异: 0.03秒 (0.70%)
2025-07-29 10:41:05,037 - INFO - 
字幕 #37 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:05,037 - INFO - 生成的视频文件:
2025-07-29 10:41:05,037 - INFO -   1. F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-29 10:41:05,037 - INFO -   2. F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-07-29 10:41:05,037 - INFO -   3. F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-07-29 10:41:05,037 - INFO - ========== 字幕 #37 处理结束 ==========

