2025-07-29 10:41:45,823 - INFO - ========== 字幕 #63 处理开始 ==========
2025-07-29 10:41:45,823 - INFO - 字幕内容: 他甚至以萧家效忠为筹码，促成这桩交易。
2025-07-29 10:41:45,823 - INFO - 字幕序号: [1251, 1255]
2025-07-29 10:41:45,823 - INFO - 音频文件详情:
2025-07-29 10:41:45,823 - INFO -   - 路径: output\63.wav
2025-07-29 10:41:45,823 - INFO -   - 时长: 3.67秒
2025-07-29 10:41:45,823 - INFO -   - 验证音频时长: 3.67秒
2025-07-29 10:41:45,823 - INFO - 字幕时间戳信息:
2025-07-29 10:41:45,823 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:45,824 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:45,824 - INFO -   - 根据生成的音频时长(3.67秒)已调整字幕时间戳
2025-07-29 10:41:45,824 - INFO - ========== 新模式：为字幕 #63 生成4套场景方案 ==========
2025-07-29 10:41:45,824 - INFO - 字幕序号列表: [1251, 1255]
2025-07-29 10:41:45,824 - INFO - 
--- 生成方案 #1：基于字幕序号 #1251 ---
2025-07-29 10:41:45,824 - INFO - 开始为单个字幕序号 #1251 匹配场景，目标时长: 3.67秒
2025-07-29 10:41:45,824 - INFO - 开始查找字幕序号 [1251] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:45,824 - INFO - 找到related_overlap场景: scene_id=1607, 字幕#1251
2025-07-29 10:41:45,825 - INFO - 字幕 #1251 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:45,825 - INFO - 字幕序号 #1251 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:45,825 - INFO - 选择第一个overlap场景作为起点: scene_id=1607
2025-07-29 10:41:45,825 - INFO - 添加起点场景: scene_id=1607, 时长=2.72秒, 累计时长=2.72秒
2025-07-29 10:41:45,825 - INFO - 起点场景时长不足，需要延伸填充 0.95秒
2025-07-29 10:41:45,825 - INFO - 起点场景在原始列表中的索引: 1606
2025-07-29 10:41:45,825 - INFO - 延伸添加场景: scene_id=1608 (裁剪至 0.95秒)
2025-07-29 10:41:45,825 - INFO - 累计时长: 3.67秒
2025-07-29 10:41:45,825 - INFO - 字幕序号 #1251 场景匹配完成，共选择 2 个场景，总时长: 3.67秒
2025-07-29 10:41:45,825 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:45,825 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:45,825 - INFO - 
--- 生成方案 #2：基于字幕序号 #1255 ---
2025-07-29 10:41:45,825 - INFO - 开始为单个字幕序号 #1255 匹配场景，目标时长: 3.67秒
2025-07-29 10:41:45,825 - INFO - 开始查找字幕序号 [1255] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:45,826 - INFO - 找到related_overlap场景: scene_id=1609, 字幕#1255
2025-07-29 10:41:45,826 - INFO - 找到related_between场景: scene_id=1610, 字幕#1255
2025-07-29 10:41:45,826 - INFO - 字幕 #1255 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:45,826 - INFO - 字幕序号 #1255 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 10:41:45,826 - INFO - 选择第一个overlap场景作为起点: scene_id=1609
2025-07-29 10:41:45,826 - INFO - 添加起点场景: scene_id=1609, 时长=3.28秒, 累计时长=3.28秒
2025-07-29 10:41:45,826 - INFO - 起点场景时长不足，需要延伸填充 0.39秒
2025-07-29 10:41:45,827 - INFO - 起点场景在原始列表中的索引: 1608
2025-07-29 10:41:45,827 - INFO - 延伸添加场景: scene_id=1610 (裁剪至 0.39秒)
2025-07-29 10:41:45,827 - INFO - 累计时长: 3.67秒
2025-07-29 10:41:45,827 - INFO - 字幕序号 #1255 场景匹配完成，共选择 2 个场景，总时长: 3.67秒
2025-07-29 10:41:45,827 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:45,827 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:45,827 - INFO - ========== 当前模式：为字幕 #63 生成 1 套场景方案 ==========
2025-07-29 10:41:45,827 - INFO - 开始查找字幕序号 [1251, 1255] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:45,827 - INFO - 找到related_overlap场景: scene_id=1607, 字幕#1251
2025-07-29 10:41:45,827 - INFO - 找到related_overlap场景: scene_id=1609, 字幕#1255
2025-07-29 10:41:45,828 - INFO - 找到related_between场景: scene_id=1610, 字幕#1255
2025-07-29 10:41:45,828 - INFO - 字幕 #1251 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:45,828 - INFO - 字幕 #1255 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:45,828 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 10:41:45,828 - INFO - 开始生成方案 #1
2025-07-29 10:41:45,828 - INFO - 方案 #1: 为字幕#1251选择初始化overlap场景id=1607
2025-07-29 10:41:45,828 - INFO - 方案 #1: 为字幕#1255选择初始化overlap场景id=1609
2025-07-29 10:41:45,828 - INFO - 方案 #1: 初始选择后，当前总时长=6.00秒
2025-07-29 10:41:45,828 - INFO - 方案 #1: 额外between选择后，当前总时长=6.00秒
2025-07-29 10:41:45,828 - INFO - 方案 #1: 场景总时长(6.00秒)大于音频时长(3.67秒)，需要裁剪
2025-07-29 10:41:45,828 - INFO - 调整前总时长: 6.00秒, 目标时长: 3.67秒
2025-07-29 10:41:45,828 - INFO - 需要裁剪 2.33秒
2025-07-29 10:41:45,828 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:45,828 - INFO - 裁剪场景ID=1609：从3.28秒裁剪至1.00秒
2025-07-29 10:41:45,828 - INFO - 裁剪场景ID=1607：从2.72秒裁剪至2.67秒
2025-07-29 10:41:45,828 - INFO - 调整后总时长: 3.67秒，与目标时长差异: 0.00秒
2025-07-29 10:41:45,828 - INFO - 方案 #1 调整/填充后最终总时长: 3.67秒
2025-07-29 10:41:45,829 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:45,829 - INFO - ========== 当前模式：字幕 #63 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:45,829 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:45,829 - INFO - ========== 新模式：字幕 #63 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:45,829 - INFO - 
----- 处理字幕 #63 的方案 #1 -----
2025-07-29 10:41:45,829 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-29 10:41:45,829 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplljnnx6p
2025-07-29 10:41:45,830 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1607.mp4 (确认存在: True)
2025-07-29 10:41:45,830 - INFO - 添加场景ID=1607，时长=2.72秒，累计时长=2.72秒
2025-07-29 10:41:45,830 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1608.mp4 (确认存在: True)
2025-07-29 10:41:45,830 - INFO - 添加场景ID=1608，时长=1.12秒，累计时长=3.84秒
2025-07-29 10:41:45,830 - INFO - 准备合并 2 个场景文件，总时长约 3.84秒
2025-07-29 10:41:45,830 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1607.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1608.mp4'

2025-07-29 10:41:45,831 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplljnnx6p\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplljnnx6p\temp_combined.mp4
2025-07-29 10:41:45,997 - INFO - 合并后的视频时长: 3.89秒，目标音频时长: 3.67秒
2025-07-29 10:41:45,997 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplljnnx6p\temp_combined.mp4 -ss 0 -to 3.674 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-29 10:41:46,335 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:46,335 - INFO - 目标音频时长: 3.67秒
2025-07-29 10:41:46,335 - INFO - 实际视频时长: 3.70秒
2025-07-29 10:41:46,335 - INFO - 时长差异: 0.03秒 (0.79%)
2025-07-29 10:41:46,335 - INFO - ==========================================
2025-07-29 10:41:46,335 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:46,335 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-29 10:41:46,336 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplljnnx6p
2025-07-29 10:41:46,388 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:46,388 - INFO -   - 音频时长: 3.67秒
2025-07-29 10:41:46,388 - INFO -   - 视频时长: 3.70秒
2025-07-29 10:41:46,388 - INFO -   - 时长差异: 0.03秒 (0.79%)
2025-07-29 10:41:46,388 - INFO - 
----- 处理字幕 #63 的方案 #2 -----
2025-07-29 10:41:46,388 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-07-29 10:41:46,388 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwmjxd0ym
2025-07-29 10:41:46,389 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1609.mp4 (确认存在: True)
2025-07-29 10:41:46,389 - INFO - 添加场景ID=1609，时长=3.28秒，累计时长=3.28秒
2025-07-29 10:41:46,389 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1610.mp4 (确认存在: True)
2025-07-29 10:41:46,389 - INFO - 添加场景ID=1610，时长=1.24秒，累计时长=4.52秒
2025-07-29 10:41:46,389 - INFO - 准备合并 2 个场景文件，总时长约 4.52秒
2025-07-29 10:41:46,389 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1609.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1610.mp4'

2025-07-29 10:41:46,389 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwmjxd0ym\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwmjxd0ym\temp_combined.mp4
2025-07-29 10:41:46,545 - INFO - 合并后的视频时长: 4.57秒，目标音频时长: 3.67秒
2025-07-29 10:41:46,545 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwmjxd0ym\temp_combined.mp4 -ss 0 -to 3.674 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-07-29 10:41:46,863 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:46,863 - INFO - 目标音频时长: 3.67秒
2025-07-29 10:41:46,863 - INFO - 实际视频时长: 3.70秒
2025-07-29 10:41:46,863 - INFO - 时长差异: 0.03秒 (0.79%)
2025-07-29 10:41:46,863 - INFO - ==========================================
2025-07-29 10:41:46,863 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:46,863 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-07-29 10:41:46,864 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwmjxd0ym
2025-07-29 10:41:46,928 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:46,928 - INFO -   - 音频时长: 3.67秒
2025-07-29 10:41:46,928 - INFO -   - 视频时长: 3.70秒
2025-07-29 10:41:46,928 - INFO -   - 时长差异: 0.03秒 (0.79%)
2025-07-29 10:41:46,928 - INFO - 
----- 处理字幕 #63 的方案 #3 -----
2025-07-29 10:41:46,928 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-07-29 10:41:46,928 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdrun25qt
2025-07-29 10:41:46,929 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1607.mp4 (确认存在: True)
2025-07-29 10:41:46,929 - INFO - 添加场景ID=1607，时长=2.72秒，累计时长=2.72秒
2025-07-29 10:41:46,929 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1609.mp4 (确认存在: True)
2025-07-29 10:41:46,929 - INFO - 添加场景ID=1609，时长=3.28秒，累计时长=6.00秒
2025-07-29 10:41:46,929 - INFO - 场景总时长(6.00秒)已达到音频时长(3.67秒)的1.5倍，停止添加场景
2025-07-29 10:41:46,929 - INFO - 准备合并 2 个场景文件，总时长约 6.00秒
2025-07-29 10:41:46,930 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1607.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1609.mp4'

2025-07-29 10:41:46,930 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpdrun25qt\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpdrun25qt\temp_combined.mp4
2025-07-29 10:41:47,092 - INFO - 合并后的视频时长: 6.05秒，目标音频时长: 3.67秒
2025-07-29 10:41:47,092 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpdrun25qt\temp_combined.mp4 -ss 0 -to 3.674 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-07-29 10:41:47,405 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:47,405 - INFO - 目标音频时长: 3.67秒
2025-07-29 10:41:47,405 - INFO - 实际视频时长: 3.70秒
2025-07-29 10:41:47,405 - INFO - 时长差异: 0.03秒 (0.79%)
2025-07-29 10:41:47,405 - INFO - ==========================================
2025-07-29 10:41:47,405 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:47,405 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-07-29 10:41:47,407 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdrun25qt
2025-07-29 10:41:47,473 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:47,473 - INFO -   - 音频时长: 3.67秒
2025-07-29 10:41:47,473 - INFO -   - 视频时长: 3.70秒
2025-07-29 10:41:47,473 - INFO -   - 时长差异: 0.03秒 (0.79%)
2025-07-29 10:41:47,474 - INFO - 
字幕 #63 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:47,474 - INFO - 生成的视频文件:
2025-07-29 10:41:47,474 - INFO -   1. F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-07-29 10:41:47,474 - INFO -   2. F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-07-29 10:41:47,474 - INFO -   3. F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-07-29 10:41:47,474 - INFO - ========== 字幕 #63 处理结束 ==========

