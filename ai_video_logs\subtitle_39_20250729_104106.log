2025-07-29 10:41:06,353 - INFO - ========== 字幕 #39 处理开始 ==========
2025-07-29 10:41:06,353 - INFO - 字幕内容: 她错把他当成哥哥，哭诉着自己埋藏已久的心意。
2025-07-29 10:41:06,353 - INFO - 字幕序号: [224, 227]
2025-07-29 10:41:06,353 - INFO - 音频文件详情:
2025-07-29 10:41:06,353 - INFO -   - 路径: output\39.wav
2025-07-29 10:41:06,353 - INFO -   - 时长: 3.20秒
2025-07-29 10:41:06,353 - INFO -   - 验证音频时长: 3.20秒
2025-07-29 10:41:06,353 - INFO - 字幕时间戳信息:
2025-07-29 10:41:06,353 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:06,353 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:06,353 - INFO -   - 根据生成的音频时长(3.20秒)已调整字幕时间戳
2025-07-29 10:41:06,353 - INFO - ========== 新模式：为字幕 #39 生成4套场景方案 ==========
2025-07-29 10:41:06,353 - INFO - 字幕序号列表: [224, 227]
2025-07-29 10:41:06,353 - INFO - 
--- 生成方案 #1：基于字幕序号 #224 ---
2025-07-29 10:41:06,354 - INFO - 开始为单个字幕序号 #224 匹配场景，目标时长: 3.20秒
2025-07-29 10:41:06,354 - INFO - 开始查找字幕序号 [224] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:06,354 - INFO - 找到related_overlap场景: scene_id=327, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_overlap场景: scene_id=328, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_between场景: scene_id=318, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_between场景: scene_id=319, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_between场景: scene_id=320, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_between场景: scene_id=321, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_between场景: scene_id=322, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_between场景: scene_id=323, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_between场景: scene_id=324, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_between场景: scene_id=325, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_between场景: scene_id=326, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_between场景: scene_id=329, 字幕#224
2025-07-29 10:41:06,354 - INFO - 找到related_between场景: scene_id=330, 字幕#224
2025-07-29 10:41:06,355 - INFO - 字幕 #224 找到 2 个overlap场景, 11 个between场景
2025-07-29 10:41:06,355 - INFO - 字幕序号 #224 找到 2 个可用overlap场景, 11 个可用between场景
2025-07-29 10:41:06,355 - INFO - 选择第一个overlap场景作为起点: scene_id=327
2025-07-29 10:41:06,355 - INFO - 添加起点场景: scene_id=327, 时长=1.92秒, 累计时长=1.92秒
2025-07-29 10:41:06,355 - INFO - 起点场景时长不足，需要延伸填充 1.28秒
2025-07-29 10:41:06,355 - INFO - 起点场景在原始列表中的索引: 326
2025-07-29 10:41:06,355 - INFO - 延伸添加场景: scene_id=328 (完整时长 0.92秒)
2025-07-29 10:41:06,355 - INFO - 累计时长: 2.84秒
2025-07-29 10:41:06,355 - INFO - 延伸添加场景: scene_id=329 (裁剪至 0.36秒)
2025-07-29 10:41:06,355 - INFO - 累计时长: 3.20秒
2025-07-29 10:41:06,355 - INFO - 字幕序号 #224 场景匹配完成，共选择 3 个场景，总时长: 3.20秒
2025-07-29 10:41:06,355 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:06,355 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:06,355 - INFO - 
--- 生成方案 #2：基于字幕序号 #227 ---
2025-07-29 10:41:06,355 - INFO - 开始为单个字幕序号 #227 匹配场景，目标时长: 3.20秒
2025-07-29 10:41:06,355 - INFO - 开始查找字幕序号 [227] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:06,355 - INFO - 找到related_overlap场景: scene_id=332, 字幕#227
2025-07-29 10:41:06,356 - INFO - 找到related_between场景: scene_id=333, 字幕#227
2025-07-29 10:41:06,356 - INFO - 找到related_between场景: scene_id=334, 字幕#227
2025-07-29 10:41:06,356 - INFO - 找到related_between场景: scene_id=335, 字幕#227
2025-07-29 10:41:06,356 - INFO - 字幕 #227 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:41:06,356 - INFO - 字幕序号 #227 找到 1 个可用overlap场景, 3 个可用between场景
2025-07-29 10:41:06,357 - INFO - 选择第一个overlap场景作为起点: scene_id=332
2025-07-29 10:41:06,357 - INFO - 添加起点场景: scene_id=332, 时长=4.32秒, 累计时长=4.32秒
2025-07-29 10:41:06,357 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:06,357 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:41:06,357 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:06,357 - INFO - ========== 当前模式：为字幕 #39 生成 1 套场景方案 ==========
2025-07-29 10:41:06,357 - INFO - 开始查找字幕序号 [224, 227] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:06,357 - INFO - 找到related_overlap场景: scene_id=327, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_overlap场景: scene_id=328, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_overlap场景: scene_id=332, 字幕#227
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=318, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=319, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=320, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=321, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=322, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=323, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=324, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=325, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=326, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=329, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=330, 字幕#224
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=333, 字幕#227
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=334, 字幕#227
2025-07-29 10:41:06,357 - INFO - 找到related_between场景: scene_id=335, 字幕#227
2025-07-29 10:41:06,357 - INFO - 字幕 #224 找到 2 个overlap场景, 11 个between场景
2025-07-29 10:41:06,357 - INFO - 字幕 #227 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:41:06,357 - INFO - 共收集 3 个未使用的overlap场景和 14 个未使用的between场景
2025-07-29 10:41:06,357 - INFO - 开始生成方案 #1
2025-07-29 10:41:06,357 - INFO - 方案 #1: 为字幕#224选择初始化overlap场景id=327
2025-07-29 10:41:06,357 - INFO - 方案 #1: 为字幕#227选择初始化overlap场景id=332
2025-07-29 10:41:06,357 - INFO - 方案 #1: 初始选择后，当前总时长=6.24秒
2025-07-29 10:41:06,357 - INFO - 方案 #1: 额外between选择后，当前总时长=6.24秒
2025-07-29 10:41:06,357 - INFO - 方案 #1: 场景总时长(6.24秒)大于音频时长(3.20秒)，需要裁剪
2025-07-29 10:41:06,358 - INFO - 调整前总时长: 6.24秒, 目标时长: 3.20秒
2025-07-29 10:41:06,358 - INFO - 需要裁剪 3.04秒
2025-07-29 10:41:06,358 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:06,358 - INFO - 裁剪场景ID=332：从4.32秒裁剪至1.30秒
2025-07-29 10:41:06,358 - INFO - 裁剪场景ID=327：从1.92秒裁剪至1.90秒
2025-07-29 10:41:06,358 - INFO - 调整后总时长: 3.20秒，与目标时长差异: 0.00秒
2025-07-29 10:41:06,358 - INFO - 方案 #1 调整/填充后最终总时长: 3.20秒
2025-07-29 10:41:06,358 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:06,358 - INFO - ========== 当前模式：字幕 #39 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:06,358 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:06,358 - INFO - ========== 新模式：字幕 #39 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:06,358 - INFO - 
----- 处理字幕 #39 的方案 #1 -----
2025-07-29 10:41:06,358 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-29 10:41:06,358 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv8cbkvi0
2025-07-29 10:41:06,359 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\327.mp4 (确认存在: True)
2025-07-29 10:41:06,359 - INFO - 添加场景ID=327，时长=1.92秒，累计时长=1.92秒
2025-07-29 10:41:06,359 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\328.mp4 (确认存在: True)
2025-07-29 10:41:06,359 - INFO - 添加场景ID=328，时长=0.92秒，累计时长=2.84秒
2025-07-29 10:41:06,359 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\329.mp4 (确认存在: True)
2025-07-29 10:41:06,359 - INFO - 添加场景ID=329，时长=1.20秒，累计时长=4.04秒
2025-07-29 10:41:06,359 - INFO - 准备合并 3 个场景文件，总时长约 4.04秒
2025-07-29 10:41:06,359 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/327.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/328.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/329.mp4'

2025-07-29 10:41:06,359 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpv8cbkvi0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpv8cbkvi0\temp_combined.mp4
2025-07-29 10:41:06,509 - INFO - 合并后的视频时长: 4.11秒，目标音频时长: 3.20秒
2025-07-29 10:41:06,509 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpv8cbkvi0\temp_combined.mp4 -ss 0 -to 3.198 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-29 10:41:06,772 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:06,772 - INFO - 目标音频时长: 3.20秒
2025-07-29 10:41:06,772 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:41:06,772 - INFO - 时长差异: 0.02秒 (0.78%)
2025-07-29 10:41:06,772 - INFO - ==========================================
2025-07-29 10:41:06,772 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:06,772 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-29 10:41:06,773 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv8cbkvi0
2025-07-29 10:41:06,815 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:06,815 - INFO -   - 音频时长: 3.20秒
2025-07-29 10:41:06,815 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:41:06,815 - INFO -   - 时长差异: 0.02秒 (0.78%)
2025-07-29 10:41:06,815 - INFO - 
----- 处理字幕 #39 的方案 #2 -----
2025-07-29 10:41:06,815 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-07-29 10:41:06,815 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe81rxfic
2025-07-29 10:41:06,816 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\332.mp4 (确认存在: True)
2025-07-29 10:41:06,816 - INFO - 添加场景ID=332，时长=4.32秒，累计时长=4.32秒
2025-07-29 10:41:06,816 - INFO - 准备合并 1 个场景文件，总时长约 4.32秒
2025-07-29 10:41:06,816 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/332.mp4'

2025-07-29 10:41:06,816 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe81rxfic\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe81rxfic\temp_combined.mp4
2025-07-29 10:41:06,925 - INFO - 合并后的视频时长: 4.34秒，目标音频时长: 3.20秒
2025-07-29 10:41:06,925 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe81rxfic\temp_combined.mp4 -ss 0 -to 3.198 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-07-29 10:41:07,154 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:07,154 - INFO - 目标音频时长: 3.20秒
2025-07-29 10:41:07,154 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:41:07,154 - INFO - 时长差异: 0.02秒 (0.78%)
2025-07-29 10:41:07,154 - INFO - ==========================================
2025-07-29 10:41:07,154 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:07,154 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-07-29 10:41:07,155 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe81rxfic
2025-07-29 10:41:07,202 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:07,202 - INFO -   - 音频时长: 3.20秒
2025-07-29 10:41:07,202 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:41:07,202 - INFO -   - 时长差异: 0.02秒 (0.78%)
2025-07-29 10:41:07,202 - INFO - 
----- 处理字幕 #39 的方案 #3 -----
2025-07-29 10:41:07,202 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\39_3.mp4
2025-07-29 10:41:07,202 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp716ncgf9
2025-07-29 10:41:07,203 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\327.mp4 (确认存在: True)
2025-07-29 10:41:07,203 - INFO - 添加场景ID=327，时长=1.92秒，累计时长=1.92秒
2025-07-29 10:41:07,203 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\332.mp4 (确认存在: True)
2025-07-29 10:41:07,203 - INFO - 添加场景ID=332，时长=4.32秒，累计时长=6.24秒
2025-07-29 10:41:07,203 - INFO - 场景总时长(6.24秒)已达到音频时长(3.20秒)的1.5倍，停止添加场景
2025-07-29 10:41:07,203 - INFO - 准备合并 2 个场景文件，总时长约 6.24秒
2025-07-29 10:41:07,203 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/327.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/332.mp4'

2025-07-29 10:41:07,203 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp716ncgf9\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp716ncgf9\temp_combined.mp4
2025-07-29 10:41:07,319 - INFO - 合并后的视频时长: 6.29秒，目标音频时长: 3.20秒
2025-07-29 10:41:07,319 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp716ncgf9\temp_combined.mp4 -ss 0 -to 3.198 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\39_3.mp4
2025-07-29 10:41:07,576 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:07,576 - INFO - 目标音频时长: 3.20秒
2025-07-29 10:41:07,577 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:41:07,577 - INFO - 时长差异: 0.02秒 (0.78%)
2025-07-29 10:41:07,577 - INFO - ==========================================
2025-07-29 10:41:07,577 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:07,577 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\39_3.mp4
2025-07-29 10:41:07,577 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp716ncgf9
2025-07-29 10:41:07,620 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:07,620 - INFO -   - 音频时长: 3.20秒
2025-07-29 10:41:07,620 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:41:07,620 - INFO -   - 时长差异: 0.02秒 (0.78%)
2025-07-29 10:41:07,620 - INFO - 
字幕 #39 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:07,620 - INFO - 生成的视频文件:
2025-07-29 10:41:07,620 - INFO -   1. F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-07-29 10:41:07,620 - INFO -   2. F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-07-29 10:41:07,620 - INFO -   3. F:/github/aicut_auto/newcut_ai\39_3.mp4
2025-07-29 10:41:07,620 - INFO - ========== 字幕 #39 处理结束 ==========

