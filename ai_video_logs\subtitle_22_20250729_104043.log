2025-07-29 10:40:43,717 - INFO - ========== 字幕 #22 处理开始 ==========
2025-07-29 10:40:43,717 - INFO - 字幕内容: 她怕哥哥找不到，执意等待，一如多年前那个约定。
2025-07-29 10:40:43,717 - INFO - 字幕序号: [114, 120]
2025-07-29 10:40:43,717 - INFO - 音频文件详情:
2025-07-29 10:40:43,717 - INFO -   - 路径: output\22.wav
2025-07-29 10:40:43,717 - INFO -   - 时长: 3.92秒
2025-07-29 10:40:43,717 - INFO -   - 验证音频时长: 3.92秒
2025-07-29 10:40:43,717 - INFO - 字幕时间戳信息:
2025-07-29 10:40:43,717 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:43,717 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:43,717 - INFO -   - 根据生成的音频时长(3.92秒)已调整字幕时间戳
2025-07-29 10:40:43,717 - INFO - ========== 新模式：为字幕 #22 生成4套场景方案 ==========
2025-07-29 10:40:43,717 - INFO - 字幕序号列表: [114, 120]
2025-07-29 10:40:43,717 - INFO - 
--- 生成方案 #1：基于字幕序号 #114 ---
2025-07-29 10:40:43,717 - INFO - 开始为单个字幕序号 #114 匹配场景，目标时长: 3.92秒
2025-07-29 10:40:43,717 - INFO - 开始查找字幕序号 [114] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:43,717 - INFO - 找到related_overlap场景: scene_id=164, 字幕#114
2025-07-29 10:40:43,717 - INFO - 找到related_overlap场景: scene_id=165, 字幕#114
2025-07-29 10:40:43,719 - INFO - 字幕 #114 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:43,720 - INFO - 字幕序号 #114 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:43,720 - INFO - 选择第一个overlap场景作为起点: scene_id=164
2025-07-29 10:40:43,720 - INFO - 添加起点场景: scene_id=164, 时长=1.32秒, 累计时长=1.32秒
2025-07-29 10:40:43,720 - INFO - 起点场景时长不足，需要延伸填充 2.60秒
2025-07-29 10:40:43,720 - INFO - 起点场景在原始列表中的索引: 163
2025-07-29 10:40:43,720 - INFO - 延伸添加场景: scene_id=165 (完整时长 1.28秒)
2025-07-29 10:40:43,720 - INFO - 累计时长: 2.60秒
2025-07-29 10:40:43,720 - INFO - 延伸添加场景: scene_id=166 (完整时长 1.04秒)
2025-07-29 10:40:43,720 - INFO - 累计时长: 3.64秒
2025-07-29 10:40:43,720 - INFO - 延伸添加场景: scene_id=167 (裁剪至 0.29秒)
2025-07-29 10:40:43,720 - INFO - 累计时长: 3.92秒
2025-07-29 10:40:43,720 - INFO - 字幕序号 #114 场景匹配完成，共选择 4 个场景，总时长: 3.92秒
2025-07-29 10:40:43,720 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 10:40:43,720 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 10:40:43,720 - INFO - 
--- 生成方案 #2：基于字幕序号 #120 ---
2025-07-29 10:40:43,720 - INFO - 开始为单个字幕序号 #120 匹配场景，目标时长: 3.92秒
2025-07-29 10:40:43,720 - INFO - 开始查找字幕序号 [120] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:43,720 - INFO - 找到related_overlap场景: scene_id=172, 字幕#120
2025-07-29 10:40:43,720 - INFO - 找到related_overlap场景: scene_id=173, 字幕#120
2025-07-29 10:40:43,720 - INFO - 找到related_between场景: scene_id=171, 字幕#120
2025-07-29 10:40:43,720 - INFO - 找到related_between场景: scene_id=174, 字幕#120
2025-07-29 10:40:43,720 - INFO - 找到related_between场景: scene_id=175, 字幕#120
2025-07-29 10:40:43,721 - INFO - 字幕 #120 找到 2 个overlap场景, 3 个between场景
2025-07-29 10:40:43,721 - INFO - 字幕序号 #120 找到 2 个可用overlap场景, 3 个可用between场景
2025-07-29 10:40:43,721 - INFO - 选择第一个overlap场景作为起点: scene_id=172
2025-07-29 10:40:43,721 - INFO - 添加起点场景: scene_id=172, 时长=0.88秒, 累计时长=0.88秒
2025-07-29 10:40:43,721 - INFO - 起点场景时长不足，需要延伸填充 3.04秒
2025-07-29 10:40:43,721 - INFO - 起点场景在原始列表中的索引: 171
2025-07-29 10:40:43,721 - INFO - 延伸添加场景: scene_id=173 (完整时长 0.68秒)
2025-07-29 10:40:43,721 - INFO - 累计时长: 1.56秒
2025-07-29 10:40:43,721 - INFO - 延伸添加场景: scene_id=174 (完整时长 1.12秒)
2025-07-29 10:40:43,721 - INFO - 累计时长: 2.68秒
2025-07-29 10:40:43,721 - INFO - 延伸添加场景: scene_id=175 (完整时长 0.92秒)
2025-07-29 10:40:43,721 - INFO - 累计时长: 3.60秒
2025-07-29 10:40:43,721 - INFO - 延伸添加场景: scene_id=176 (裁剪至 0.32秒)
2025-07-29 10:40:43,721 - INFO - 累计时长: 3.92秒
2025-07-29 10:40:43,721 - INFO - 字幕序号 #120 场景匹配完成，共选择 5 个场景，总时长: 3.92秒
2025-07-29 10:40:43,721 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-07-29 10:40:43,721 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:43,721 - INFO - ========== 当前模式：为字幕 #22 生成 1 套场景方案 ==========
2025-07-29 10:40:43,721 - INFO - 开始查找字幕序号 [114, 120] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:43,721 - INFO - 找到related_overlap场景: scene_id=164, 字幕#114
2025-07-29 10:40:43,721 - INFO - 找到related_overlap场景: scene_id=165, 字幕#114
2025-07-29 10:40:43,721 - INFO - 找到related_overlap场景: scene_id=172, 字幕#120
2025-07-29 10:40:43,721 - INFO - 找到related_overlap场景: scene_id=173, 字幕#120
2025-07-29 10:40:43,722 - INFO - 找到related_between场景: scene_id=171, 字幕#120
2025-07-29 10:40:43,722 - INFO - 找到related_between场景: scene_id=174, 字幕#120
2025-07-29 10:40:43,722 - INFO - 找到related_between场景: scene_id=175, 字幕#120
2025-07-29 10:40:43,722 - INFO - 字幕 #114 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:43,722 - INFO - 字幕 #120 找到 2 个overlap场景, 3 个between场景
2025-07-29 10:40:43,722 - INFO - 共收集 4 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 10:40:43,723 - INFO - 开始生成方案 #1
2025-07-29 10:40:43,723 - INFO - 方案 #1: 为字幕#114选择初始化overlap场景id=164
2025-07-29 10:40:43,723 - INFO - 方案 #1: 为字幕#120选择初始化overlap场景id=173
2025-07-29 10:40:43,723 - INFO - 方案 #1: 初始选择后，当前总时长=2.00秒
2025-07-29 10:40:43,723 - INFO - 方案 #1: 额外添加overlap场景id=165, 当前总时长=3.28秒
2025-07-29 10:40:43,723 - INFO - 方案 #1: 额外添加overlap场景id=172, 当前总时长=4.16秒
2025-07-29 10:40:43,723 - INFO - 方案 #1: 额外between选择后，当前总时长=4.16秒
2025-07-29 10:40:43,723 - INFO - 方案 #1: 场景总时长(4.16秒)大于音频时长(3.92秒)，需要裁剪
2025-07-29 10:40:43,723 - INFO - 调整前总时长: 4.16秒, 目标时长: 3.92秒
2025-07-29 10:40:43,723 - INFO - 需要裁剪 0.23秒
2025-07-29 10:40:43,723 - INFO - 裁剪最长场景ID=164：从1.32秒裁剪至1.08秒
2025-07-29 10:40:43,723 - INFO - 调整后总时长: 3.92秒，与目标时长差异: 0.00秒
2025-07-29 10:40:43,723 - INFO - 方案 #1 调整/填充后最终总时长: 3.92秒
2025-07-29 10:40:43,723 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:43,723 - INFO - ========== 当前模式：字幕 #22 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:43,723 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:43,723 - INFO - ========== 新模式：字幕 #22 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:43,723 - INFO - 
----- 处理字幕 #22 的方案 #1 -----
2025-07-29 10:40:43,723 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-29 10:40:43,723 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2xefi2v7
2025-07-29 10:40:43,724 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\164.mp4 (确认存在: True)
2025-07-29 10:40:43,724 - INFO - 添加场景ID=164，时长=1.32秒，累计时长=1.32秒
2025-07-29 10:40:43,724 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\165.mp4 (确认存在: True)
2025-07-29 10:40:43,724 - INFO - 添加场景ID=165，时长=1.28秒，累计时长=2.60秒
2025-07-29 10:40:43,724 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\166.mp4 (确认存在: True)
2025-07-29 10:40:43,724 - INFO - 添加场景ID=166，时长=1.04秒，累计时长=3.64秒
2025-07-29 10:40:43,724 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\167.mp4 (确认存在: True)
2025-07-29 10:40:43,724 - INFO - 添加场景ID=167，时长=1.80秒，累计时长=5.44秒
2025-07-29 10:40:43,724 - INFO - 准备合并 4 个场景文件，总时长约 5.44秒
2025-07-29 10:40:43,724 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/164.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/165.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/166.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/167.mp4'

2025-07-29 10:40:43,724 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2xefi2v7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2xefi2v7\temp_combined.mp4
2025-07-29 10:40:43,871 - INFO - 合并后的视频时长: 5.53秒，目标音频时长: 3.92秒
2025-07-29 10:40:43,871 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2xefi2v7\temp_combined.mp4 -ss 0 -to 3.923 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-29 10:40:44,155 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:44,155 - INFO - 目标音频时长: 3.92秒
2025-07-29 10:40:44,155 - INFO - 实际视频时长: 3.98秒
2025-07-29 10:40:44,155 - INFO - 时长差异: 0.06秒 (1.53%)
2025-07-29 10:40:44,155 - INFO - ==========================================
2025-07-29 10:40:44,155 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:44,155 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-29 10:40:44,155 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2xefi2v7
2025-07-29 10:40:44,196 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:44,196 - INFO -   - 音频时长: 3.92秒
2025-07-29 10:40:44,196 - INFO -   - 视频时长: 3.98秒
2025-07-29 10:40:44,196 - INFO -   - 时长差异: 0.06秒 (1.53%)
2025-07-29 10:40:44,196 - INFO - 
----- 处理字幕 #22 的方案 #2 -----
2025-07-29 10:40:44,196 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-29 10:40:44,196 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpayg7wvmo
2025-07-29 10:40:44,197 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\172.mp4 (确认存在: True)
2025-07-29 10:40:44,197 - INFO - 添加场景ID=172，时长=0.88秒，累计时长=0.88秒
2025-07-29 10:40:44,197 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\173.mp4 (确认存在: True)
2025-07-29 10:40:44,197 - INFO - 添加场景ID=173，时长=0.68秒，累计时长=1.56秒
2025-07-29 10:40:44,197 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\174.mp4 (确认存在: True)
2025-07-29 10:40:44,197 - INFO - 添加场景ID=174，时长=1.12秒，累计时长=2.68秒
2025-07-29 10:40:44,197 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\175.mp4 (确认存在: True)
2025-07-29 10:40:44,197 - INFO - 添加场景ID=175，时长=0.92秒，累计时长=3.60秒
2025-07-29 10:40:44,197 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\176.mp4 (确认存在: True)
2025-07-29 10:40:44,197 - INFO - 添加场景ID=176，时长=1.08秒，累计时长=4.68秒
2025-07-29 10:40:44,197 - INFO - 准备合并 5 个场景文件，总时长约 4.68秒
2025-07-29 10:40:44,197 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/172.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/173.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/174.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/175.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/176.mp4'

2025-07-29 10:40:44,198 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpayg7wvmo\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpayg7wvmo\temp_combined.mp4
2025-07-29 10:40:44,337 - INFO - 合并后的视频时长: 4.80秒，目标音频时长: 3.92秒
2025-07-29 10:40:44,337 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpayg7wvmo\temp_combined.mp4 -ss 0 -to 3.923 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-29 10:40:44,637 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:44,637 - INFO - 目标音频时长: 3.92秒
2025-07-29 10:40:44,637 - INFO - 实际视频时长: 3.98秒
2025-07-29 10:40:44,637 - INFO - 时长差异: 0.06秒 (1.53%)
2025-07-29 10:40:44,637 - INFO - ==========================================
2025-07-29 10:40:44,637 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:44,637 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-29 10:40:44,638 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpayg7wvmo
2025-07-29 10:40:44,682 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:44,682 - INFO -   - 音频时长: 3.92秒
2025-07-29 10:40:44,682 - INFO -   - 视频时长: 3.98秒
2025-07-29 10:40:44,682 - INFO -   - 时长差异: 0.06秒 (1.53%)
2025-07-29 10:40:44,682 - INFO - 
----- 处理字幕 #22 的方案 #3 -----
2025-07-29 10:40:44,682 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-29 10:40:44,683 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkc5pgm3d
2025-07-29 10:40:44,683 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\164.mp4 (确认存在: True)
2025-07-29 10:40:44,683 - INFO - 添加场景ID=164，时长=1.32秒，累计时长=1.32秒
2025-07-29 10:40:44,683 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\173.mp4 (确认存在: True)
2025-07-29 10:40:44,683 - INFO - 添加场景ID=173，时长=0.68秒，累计时长=2.00秒
2025-07-29 10:40:44,683 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\165.mp4 (确认存在: True)
2025-07-29 10:40:44,683 - INFO - 添加场景ID=165，时长=1.28秒，累计时长=3.28秒
2025-07-29 10:40:44,683 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\172.mp4 (确认存在: True)
2025-07-29 10:40:44,683 - INFO - 添加场景ID=172，时长=0.88秒，累计时长=4.16秒
2025-07-29 10:40:44,684 - INFO - 准备合并 4 个场景文件，总时长约 4.16秒
2025-07-29 10:40:44,684 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/164.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/173.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/165.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/172.mp4'

2025-07-29 10:40:44,684 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkc5pgm3d\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkc5pgm3d\temp_combined.mp4
2025-07-29 10:40:44,836 - INFO - 合并后的视频时长: 4.25秒，目标音频时长: 3.92秒
2025-07-29 10:40:44,836 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkc5pgm3d\temp_combined.mp4 -ss 0 -to 3.923 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-29 10:40:45,127 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:45,129 - INFO - 目标音频时长: 3.92秒
2025-07-29 10:40:45,129 - INFO - 实际视频时长: 3.98秒
2025-07-29 10:40:45,129 - INFO - 时长差异: 0.06秒 (1.53%)
2025-07-29 10:40:45,129 - INFO - ==========================================
2025-07-29 10:40:45,129 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:45,129 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-29 10:40:45,129 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkc5pgm3d
2025-07-29 10:40:45,170 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:45,170 - INFO -   - 音频时长: 3.92秒
2025-07-29 10:40:45,170 - INFO -   - 视频时长: 3.98秒
2025-07-29 10:40:45,170 - INFO -   - 时长差异: 0.06秒 (1.53%)
2025-07-29 10:40:45,171 - INFO - 
字幕 #22 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:45,171 - INFO - 生成的视频文件:
2025-07-29 10:40:45,171 - INFO -   1. F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-29 10:40:45,171 - INFO -   2. F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-29 10:40:45,171 - INFO -   3. F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-29 10:40:45,171 - INFO - ========== 字幕 #22 处理结束 ==========

