2025-07-29 10:40:41,625 - INFO - ========== 字幕 #20 处理开始 ==========
2025-07-29 10:40:41,625 - INFO - 字幕内容: 哥哥让她在院落等待，承诺自己一定会回来接她。
2025-07-29 10:40:41,625 - INFO - 字幕序号: [97, 105]
2025-07-29 10:40:41,625 - INFO - 音频文件详情:
2025-07-29 10:40:41,625 - INFO -   - 路径: output\20.wav
2025-07-29 10:40:41,625 - INFO -   - 时长: 3.15秒
2025-07-29 10:40:41,625 - INFO -   - 验证音频时长: 3.15秒
2025-07-29 10:40:41,625 - INFO - 字幕时间戳信息:
2025-07-29 10:40:41,625 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:41,625 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:41,625 - INFO -   - 根据生成的音频时长(3.15秒)已调整字幕时间戳
2025-07-29 10:40:41,626 - INFO - ========== 新模式：为字幕 #20 生成4套场景方案 ==========
2025-07-29 10:40:41,626 - INFO - 字幕序号列表: [97, 105]
2025-07-29 10:40:41,626 - INFO - 
--- 生成方案 #1：基于字幕序号 #97 ---
2025-07-29 10:40:41,626 - INFO - 开始为单个字幕序号 #97 匹配场景，目标时长: 3.15秒
2025-07-29 10:40:41,626 - INFO - 开始查找字幕序号 [97] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:41,626 - INFO - 找到related_overlap场景: scene_id=149, 字幕#97
2025-07-29 10:40:41,627 - INFO - 找到related_between场景: scene_id=145, 字幕#97
2025-07-29 10:40:41,627 - INFO - 找到related_between场景: scene_id=146, 字幕#97
2025-07-29 10:40:41,627 - INFO - 找到related_between场景: scene_id=147, 字幕#97
2025-07-29 10:40:41,627 - INFO - 找到related_between场景: scene_id=148, 字幕#97
2025-07-29 10:40:41,627 - INFO - 字幕 #97 找到 1 个overlap场景, 4 个between场景
2025-07-29 10:40:41,627 - INFO - 字幕序号 #97 找到 1 个可用overlap场景, 4 个可用between场景
2025-07-29 10:40:41,627 - INFO - 选择第一个overlap场景作为起点: scene_id=149
2025-07-29 10:40:41,627 - INFO - 添加起点场景: scene_id=149, 时长=8.32秒, 累计时长=8.32秒
2025-07-29 10:40:41,627 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:40:41,627 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:40:41,627 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:40:41,627 - INFO - 
--- 生成方案 #2：基于字幕序号 #105 ---
2025-07-29 10:40:41,627 - INFO - 开始为单个字幕序号 #105 匹配场景，目标时长: 3.15秒
2025-07-29 10:40:41,627 - INFO - 开始查找字幕序号 [105] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:41,627 - INFO - 找到related_overlap场景: scene_id=155, 字幕#105
2025-07-29 10:40:41,628 - INFO - 找到related_between场景: scene_id=156, 字幕#105
2025-07-29 10:40:41,628 - INFO - 字幕 #105 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:40:41,629 - INFO - 字幕序号 #105 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 10:40:41,629 - INFO - 选择第一个overlap场景作为起点: scene_id=155
2025-07-29 10:40:41,629 - INFO - 添加起点场景: scene_id=155, 时长=2.84秒, 累计时长=2.84秒
2025-07-29 10:40:41,629 - INFO - 起点场景时长不足，需要延伸填充 0.31秒
2025-07-29 10:40:41,629 - INFO - 起点场景在原始列表中的索引: 154
2025-07-29 10:40:41,629 - INFO - 延伸添加场景: scene_id=156 (裁剪至 0.31秒)
2025-07-29 10:40:41,629 - INFO - 累计时长: 3.15秒
2025-07-29 10:40:41,629 - INFO - 字幕序号 #105 场景匹配完成，共选择 2 个场景，总时长: 3.15秒
2025-07-29 10:40:41,629 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:41,629 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:41,629 - INFO - ========== 当前模式：为字幕 #20 生成 1 套场景方案 ==========
2025-07-29 10:40:41,629 - INFO - 开始查找字幕序号 [97, 105] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:41,629 - INFO - 找到related_overlap场景: scene_id=149, 字幕#97
2025-07-29 10:40:41,629 - INFO - 找到related_overlap场景: scene_id=155, 字幕#105
2025-07-29 10:40:41,629 - INFO - 找到related_between场景: scene_id=145, 字幕#97
2025-07-29 10:40:41,629 - INFO - 找到related_between场景: scene_id=146, 字幕#97
2025-07-29 10:40:41,629 - INFO - 找到related_between场景: scene_id=147, 字幕#97
2025-07-29 10:40:41,629 - INFO - 找到related_between场景: scene_id=148, 字幕#97
2025-07-29 10:40:41,629 - INFO - 找到related_between场景: scene_id=156, 字幕#105
2025-07-29 10:40:41,629 - INFO - 字幕 #97 找到 1 个overlap场景, 4 个between场景
2025-07-29 10:40:41,629 - INFO - 字幕 #105 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:40:41,629 - INFO - 共收集 2 个未使用的overlap场景和 5 个未使用的between场景
2025-07-29 10:40:41,629 - INFO - 开始生成方案 #1
2025-07-29 10:40:41,629 - INFO - 方案 #1: 为字幕#97选择初始化overlap场景id=149
2025-07-29 10:40:41,629 - INFO - 方案 #1: 为字幕#105选择初始化overlap场景id=155
2025-07-29 10:40:41,629 - INFO - 方案 #1: 初始选择后，当前总时长=11.16秒
2025-07-29 10:40:41,629 - INFO - 方案 #1: 额外between选择后，当前总时长=11.16秒
2025-07-29 10:40:41,629 - INFO - 方案 #1: 场景总时长(11.16秒)大于音频时长(3.15秒)，需要裁剪
2025-07-29 10:40:41,629 - INFO - 调整前总时长: 11.16秒, 目标时长: 3.15秒
2025-07-29 10:40:41,629 - INFO - 需要裁剪 8.01秒
2025-07-29 10:40:41,630 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:40:41,630 - INFO - 裁剪场景ID=149：从8.32秒裁剪至2.50秒
2025-07-29 10:40:41,630 - INFO - 裁剪场景ID=155：从2.84秒裁剪至2.50秒
2025-07-29 10:40:41,630 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 1.84秒
2025-07-29 10:40:41,630 - INFO - 调整后总时长: 4.99秒，与目标时长差异: 1.84秒
2025-07-29 10:40:41,630 - INFO - 方案 #1 调整/填充后最终总时长: 4.99秒
2025-07-29 10:40:41,630 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:41,630 - INFO - ========== 当前模式：字幕 #20 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:41,630 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:41,630 - INFO - ========== 新模式：字幕 #20 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:41,630 - INFO - 
----- 处理字幕 #20 的方案 #1 -----
2025-07-29 10:40:41,630 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-29 10:40:41,630 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpo2s1bbia
2025-07-29 10:40:41,631 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\149.mp4 (确认存在: True)
2025-07-29 10:40:41,631 - INFO - 添加场景ID=149，时长=8.32秒，累计时长=8.32秒
2025-07-29 10:40:41,631 - INFO - 场景总时长(8.32秒)已达到音频时长(3.15秒)的1.5倍，停止添加场景
2025-07-29 10:40:41,631 - INFO - 准备合并 1 个场景文件，总时长约 8.32秒
2025-07-29 10:40:41,631 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/149.mp4'

2025-07-29 10:40:41,631 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpo2s1bbia\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpo2s1bbia\temp_combined.mp4
2025-07-29 10:40:41,740 - INFO - 合并后的视频时长: 8.34秒，目标音频时长: 3.15秒
2025-07-29 10:40:41,740 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpo2s1bbia\temp_combined.mp4 -ss 0 -to 3.153 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-29 10:40:41,990 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:41,990 - INFO - 目标音频时长: 3.15秒
2025-07-29 10:40:41,990 - INFO - 实际视频时长: 3.18秒
2025-07-29 10:40:41,990 - INFO - 时长差异: 0.03秒 (0.95%)
2025-07-29 10:40:41,991 - INFO - ==========================================
2025-07-29 10:40:41,991 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:41,991 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-29 10:40:41,991 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpo2s1bbia
2025-07-29 10:40:42,036 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:42,036 - INFO -   - 音频时长: 3.15秒
2025-07-29 10:40:42,036 - INFO -   - 视频时长: 3.18秒
2025-07-29 10:40:42,036 - INFO -   - 时长差异: 0.03秒 (0.95%)
2025-07-29 10:40:42,036 - INFO - 
----- 处理字幕 #20 的方案 #2 -----
2025-07-29 10:40:42,036 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-07-29 10:40:42,036 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpz9yge4n6
2025-07-29 10:40:42,037 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\155.mp4 (确认存在: True)
2025-07-29 10:40:42,037 - INFO - 添加场景ID=155，时长=2.84秒，累计时长=2.84秒
2025-07-29 10:40:42,037 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\156.mp4 (确认存在: True)
2025-07-29 10:40:42,037 - INFO - 添加场景ID=156，时长=1.00秒，累计时长=3.84秒
2025-07-29 10:40:42,037 - INFO - 准备合并 2 个场景文件，总时长约 3.84秒
2025-07-29 10:40:42,037 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/155.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/156.mp4'

2025-07-29 10:40:42,037 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpz9yge4n6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpz9yge4n6\temp_combined.mp4
2025-07-29 10:40:42,165 - INFO - 合并后的视频时长: 3.89秒，目标音频时长: 3.15秒
2025-07-29 10:40:42,165 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpz9yge4n6\temp_combined.mp4 -ss 0 -to 3.153 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-07-29 10:40:42,403 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:42,403 - INFO - 目标音频时长: 3.15秒
2025-07-29 10:40:42,403 - INFO - 实际视频时长: 3.18秒
2025-07-29 10:40:42,403 - INFO - 时长差异: 0.03秒 (0.95%)
2025-07-29 10:40:42,403 - INFO - ==========================================
2025-07-29 10:40:42,403 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:42,403 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-07-29 10:40:42,404 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpz9yge4n6
2025-07-29 10:40:42,456 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:42,456 - INFO -   - 音频时长: 3.15秒
2025-07-29 10:40:42,456 - INFO -   - 视频时长: 3.18秒
2025-07-29 10:40:42,456 - INFO -   - 时长差异: 0.03秒 (0.95%)
2025-07-29 10:40:42,456 - INFO - 
----- 处理字幕 #20 的方案 #3 -----
2025-07-29 10:40:42,456 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-07-29 10:40:42,456 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfdio0z2p
2025-07-29 10:40:42,457 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\149.mp4 (确认存在: True)
2025-07-29 10:40:42,457 - INFO - 添加场景ID=149，时长=8.32秒，累计时长=8.32秒
2025-07-29 10:40:42,457 - INFO - 场景总时长(8.32秒)已达到音频时长(3.15秒)的1.5倍，停止添加场景
2025-07-29 10:40:42,457 - INFO - 准备合并 1 个场景文件，总时长约 8.32秒
2025-07-29 10:40:42,457 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/149.mp4'

2025-07-29 10:40:42,457 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfdio0z2p\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfdio0z2p\temp_combined.mp4
2025-07-29 10:40:42,574 - INFO - 合并后的视频时长: 8.34秒，目标音频时长: 3.15秒
2025-07-29 10:40:42,574 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfdio0z2p\temp_combined.mp4 -ss 0 -to 3.153 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-07-29 10:40:42,818 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:42,818 - INFO - 目标音频时长: 3.15秒
2025-07-29 10:40:42,818 - INFO - 实际视频时长: 3.18秒
2025-07-29 10:40:42,818 - INFO - 时长差异: 0.03秒 (0.95%)
2025-07-29 10:40:42,818 - INFO - ==========================================
2025-07-29 10:40:42,818 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:42,818 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-07-29 10:40:42,819 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfdio0z2p
2025-07-29 10:40:42,869 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:42,869 - INFO -   - 音频时长: 3.15秒
2025-07-29 10:40:42,869 - INFO -   - 视频时长: 3.18秒
2025-07-29 10:40:42,869 - INFO -   - 时长差异: 0.03秒 (0.95%)
2025-07-29 10:40:42,869 - INFO - 
字幕 #20 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:42,870 - INFO - 生成的视频文件:
2025-07-29 10:40:42,870 - INFO -   1. F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-29 10:40:42,870 - INFO -   2. F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-07-29 10:40:42,870 - INFO -   3. F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-07-29 10:40:42,870 - INFO - ========== 字幕 #20 处理结束 ==========

