2025-07-29 10:40:58,127 - INFO - ========== 字幕 #33 处理开始 ==========
2025-07-29 10:40:58,127 - INFO - 字幕内容: 对方自称倾慕已久，转眼却叫来地痞流氓。
2025-07-29 10:40:58,127 - INFO - 字幕序号: [186, 193]
2025-07-29 10:40:58,128 - INFO - 音频文件详情:
2025-07-29 10:40:58,128 - INFO -   - 路径: output\33.wav
2025-07-29 10:40:58,128 - INFO -   - 时长: 3.00秒
2025-07-29 10:40:58,128 - INFO -   - 验证音频时长: 3.00秒
2025-07-29 10:40:58,128 - INFO - 字幕时间戳信息:
2025-07-29 10:40:58,128 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:58,129 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:58,129 - INFO -   - 根据生成的音频时长(3.00秒)已调整字幕时间戳
2025-07-29 10:40:58,129 - INFO - ========== 新模式：为字幕 #33 生成4套场景方案 ==========
2025-07-29 10:40:58,129 - INFO - 字幕序号列表: [186, 193]
2025-07-29 10:40:58,129 - INFO - 
--- 生成方案 #1：基于字幕序号 #186 ---
2025-07-29 10:40:58,129 - INFO - 开始为单个字幕序号 #186 匹配场景，目标时长: 3.00秒
2025-07-29 10:40:58,129 - INFO - 开始查找字幕序号 [186] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:58,129 - INFO - 找到related_overlap场景: scene_id=264, 字幕#186
2025-07-29 10:40:58,129 - INFO - 找到related_between场景: scene_id=261, 字幕#186
2025-07-29 10:40:58,130 - INFO - 找到related_between场景: scene_id=262, 字幕#186
2025-07-29 10:40:58,130 - INFO - 找到related_between场景: scene_id=263, 字幕#186
2025-07-29 10:40:58,130 - INFO - 字幕 #186 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:40:58,130 - INFO - 字幕序号 #186 找到 1 个可用overlap场景, 3 个可用between场景
2025-07-29 10:40:58,130 - INFO - 选择第一个overlap场景作为起点: scene_id=264
2025-07-29 10:40:58,130 - INFO - 添加起点场景: scene_id=264, 时长=2.96秒, 累计时长=2.96秒
2025-07-29 10:40:58,130 - INFO - 起点场景时长不足，需要延伸填充 0.04秒
2025-07-29 10:40:58,130 - INFO - 起点场景在原始列表中的索引: 263
2025-07-29 10:40:58,130 - INFO - 延伸添加场景: scene_id=265 (裁剪至 0.04秒)
2025-07-29 10:40:58,130 - INFO - 累计时长: 3.00秒
2025-07-29 10:40:58,130 - INFO - 字幕序号 #186 场景匹配完成，共选择 2 个场景，总时长: 3.00秒
2025-07-29 10:40:58,130 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:58,130 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:58,130 - INFO - 
--- 生成方案 #2：基于字幕序号 #193 ---
2025-07-29 10:40:58,130 - INFO - 开始为单个字幕序号 #193 匹配场景，目标时长: 3.00秒
2025-07-29 10:40:58,130 - INFO - 开始查找字幕序号 [193] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:58,130 - INFO - 找到related_overlap场景: scene_id=269, 字幕#193
2025-07-29 10:40:58,130 - INFO - 找到related_overlap场景: scene_id=270, 字幕#193
2025-07-29 10:40:58,131 - INFO - 找到related_between场景: scene_id=271, 字幕#193
2025-07-29 10:40:58,131 - INFO - 找到related_between场景: scene_id=272, 字幕#193
2025-07-29 10:40:58,131 - INFO - 找到related_between场景: scene_id=273, 字幕#193
2025-07-29 10:40:58,131 - INFO - 找到related_between场景: scene_id=274, 字幕#193
2025-07-29 10:40:58,131 - INFO - 找到related_between场景: scene_id=275, 字幕#193
2025-07-29 10:40:58,131 - INFO - 字幕 #193 找到 2 个overlap场景, 5 个between场景
2025-07-29 10:40:58,131 - INFO - 字幕序号 #193 找到 2 个可用overlap场景, 5 个可用between场景
2025-07-29 10:40:58,131 - INFO - 选择第一个overlap场景作为起点: scene_id=269
2025-07-29 10:40:58,131 - INFO - 添加起点场景: scene_id=269, 时长=6.36秒, 累计时长=6.36秒
2025-07-29 10:40:58,132 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:40:58,132 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:40:58,132 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:58,132 - INFO - ========== 当前模式：为字幕 #33 生成 1 套场景方案 ==========
2025-07-29 10:40:58,132 - INFO - 开始查找字幕序号 [186, 193] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:58,132 - INFO - 找到related_overlap场景: scene_id=264, 字幕#186
2025-07-29 10:40:58,132 - INFO - 找到related_overlap场景: scene_id=269, 字幕#193
2025-07-29 10:40:58,132 - INFO - 找到related_overlap场景: scene_id=270, 字幕#193
2025-07-29 10:40:58,132 - INFO - 找到related_between场景: scene_id=261, 字幕#186
2025-07-29 10:40:58,132 - INFO - 找到related_between场景: scene_id=262, 字幕#186
2025-07-29 10:40:58,132 - INFO - 找到related_between场景: scene_id=263, 字幕#186
2025-07-29 10:40:58,132 - INFO - 找到related_between场景: scene_id=271, 字幕#193
2025-07-29 10:40:58,132 - INFO - 找到related_between场景: scene_id=272, 字幕#193
2025-07-29 10:40:58,132 - INFO - 找到related_between场景: scene_id=273, 字幕#193
2025-07-29 10:40:58,132 - INFO - 找到related_between场景: scene_id=274, 字幕#193
2025-07-29 10:40:58,132 - INFO - 找到related_between场景: scene_id=275, 字幕#193
2025-07-29 10:40:58,133 - INFO - 字幕 #186 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:40:58,133 - INFO - 字幕 #193 找到 2 个overlap场景, 5 个between场景
2025-07-29 10:40:58,133 - INFO - 共收集 3 个未使用的overlap场景和 8 个未使用的between场景
2025-07-29 10:40:58,133 - INFO - 开始生成方案 #1
2025-07-29 10:40:58,133 - INFO - 方案 #1: 为字幕#186选择初始化overlap场景id=264
2025-07-29 10:40:58,133 - INFO - 方案 #1: 为字幕#193选择初始化overlap场景id=269
2025-07-29 10:40:58,133 - INFO - 方案 #1: 初始选择后，当前总时长=9.32秒
2025-07-29 10:40:58,133 - INFO - 方案 #1: 额外between选择后，当前总时长=9.32秒
2025-07-29 10:40:58,133 - INFO - 方案 #1: 场景总时长(9.32秒)大于音频时长(3.00秒)，需要裁剪
2025-07-29 10:40:58,133 - INFO - 调整前总时长: 9.32秒, 目标时长: 3.00秒
2025-07-29 10:40:58,133 - INFO - 需要裁剪 6.32秒
2025-07-29 10:40:58,133 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:40:58,133 - INFO - 裁剪场景ID=269：从6.36秒裁剪至1.91秒
2025-07-29 10:40:58,133 - INFO - 裁剪场景ID=264：从2.96秒裁剪至1.91秒
2025-07-29 10:40:58,133 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.81秒
2025-07-29 10:40:58,133 - INFO - 调整后总时长: 3.82秒，与目标时长差异: 0.81秒
2025-07-29 10:40:58,133 - INFO - 方案 #1 调整/填充后最终总时长: 3.82秒
2025-07-29 10:40:58,133 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:58,133 - INFO - ========== 当前模式：字幕 #33 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:58,133 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:58,133 - INFO - ========== 新模式：字幕 #33 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:58,133 - INFO - 
----- 处理字幕 #33 的方案 #1 -----
2025-07-29 10:40:58,133 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-29 10:40:58,134 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7aib3oqg
2025-07-29 10:40:58,134 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\264.mp4 (确认存在: True)
2025-07-29 10:40:58,134 - INFO - 添加场景ID=264，时长=2.96秒，累计时长=2.96秒
2025-07-29 10:40:58,134 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\265.mp4 (确认存在: True)
2025-07-29 10:40:58,134 - INFO - 添加场景ID=265，时长=2.60秒，累计时长=5.56秒
2025-07-29 10:40:58,134 - INFO - 场景总时长(5.56秒)已达到音频时长(3.00秒)的1.5倍，停止添加场景
2025-07-29 10:40:58,134 - INFO - 准备合并 2 个场景文件，总时长约 5.56秒
2025-07-29 10:40:58,134 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/264.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/265.mp4'

2025-07-29 10:40:58,134 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7aib3oqg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7aib3oqg\temp_combined.mp4
2025-07-29 10:40:58,265 - INFO - 合并后的视频时长: 5.61秒，目标音频时长: 3.00秒
2025-07-29 10:40:58,265 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7aib3oqg\temp_combined.mp4 -ss 0 -to 3.001 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-29 10:40:58,570 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:58,570 - INFO - 目标音频时长: 3.00秒
2025-07-29 10:40:58,570 - INFO - 实际视频时长: 3.06秒
2025-07-29 10:40:58,570 - INFO - 时长差异: 0.06秒 (2.07%)
2025-07-29 10:40:58,570 - INFO - ==========================================
2025-07-29 10:40:58,570 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:58,570 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-29 10:40:58,571 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7aib3oqg
2025-07-29 10:40:58,617 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:58,617 - INFO -   - 音频时长: 3.00秒
2025-07-29 10:40:58,617 - INFO -   - 视频时长: 3.06秒
2025-07-29 10:40:58,617 - INFO -   - 时长差异: 0.06秒 (2.07%)
2025-07-29 10:40:58,617 - INFO - 
----- 处理字幕 #33 的方案 #2 -----
2025-07-29 10:40:58,617 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\33_2.mp4
2025-07-29 10:40:58,618 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4_3hs5wa
2025-07-29 10:40:58,618 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\269.mp4 (确认存在: True)
2025-07-29 10:40:58,618 - INFO - 添加场景ID=269，时长=6.36秒，累计时长=6.36秒
2025-07-29 10:40:58,618 - INFO - 场景总时长(6.36秒)已达到音频时长(3.00秒)的1.5倍，停止添加场景
2025-07-29 10:40:58,618 - INFO - 准备合并 1 个场景文件，总时长约 6.36秒
2025-07-29 10:40:58,618 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/269.mp4'

2025-07-29 10:40:58,620 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp4_3hs5wa\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp4_3hs5wa\temp_combined.mp4
2025-07-29 10:40:58,742 - INFO - 合并后的视频时长: 6.38秒，目标音频时长: 3.00秒
2025-07-29 10:40:58,742 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp4_3hs5wa\temp_combined.mp4 -ss 0 -to 3.001 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\33_2.mp4
2025-07-29 10:40:59,081 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:59,081 - INFO - 目标音频时长: 3.00秒
2025-07-29 10:40:59,081 - INFO - 实际视频时长: 3.06秒
2025-07-29 10:40:59,081 - INFO - 时长差异: 0.06秒 (2.07%)
2025-07-29 10:40:59,081 - INFO - ==========================================
2025-07-29 10:40:59,081 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:59,081 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\33_2.mp4
2025-07-29 10:40:59,081 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4_3hs5wa
2025-07-29 10:40:59,125 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:59,125 - INFO -   - 音频时长: 3.00秒
2025-07-29 10:40:59,125 - INFO -   - 视频时长: 3.06秒
2025-07-29 10:40:59,125 - INFO -   - 时长差异: 0.06秒 (2.07%)
2025-07-29 10:40:59,125 - INFO - 
----- 处理字幕 #33 的方案 #3 -----
2025-07-29 10:40:59,125 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\33_3.mp4
2025-07-29 10:40:59,126 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc4zajwj0
2025-07-29 10:40:59,127 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\264.mp4 (确认存在: True)
2025-07-29 10:40:59,127 - INFO - 添加场景ID=264，时长=2.96秒，累计时长=2.96秒
2025-07-29 10:40:59,127 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\269.mp4 (确认存在: True)
2025-07-29 10:40:59,127 - INFO - 添加场景ID=269，时长=6.36秒，累计时长=9.32秒
2025-07-29 10:40:59,127 - INFO - 场景总时长(9.32秒)已达到音频时长(3.00秒)的1.5倍，停止添加场景
2025-07-29 10:40:59,127 - INFO - 准备合并 2 个场景文件，总时长约 9.32秒
2025-07-29 10:40:59,127 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/264.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/269.mp4'

2025-07-29 10:40:59,127 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpc4zajwj0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpc4zajwj0\temp_combined.mp4
2025-07-29 10:40:59,246 - INFO - 合并后的视频时长: 9.37秒，目标音频时长: 3.00秒
2025-07-29 10:40:59,247 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpc4zajwj0\temp_combined.mp4 -ss 0 -to 3.001 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\33_3.mp4
2025-07-29 10:40:59,540 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:59,540 - INFO - 目标音频时长: 3.00秒
2025-07-29 10:40:59,540 - INFO - 实际视频时长: 3.06秒
2025-07-29 10:40:59,540 - INFO - 时长差异: 0.06秒 (2.07%)
2025-07-29 10:40:59,540 - INFO - ==========================================
2025-07-29 10:40:59,540 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:59,540 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\33_3.mp4
2025-07-29 10:40:59,541 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc4zajwj0
2025-07-29 10:40:59,584 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:59,584 - INFO -   - 音频时长: 3.00秒
2025-07-29 10:40:59,584 - INFO -   - 视频时长: 3.06秒
2025-07-29 10:40:59,584 - INFO -   - 时长差异: 0.06秒 (2.07%)
2025-07-29 10:40:59,584 - INFO - 
字幕 #33 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:59,584 - INFO - 生成的视频文件:
2025-07-29 10:40:59,584 - INFO -   1. F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-29 10:40:59,584 - INFO -   2. F:/github/aicut_auto/newcut_ai\33_2.mp4
2025-07-29 10:40:59,584 - INFO -   3. F:/github/aicut_auto/newcut_ai\33_3.mp4
2025-07-29 10:40:59,584 - INFO - ========== 字幕 #33 处理结束 ==========

