2025-07-29 10:41:53,930 - INFO - ========== 字幕 #68 处理开始 ==========
2025-07-29 10:41:53,930 - INFO - 字幕内容: 哥哥让她去顶罪，理由是九姨娘怀了他的骨肉。
2025-07-29 10:41:53,930 - INFO - 字幕序号: [1290, 1295]
2025-07-29 10:41:53,930 - INFO - 音频文件详情:
2025-07-29 10:41:53,931 - INFO -   - 路径: output\68.wav
2025-07-29 10:41:53,931 - INFO -   - 时长: 3.54秒
2025-07-29 10:41:53,931 - INFO -   - 验证音频时长: 3.54秒
2025-07-29 10:41:53,931 - INFO - 字幕时间戳信息:
2025-07-29 10:41:53,931 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:53,931 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:53,932 - INFO -   - 根据生成的音频时长(3.54秒)已调整字幕时间戳
2025-07-29 10:41:53,932 - INFO - ========== 新模式：为字幕 #68 生成4套场景方案 ==========
2025-07-29 10:41:53,932 - INFO - 字幕序号列表: [1290, 1295]
2025-07-29 10:41:53,932 - INFO - 
--- 生成方案 #1：基于字幕序号 #1290 ---
2025-07-29 10:41:53,932 - INFO - 开始为单个字幕序号 #1290 匹配场景，目标时长: 3.54秒
2025-07-29 10:41:53,932 - INFO - 开始查找字幕序号 [1290] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:53,933 - INFO - 找到related_overlap场景: scene_id=1639, 字幕#1290
2025-07-29 10:41:53,934 - INFO - 找到related_between场景: scene_id=1638, 字幕#1290
2025-07-29 10:41:53,936 - INFO - 字幕 #1290 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:53,936 - INFO - 字幕序号 #1290 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 10:41:53,936 - INFO - 选择第一个overlap场景作为起点: scene_id=1639
2025-07-29 10:41:53,936 - INFO - 添加起点场景: scene_id=1639, 时长=1.40秒, 累计时长=1.40秒
2025-07-29 10:41:53,936 - INFO - 起点场景时长不足，需要延伸填充 2.15秒
2025-07-29 10:41:53,937 - INFO - 起点场景在原始列表中的索引: 1638
2025-07-29 10:41:53,937 - INFO - 延伸添加场景: scene_id=1640 (完整时长 0.76秒)
2025-07-29 10:41:53,937 - INFO - 累计时长: 2.16秒
2025-07-29 10:41:53,937 - INFO - 延伸添加场景: scene_id=1641 (完整时长 1.08秒)
2025-07-29 10:41:53,937 - INFO - 累计时长: 3.24秒
2025-07-29 10:41:53,937 - INFO - 延伸添加场景: scene_id=1642 (裁剪至 0.31秒)
2025-07-29 10:41:53,937 - INFO - 累计时长: 3.54秒
2025-07-29 10:41:53,937 - INFO - 字幕序号 #1290 场景匹配完成，共选择 4 个场景，总时长: 3.54秒
2025-07-29 10:41:53,937 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 10:41:53,937 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 10:41:53,937 - INFO - 
--- 生成方案 #2：基于字幕序号 #1295 ---
2025-07-29 10:41:53,937 - INFO - 开始为单个字幕序号 #1295 匹配场景，目标时长: 3.54秒
2025-07-29 10:41:53,937 - INFO - 开始查找字幕序号 [1295] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:53,937 - INFO - 找到related_overlap场景: scene_id=1644, 字幕#1295
2025-07-29 10:41:53,937 - INFO - 找到related_overlap场景: scene_id=1646, 字幕#1295
2025-07-29 10:41:53,940 - INFO - 字幕 #1295 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:53,940 - INFO - 字幕序号 #1295 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:53,940 - INFO - 选择第一个overlap场景作为起点: scene_id=1644
2025-07-29 10:41:53,940 - INFO - 添加起点场景: scene_id=1644, 时长=1.28秒, 累计时长=1.28秒
2025-07-29 10:41:53,940 - INFO - 起点场景时长不足，需要延伸填充 2.26秒
2025-07-29 10:41:53,942 - INFO - 起点场景在原始列表中的索引: 1643
2025-07-29 10:41:53,942 - INFO - 延伸添加场景: scene_id=1645 (完整时长 1.04秒)
2025-07-29 10:41:53,942 - INFO - 累计时长: 2.32秒
2025-07-29 10:41:53,942 - INFO - 延伸添加场景: scene_id=1646 (裁剪至 1.23秒)
2025-07-29 10:41:53,942 - INFO - 累计时长: 3.54秒
2025-07-29 10:41:53,942 - INFO - 字幕序号 #1295 场景匹配完成，共选择 3 个场景，总时长: 3.54秒
2025-07-29 10:41:53,942 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:41:53,942 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:53,942 - INFO - ========== 当前模式：为字幕 #68 生成 1 套场景方案 ==========
2025-07-29 10:41:53,942 - INFO - 开始查找字幕序号 [1290, 1295] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:53,943 - INFO - 找到related_overlap场景: scene_id=1639, 字幕#1290
2025-07-29 10:41:53,943 - INFO - 找到related_overlap场景: scene_id=1644, 字幕#1295
2025-07-29 10:41:53,943 - INFO - 找到related_overlap场景: scene_id=1646, 字幕#1295
2025-07-29 10:41:53,944 - INFO - 找到related_between场景: scene_id=1638, 字幕#1290
2025-07-29 10:41:53,944 - INFO - 字幕 #1290 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:53,944 - INFO - 字幕 #1295 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:53,945 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 10:41:53,945 - INFO - 开始生成方案 #1
2025-07-29 10:41:53,945 - INFO - 方案 #1: 为字幕#1290选择初始化overlap场景id=1639
2025-07-29 10:41:53,945 - INFO - 方案 #1: 为字幕#1295选择初始化overlap场景id=1646
2025-07-29 10:41:53,945 - INFO - 方案 #1: 初始选择后，当前总时长=2.76秒
2025-07-29 10:41:53,945 - INFO - 方案 #1: 额外添加overlap场景id=1644, 当前总时长=4.04秒
2025-07-29 10:41:53,945 - INFO - 方案 #1: 额外between选择后，当前总时长=4.04秒
2025-07-29 10:41:53,945 - INFO - 方案 #1: 场景总时长(4.04秒)大于音频时长(3.54秒)，需要裁剪
2025-07-29 10:41:53,945 - INFO - 调整前总时长: 4.04秒, 目标时长: 3.54秒
2025-07-29 10:41:53,945 - INFO - 需要裁剪 0.50秒
2025-07-29 10:41:53,945 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:53,945 - INFO - 裁剪场景ID=1639：从1.40秒裁剪至1.00秒
2025-07-29 10:41:53,945 - INFO - 裁剪场景ID=1646：从1.36秒裁剪至1.26秒
2025-07-29 10:41:53,945 - INFO - 调整后总时长: 3.54秒，与目标时长差异: 0.00秒
2025-07-29 10:41:53,945 - INFO - 方案 #1 调整/填充后最终总时长: 3.54秒
2025-07-29 10:41:53,945 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:53,945 - INFO - ========== 当前模式：字幕 #68 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:53,945 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:53,945 - INFO - ========== 新模式：字幕 #68 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:53,945 - INFO - 
----- 处理字幕 #68 的方案 #1 -----
2025-07-29 10:41:53,946 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-07-29 10:41:53,946 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt5nxlgbx
2025-07-29 10:41:53,947 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1639.mp4 (确认存在: True)
2025-07-29 10:41:53,947 - INFO - 添加场景ID=1639，时长=1.40秒，累计时长=1.40秒
2025-07-29 10:41:53,947 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1640.mp4 (确认存在: True)
2025-07-29 10:41:53,947 - INFO - 添加场景ID=1640，时长=0.76秒，累计时长=2.16秒
2025-07-29 10:41:53,947 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1641.mp4 (确认存在: True)
2025-07-29 10:41:53,947 - INFO - 添加场景ID=1641，时长=1.08秒，累计时长=3.24秒
2025-07-29 10:41:53,947 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1642.mp4 (确认存在: True)
2025-07-29 10:41:53,947 - INFO - 添加场景ID=1642，时长=1.56秒，累计时长=4.80秒
2025-07-29 10:41:53,948 - INFO - 准备合并 4 个场景文件，总时长约 4.80秒
2025-07-29 10:41:53,948 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1639.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1640.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1641.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1642.mp4'

2025-07-29 10:41:53,948 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpt5nxlgbx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpt5nxlgbx\temp_combined.mp4
2025-07-29 10:41:54,147 - INFO - 合并后的视频时长: 4.89秒，目标音频时长: 3.54秒
2025-07-29 10:41:54,147 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpt5nxlgbx\temp_combined.mp4 -ss 0 -to 3.545 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-07-29 10:41:54,458 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:54,458 - INFO - 目标音频时长: 3.54秒
2025-07-29 10:41:54,459 - INFO - 实际视频时长: 3.58秒
2025-07-29 10:41:54,459 - INFO - 时长差异: 0.04秒 (1.07%)
2025-07-29 10:41:54,459 - INFO - ==========================================
2025-07-29 10:41:54,459 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:54,459 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-07-29 10:41:54,459 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt5nxlgbx
2025-07-29 10:41:54,521 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:54,521 - INFO -   - 音频时长: 3.54秒
2025-07-29 10:41:54,521 - INFO -   - 视频时长: 3.58秒
2025-07-29 10:41:54,521 - INFO -   - 时长差异: 0.04秒 (1.07%)
2025-07-29 10:41:54,521 - INFO - 
----- 处理字幕 #68 的方案 #2 -----
2025-07-29 10:41:54,521 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-07-29 10:41:54,522 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiwncw_bd
2025-07-29 10:41:54,522 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1644.mp4 (确认存在: True)
2025-07-29 10:41:54,522 - INFO - 添加场景ID=1644，时长=1.28秒，累计时长=1.28秒
2025-07-29 10:41:54,522 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1645.mp4 (确认存在: True)
2025-07-29 10:41:54,522 - INFO - 添加场景ID=1645，时长=1.04秒，累计时长=2.32秒
2025-07-29 10:41:54,522 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1646.mp4 (确认存在: True)
2025-07-29 10:41:54,522 - INFO - 添加场景ID=1646，时长=1.36秒，累计时长=3.68秒
2025-07-29 10:41:54,522 - INFO - 准备合并 3 个场景文件，总时长约 3.68秒
2025-07-29 10:41:54,522 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1644.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1645.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1646.mp4'

2025-07-29 10:41:54,523 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpiwncw_bd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpiwncw_bd\temp_combined.mp4
2025-07-29 10:41:54,679 - INFO - 合并后的视频时长: 3.75秒，目标音频时长: 3.54秒
2025-07-29 10:41:54,679 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpiwncw_bd\temp_combined.mp4 -ss 0 -to 3.545 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-07-29 10:41:55,024 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:55,024 - INFO - 目标音频时长: 3.54秒
2025-07-29 10:41:55,024 - INFO - 实际视频时长: 3.58秒
2025-07-29 10:41:55,024 - INFO - 时长差异: 0.04秒 (1.07%)
2025-07-29 10:41:55,024 - INFO - ==========================================
2025-07-29 10:41:55,024 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:55,024 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-07-29 10:41:55,025 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiwncw_bd
2025-07-29 10:41:55,087 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:55,087 - INFO -   - 音频时长: 3.54秒
2025-07-29 10:41:55,087 - INFO -   - 视频时长: 3.58秒
2025-07-29 10:41:55,087 - INFO -   - 时长差异: 0.04秒 (1.07%)
2025-07-29 10:41:55,087 - INFO - 
----- 处理字幕 #68 的方案 #3 -----
2025-07-29 10:41:55,088 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-07-29 10:41:55,088 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpka1s6p52
2025-07-29 10:41:55,089 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1639.mp4 (确认存在: True)
2025-07-29 10:41:55,089 - INFO - 添加场景ID=1639，时长=1.40秒，累计时长=1.40秒
2025-07-29 10:41:55,089 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1646.mp4 (确认存在: True)
2025-07-29 10:41:55,089 - INFO - 添加场景ID=1646，时长=1.36秒，累计时长=2.76秒
2025-07-29 10:41:55,089 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1644.mp4 (确认存在: True)
2025-07-29 10:41:55,089 - INFO - 添加场景ID=1644，时长=1.28秒，累计时长=4.04秒
2025-07-29 10:41:55,090 - INFO - 准备合并 3 个场景文件，总时长约 4.04秒
2025-07-29 10:41:55,090 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1639.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1646.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1644.mp4'

2025-07-29 10:41:55,090 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpka1s6p52\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpka1s6p52\temp_combined.mp4
2025-07-29 10:41:55,278 - INFO - 合并后的视频时长: 4.11秒，目标音频时长: 3.54秒
2025-07-29 10:41:55,278 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpka1s6p52\temp_combined.mp4 -ss 0 -to 3.545 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-07-29 10:41:55,568 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:55,568 - INFO - 目标音频时长: 3.54秒
2025-07-29 10:41:55,568 - INFO - 实际视频时长: 3.58秒
2025-07-29 10:41:55,568 - INFO - 时长差异: 0.04秒 (1.07%)
2025-07-29 10:41:55,568 - INFO - ==========================================
2025-07-29 10:41:55,568 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:55,568 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-07-29 10:41:55,569 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpka1s6p52
2025-07-29 10:41:55,627 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:55,627 - INFO -   - 音频时长: 3.54秒
2025-07-29 10:41:55,627 - INFO -   - 视频时长: 3.58秒
2025-07-29 10:41:55,627 - INFO -   - 时长差异: 0.04秒 (1.07%)
2025-07-29 10:41:55,627 - INFO - 
字幕 #68 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:55,627 - INFO - 生成的视频文件:
2025-07-29 10:41:55,627 - INFO -   1. F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-07-29 10:41:55,627 - INFO -   2. F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-07-29 10:41:55,627 - INFO -   3. F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-07-29 10:41:55,627 - INFO - ========== 字幕 #68 处理结束 ==========

