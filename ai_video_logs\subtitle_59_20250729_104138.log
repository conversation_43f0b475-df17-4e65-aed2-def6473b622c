2025-07-29 10:41:38,970 - INFO - ========== 字幕 #59 处理开始 ==========
2025-07-29 10:41:38,970 - INFO - 字幕内容: 摄政王指出，皇后想将她献给陛下，此计凶险。
2025-07-29 10:41:38,970 - INFO - 字幕序号: [1228, 1233]
2025-07-29 10:41:38,971 - INFO - 音频文件详情:
2025-07-29 10:41:38,971 - INFO -   - 路径: output\59.wav
2025-07-29 10:41:38,971 - INFO -   - 时长: 3.82秒
2025-07-29 10:41:38,971 - INFO -   - 验证音频时长: 3.82秒
2025-07-29 10:41:38,971 - INFO - 字幕时间戳信息:
2025-07-29 10:41:38,971 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:38,981 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:38,981 - INFO -   - 根据生成的音频时长(3.82秒)已调整字幕时间戳
2025-07-29 10:41:38,981 - INFO - ========== 新模式：为字幕 #59 生成4套场景方案 ==========
2025-07-29 10:41:38,981 - INFO - 字幕序号列表: [1228, 1233]
2025-07-29 10:41:38,981 - INFO - 
--- 生成方案 #1：基于字幕序号 #1228 ---
2025-07-29 10:41:38,981 - INFO - 开始为单个字幕序号 #1228 匹配场景，目标时长: 3.82秒
2025-07-29 10:41:38,981 - INFO - 开始查找字幕序号 [1228] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:38,982 - INFO - 找到related_overlap场景: scene_id=1592, 字幕#1228
2025-07-29 10:41:38,982 - INFO - 找到related_overlap场景: scene_id=1593, 字幕#1228
2025-07-29 10:41:38,984 - INFO - 字幕 #1228 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:38,984 - INFO - 字幕序号 #1228 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:38,984 - INFO - 选择第一个overlap场景作为起点: scene_id=1592
2025-07-29 10:41:38,984 - INFO - 添加起点场景: scene_id=1592, 时长=2.76秒, 累计时长=2.76秒
2025-07-29 10:41:38,984 - INFO - 起点场景时长不足，需要延伸填充 1.06秒
2025-07-29 10:41:38,984 - INFO - 起点场景在原始列表中的索引: 1591
2025-07-29 10:41:38,984 - INFO - 延伸添加场景: scene_id=1593 (裁剪至 1.06秒)
2025-07-29 10:41:38,984 - INFO - 累计时长: 3.82秒
2025-07-29 10:41:38,985 - INFO - 字幕序号 #1228 场景匹配完成，共选择 2 个场景，总时长: 3.82秒
2025-07-29 10:41:38,985 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:38,985 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:38,985 - INFO - 
--- 生成方案 #2：基于字幕序号 #1233 ---
2025-07-29 10:41:38,985 - INFO - 开始为单个字幕序号 #1233 匹配场景，目标时长: 3.82秒
2025-07-29 10:41:38,985 - INFO - 开始查找字幕序号 [1233] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:38,985 - INFO - 找到related_overlap场景: scene_id=1594, 字幕#1233
2025-07-29 10:41:38,985 - INFO - 找到related_overlap场景: scene_id=1595, 字幕#1233
2025-07-29 10:41:38,986 - INFO - 字幕 #1233 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:38,986 - INFO - 字幕序号 #1233 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:38,986 - INFO - 选择第一个overlap场景作为起点: scene_id=1594
2025-07-29 10:41:38,986 - INFO - 添加起点场景: scene_id=1594, 时长=2.08秒, 累计时长=2.08秒
2025-07-29 10:41:38,986 - INFO - 起点场景时长不足，需要延伸填充 1.74秒
2025-07-29 10:41:38,986 - INFO - 起点场景在原始列表中的索引: 1593
2025-07-29 10:41:38,986 - INFO - 延伸添加场景: scene_id=1595 (裁剪至 1.74秒)
2025-07-29 10:41:38,986 - INFO - 累计时长: 3.82秒
2025-07-29 10:41:38,987 - INFO - 字幕序号 #1233 场景匹配完成，共选择 2 个场景，总时长: 3.82秒
2025-07-29 10:41:38,987 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:38,987 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:38,987 - INFO - ========== 当前模式：为字幕 #59 生成 1 套场景方案 ==========
2025-07-29 10:41:38,987 - INFO - 开始查找字幕序号 [1228, 1233] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:38,987 - INFO - 找到related_overlap场景: scene_id=1592, 字幕#1228
2025-07-29 10:41:38,987 - INFO - 找到related_overlap场景: scene_id=1593, 字幕#1228
2025-07-29 10:41:38,987 - INFO - 找到related_overlap场景: scene_id=1594, 字幕#1233
2025-07-29 10:41:38,987 - INFO - 找到related_overlap场景: scene_id=1595, 字幕#1233
2025-07-29 10:41:38,989 - INFO - 字幕 #1228 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:38,989 - INFO - 字幕 #1233 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:38,989 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:38,989 - INFO - 开始生成方案 #1
2025-07-29 10:41:38,989 - INFO - 方案 #1: 为字幕#1228选择初始化overlap场景id=1593
2025-07-29 10:41:38,989 - INFO - 方案 #1: 为字幕#1233选择初始化overlap场景id=1594
2025-07-29 10:41:38,989 - INFO - 方案 #1: 初始选择后，当前总时长=8.40秒
2025-07-29 10:41:38,989 - INFO - 方案 #1: 额外between选择后，当前总时长=8.40秒
2025-07-29 10:41:38,989 - INFO - 方案 #1: 场景总时长(8.40秒)大于音频时长(3.82秒)，需要裁剪
2025-07-29 10:41:38,989 - INFO - 调整前总时长: 8.40秒, 目标时长: 3.82秒
2025-07-29 10:41:38,989 - INFO - 需要裁剪 4.58秒
2025-07-29 10:41:38,989 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:38,989 - INFO - 裁剪场景ID=1593：从6.32秒裁剪至1.90秒
2025-07-29 10:41:38,989 - INFO - 裁剪场景ID=1594：从2.08秒裁剪至1.93秒
2025-07-29 10:41:38,989 - INFO - 调整后总时长: 3.82秒，与目标时长差异: 0.00秒
2025-07-29 10:41:38,989 - INFO - 方案 #1 调整/填充后最终总时长: 3.82秒
2025-07-29 10:41:38,989 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:38,989 - INFO - ========== 当前模式：字幕 #59 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:38,989 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:38,989 - INFO - ========== 新模式：字幕 #59 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:38,990 - INFO - 
----- 处理字幕 #59 的方案 #1 -----
2025-07-29 10:41:38,990 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-29 10:41:38,990 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsaxa1vd2
2025-07-29 10:41:38,991 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1592.mp4 (确认存在: True)
2025-07-29 10:41:38,991 - INFO - 添加场景ID=1592，时长=2.76秒，累计时长=2.76秒
2025-07-29 10:41:38,991 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1593.mp4 (确认存在: True)
2025-07-29 10:41:38,991 - INFO - 添加场景ID=1593，时长=6.32秒，累计时长=9.08秒
2025-07-29 10:41:38,991 - INFO - 场景总时长(9.08秒)已达到音频时长(3.82秒)的1.5倍，停止添加场景
2025-07-29 10:41:38,991 - INFO - 准备合并 2 个场景文件，总时长约 9.08秒
2025-07-29 10:41:38,992 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1592.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1593.mp4'

2025-07-29 10:41:38,992 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsaxa1vd2\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsaxa1vd2\temp_combined.mp4
2025-07-29 10:41:39,150 - INFO - 合并后的视频时长: 9.13秒，目标音频时长: 3.82秒
2025-07-29 10:41:39,150 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsaxa1vd2\temp_combined.mp4 -ss 0 -to 3.824 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-29 10:41:39,486 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:39,486 - INFO - 目标音频时长: 3.82秒
2025-07-29 10:41:39,487 - INFO - 实际视频时长: 3.86秒
2025-07-29 10:41:39,487 - INFO - 时长差异: 0.04秒 (1.02%)
2025-07-29 10:41:39,487 - INFO - ==========================================
2025-07-29 10:41:39,487 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:39,487 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-29 10:41:39,487 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsaxa1vd2
2025-07-29 10:41:39,550 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:39,550 - INFO -   - 音频时长: 3.82秒
2025-07-29 10:41:39,550 - INFO -   - 视频时长: 3.86秒
2025-07-29 10:41:39,550 - INFO -   - 时长差异: 0.04秒 (1.02%)
2025-07-29 10:41:39,550 - INFO - 
----- 处理字幕 #59 的方案 #2 -----
2025-07-29 10:41:39,550 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-07-29 10:41:39,550 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfmvs8dan
2025-07-29 10:41:39,551 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1594.mp4 (确认存在: True)
2025-07-29 10:41:39,551 - INFO - 添加场景ID=1594，时长=2.08秒，累计时长=2.08秒
2025-07-29 10:41:39,551 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1595.mp4 (确认存在: True)
2025-07-29 10:41:39,551 - INFO - 添加场景ID=1595，时长=4.88秒，累计时长=6.96秒
2025-07-29 10:41:39,551 - INFO - 场景总时长(6.96秒)已达到音频时长(3.82秒)的1.5倍，停止添加场景
2025-07-29 10:41:39,552 - INFO - 准备合并 2 个场景文件，总时长约 6.96秒
2025-07-29 10:41:39,552 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1594.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1595.mp4'

2025-07-29 10:41:39,552 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfmvs8dan\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfmvs8dan\temp_combined.mp4
2025-07-29 10:41:39,721 - INFO - 合并后的视频时长: 7.01秒，目标音频时长: 3.82秒
2025-07-29 10:41:39,721 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfmvs8dan\temp_combined.mp4 -ss 0 -to 3.824 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-07-29 10:41:40,055 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:40,055 - INFO - 目标音频时长: 3.82秒
2025-07-29 10:41:40,055 - INFO - 实际视频时长: 3.86秒
2025-07-29 10:41:40,056 - INFO - 时长差异: 0.04秒 (1.02%)
2025-07-29 10:41:40,056 - INFO - ==========================================
2025-07-29 10:41:40,056 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:40,056 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-07-29 10:41:40,057 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfmvs8dan
2025-07-29 10:41:40,122 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:40,122 - INFO -   - 音频时长: 3.82秒
2025-07-29 10:41:40,122 - INFO -   - 视频时长: 3.86秒
2025-07-29 10:41:40,122 - INFO -   - 时长差异: 0.04秒 (1.02%)
2025-07-29 10:41:40,122 - INFO - 
----- 处理字幕 #59 的方案 #3 -----
2025-07-29 10:41:40,122 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\59_3.mp4
2025-07-29 10:41:40,123 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwtom44jf
2025-07-29 10:41:40,124 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1593.mp4 (确认存在: True)
2025-07-29 10:41:40,124 - INFO - 添加场景ID=1593，时长=6.32秒，累计时长=6.32秒
2025-07-29 10:41:40,124 - INFO - 场景总时长(6.32秒)已达到音频时长(3.82秒)的1.5倍，停止添加场景
2025-07-29 10:41:40,124 - INFO - 准备合并 1 个场景文件，总时长约 6.32秒
2025-07-29 10:41:40,124 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1593.mp4'

2025-07-29 10:41:40,124 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwtom44jf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwtom44jf\temp_combined.mp4
2025-07-29 10:41:40,282 - INFO - 合并后的视频时长: 6.34秒，目标音频时长: 3.82秒
2025-07-29 10:41:40,282 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwtom44jf\temp_combined.mp4 -ss 0 -to 3.824 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\59_3.mp4
2025-07-29 10:41:40,599 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:40,599 - INFO - 目标音频时长: 3.82秒
2025-07-29 10:41:40,599 - INFO - 实际视频时长: 3.86秒
2025-07-29 10:41:40,600 - INFO - 时长差异: 0.04秒 (1.02%)
2025-07-29 10:41:40,600 - INFO - ==========================================
2025-07-29 10:41:40,600 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:40,600 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\59_3.mp4
2025-07-29 10:41:40,601 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwtom44jf
2025-07-29 10:41:40,665 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:40,665 - INFO -   - 音频时长: 3.82秒
2025-07-29 10:41:40,665 - INFO -   - 视频时长: 3.86秒
2025-07-29 10:41:40,665 - INFO -   - 时长差异: 0.04秒 (1.02%)
2025-07-29 10:41:40,665 - INFO - 
字幕 #59 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:40,665 - INFO - 生成的视频文件:
2025-07-29 10:41:40,665 - INFO -   1. F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-07-29 10:41:40,665 - INFO -   2. F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-07-29 10:41:40,665 - INFO -   3. F:/github/aicut_auto/newcut_ai\59_3.mp4
2025-07-29 10:41:40,665 - INFO - ========== 字幕 #59 处理结束 ==========

