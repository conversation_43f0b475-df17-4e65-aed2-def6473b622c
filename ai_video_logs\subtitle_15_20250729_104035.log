2025-07-29 10:40:35,025 - INFO - ========== 字幕 #15 处理开始 ==========
2025-07-29 10:40:35,025 - INFO - 字幕内容: 她负气地想着，哥哥却立刻护住她，不让摄政王靠近。
2025-07-29 10:40:35,025 - INFO - 字幕序号: [67, 72]
2025-07-29 10:40:35,025 - INFO - 音频文件详情:
2025-07-29 10:40:35,025 - INFO -   - 路径: output\15.wav
2025-07-29 10:40:35,025 - INFO -   - 时长: 3.42秒
2025-07-29 10:40:35,025 - INFO -   - 验证音频时长: 3.42秒
2025-07-29 10:40:35,026 - INFO - 字幕时间戳信息:
2025-07-29 10:40:35,026 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:35,026 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:35,026 - INFO -   - 根据生成的音频时长(3.42秒)已调整字幕时间戳
2025-07-29 10:40:35,026 - INFO - ========== 新模式：为字幕 #15 生成4套场景方案 ==========
2025-07-29 10:40:35,026 - INFO - 字幕序号列表: [67, 72]
2025-07-29 10:40:35,026 - INFO - 
--- 生成方案 #1：基于字幕序号 #67 ---
2025-07-29 10:40:35,026 - INFO - 开始为单个字幕序号 #67 匹配场景，目标时长: 3.42秒
2025-07-29 10:40:35,026 - INFO - 开始查找字幕序号 [67] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:35,026 - INFO - 找到related_overlap场景: scene_id=89, 字幕#67
2025-07-29 10:40:35,026 - INFO - 找到related_overlap场景: scene_id=90, 字幕#67
2025-07-29 10:40:35,027 - INFO - 字幕 #67 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:35,027 - INFO - 字幕序号 #67 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:35,027 - INFO - 选择第一个overlap场景作为起点: scene_id=89
2025-07-29 10:40:35,027 - INFO - 添加起点场景: scene_id=89, 时长=0.96秒, 累计时长=0.96秒
2025-07-29 10:40:35,027 - INFO - 起点场景时长不足，需要延伸填充 2.46秒
2025-07-29 10:40:35,027 - INFO - 起点场景在原始列表中的索引: 88
2025-07-29 10:40:35,027 - INFO - 延伸添加场景: scene_id=90 (完整时长 1.04秒)
2025-07-29 10:40:35,027 - INFO - 累计时长: 2.00秒
2025-07-29 10:40:35,027 - INFO - 延伸添加场景: scene_id=91 (完整时长 1.28秒)
2025-07-29 10:40:35,027 - INFO - 累计时长: 3.28秒
2025-07-29 10:40:35,027 - INFO - 延伸添加场景: scene_id=92 (裁剪至 0.14秒)
2025-07-29 10:40:35,027 - INFO - 累计时长: 3.42秒
2025-07-29 10:40:35,027 - INFO - 字幕序号 #67 场景匹配完成，共选择 4 个场景，总时长: 3.42秒
2025-07-29 10:40:35,027 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 10:40:35,027 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 10:40:35,027 - INFO - 
--- 生成方案 #2：基于字幕序号 #72 ---
2025-07-29 10:40:35,027 - INFO - 开始为单个字幕序号 #72 匹配场景，目标时长: 3.42秒
2025-07-29 10:40:35,027 - INFO - 开始查找字幕序号 [72] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:35,029 - INFO - 找到related_overlap场景: scene_id=100, 字幕#72
2025-07-29 10:40:35,029 - INFO - 找到related_between场景: scene_id=98, 字幕#72
2025-07-29 10:40:35,029 - INFO - 找到related_between场景: scene_id=99, 字幕#72
2025-07-29 10:40:35,029 - INFO - 找到related_between场景: scene_id=101, 字幕#72
2025-07-29 10:40:35,029 - INFO - 字幕 #72 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:40:35,029 - INFO - 字幕序号 #72 找到 1 个可用overlap场景, 3 个可用between场景
2025-07-29 10:40:35,029 - INFO - 选择第一个overlap场景作为起点: scene_id=100
2025-07-29 10:40:35,029 - INFO - 添加起点场景: scene_id=100, 时长=3.68秒, 累计时长=3.68秒
2025-07-29 10:40:35,029 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:40:35,029 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:40:35,029 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:35,029 - INFO - ========== 当前模式：为字幕 #15 生成 1 套场景方案 ==========
2025-07-29 10:40:35,029 - INFO - 开始查找字幕序号 [67, 72] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:35,029 - INFO - 找到related_overlap场景: scene_id=89, 字幕#67
2025-07-29 10:40:35,029 - INFO - 找到related_overlap场景: scene_id=90, 字幕#67
2025-07-29 10:40:35,029 - INFO - 找到related_overlap场景: scene_id=100, 字幕#72
2025-07-29 10:40:35,030 - INFO - 找到related_between场景: scene_id=98, 字幕#72
2025-07-29 10:40:35,030 - INFO - 找到related_between场景: scene_id=99, 字幕#72
2025-07-29 10:40:35,030 - INFO - 找到related_between场景: scene_id=101, 字幕#72
2025-07-29 10:40:35,031 - INFO - 字幕 #67 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:35,031 - INFO - 字幕 #72 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:40:35,031 - INFO - 共收集 3 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 10:40:35,031 - INFO - 开始生成方案 #1
2025-07-29 10:40:35,031 - INFO - 方案 #1: 为字幕#67选择初始化overlap场景id=90
2025-07-29 10:40:35,031 - INFO - 方案 #1: 为字幕#72选择初始化overlap场景id=100
2025-07-29 10:40:35,031 - INFO - 方案 #1: 初始选择后，当前总时长=4.72秒
2025-07-29 10:40:35,031 - INFO - 方案 #1: 额外between选择后，当前总时长=4.72秒
2025-07-29 10:40:35,031 - INFO - 方案 #1: 场景总时长(4.72秒)大于音频时长(3.42秒)，需要裁剪
2025-07-29 10:40:35,031 - INFO - 调整前总时长: 4.72秒, 目标时长: 3.42秒
2025-07-29 10:40:35,031 - INFO - 需要裁剪 1.30秒
2025-07-29 10:40:35,031 - INFO - 裁剪最长场景ID=100：从3.68秒裁剪至2.38秒
2025-07-29 10:40:35,031 - INFO - 调整后总时长: 3.42秒，与目标时长差异: 0.00秒
2025-07-29 10:40:35,031 - INFO - 方案 #1 调整/填充后最终总时长: 3.42秒
2025-07-29 10:40:35,031 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:35,031 - INFO - ========== 当前模式：字幕 #15 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:35,031 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:35,031 - INFO - ========== 新模式：字幕 #15 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:35,031 - INFO - 
----- 处理字幕 #15 的方案 #1 -----
2025-07-29 10:40:35,031 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-29 10:40:35,032 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprtchb0vk
2025-07-29 10:40:35,032 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\89.mp4 (确认存在: True)
2025-07-29 10:40:35,032 - INFO - 添加场景ID=89，时长=0.96秒，累计时长=0.96秒
2025-07-29 10:40:35,032 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\90.mp4 (确认存在: True)
2025-07-29 10:40:35,032 - INFO - 添加场景ID=90，时长=1.04秒，累计时长=2.00秒
2025-07-29 10:40:35,032 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\91.mp4 (确认存在: True)
2025-07-29 10:40:35,032 - INFO - 添加场景ID=91，时长=1.28秒，累计时长=3.28秒
2025-07-29 10:40:35,032 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\92.mp4 (确认存在: True)
2025-07-29 10:40:35,032 - INFO - 添加场景ID=92，时长=2.16秒，累计时长=5.44秒
2025-07-29 10:40:35,032 - INFO - 场景总时长(5.44秒)已达到音频时长(3.42秒)的1.5倍，停止添加场景
2025-07-29 10:40:35,032 - INFO - 准备合并 4 个场景文件，总时长约 5.44秒
2025-07-29 10:40:35,032 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/89.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/90.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/91.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/92.mp4'

2025-07-29 10:40:35,033 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmprtchb0vk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmprtchb0vk\temp_combined.mp4
2025-07-29 10:40:35,202 - INFO - 合并后的视频时长: 5.53秒，目标音频时长: 3.42秒
2025-07-29 10:40:35,202 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmprtchb0vk\temp_combined.mp4 -ss 0 -to 3.418 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-29 10:40:35,485 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:35,485 - INFO - 目标音频时长: 3.42秒
2025-07-29 10:40:35,485 - INFO - 实际视频时长: 3.46秒
2025-07-29 10:40:35,485 - INFO - 时长差异: 0.04秒 (1.32%)
2025-07-29 10:40:35,485 - INFO - ==========================================
2025-07-29 10:40:35,485 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:35,485 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-29 10:40:35,486 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprtchb0vk
2025-07-29 10:40:35,536 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:35,536 - INFO -   - 音频时长: 3.42秒
2025-07-29 10:40:35,536 - INFO -   - 视频时长: 3.46秒
2025-07-29 10:40:35,536 - INFO -   - 时长差异: 0.04秒 (1.32%)
2025-07-29 10:40:35,536 - INFO - 
----- 处理字幕 #15 的方案 #2 -----
2025-07-29 10:40:35,536 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-07-29 10:40:35,536 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps38v447a
2025-07-29 10:40:35,536 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\100.mp4 (确认存在: True)
2025-07-29 10:40:35,537 - INFO - 添加场景ID=100，时长=3.68秒，累计时长=3.68秒
2025-07-29 10:40:35,537 - INFO - 准备合并 1 个场景文件，总时长约 3.68秒
2025-07-29 10:40:35,537 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/100.mp4'

2025-07-29 10:40:35,537 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmps38v447a\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmps38v447a\temp_combined.mp4
2025-07-29 10:40:35,659 - INFO - 合并后的视频时长: 3.70秒，目标音频时长: 3.42秒
2025-07-29 10:40:35,659 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmps38v447a\temp_combined.mp4 -ss 0 -to 3.418 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-07-29 10:40:35,915 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:35,915 - INFO - 目标音频时长: 3.42秒
2025-07-29 10:40:35,915 - INFO - 实际视频时长: 3.46秒
2025-07-29 10:40:35,915 - INFO - 时长差异: 0.04秒 (1.32%)
2025-07-29 10:40:35,915 - INFO - ==========================================
2025-07-29 10:40:35,915 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:35,915 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-07-29 10:40:35,916 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps38v447a
2025-07-29 10:40:35,959 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:35,959 - INFO -   - 音频时长: 3.42秒
2025-07-29 10:40:35,959 - INFO -   - 视频时长: 3.46秒
2025-07-29 10:40:35,959 - INFO -   - 时长差异: 0.04秒 (1.32%)
2025-07-29 10:40:35,959 - INFO - 
----- 处理字幕 #15 的方案 #3 -----
2025-07-29 10:40:35,959 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-07-29 10:40:35,960 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyn0hct_6
2025-07-29 10:40:35,961 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\90.mp4 (确认存在: True)
2025-07-29 10:40:35,961 - INFO - 添加场景ID=90，时长=1.04秒，累计时长=1.04秒
2025-07-29 10:40:35,961 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\100.mp4 (确认存在: True)
2025-07-29 10:40:35,961 - INFO - 添加场景ID=100，时长=3.68秒，累计时长=4.72秒
2025-07-29 10:40:35,961 - INFO - 准备合并 2 个场景文件，总时长约 4.72秒
2025-07-29 10:40:35,961 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/90.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/100.mp4'

2025-07-29 10:40:35,961 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpyn0hct_6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpyn0hct_6\temp_combined.mp4
2025-07-29 10:40:36,077 - INFO - 合并后的视频时长: 4.77秒，目标音频时长: 3.42秒
2025-07-29 10:40:36,077 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpyn0hct_6\temp_combined.mp4 -ss 0 -to 3.418 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-07-29 10:40:36,330 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:36,330 - INFO - 目标音频时长: 3.42秒
2025-07-29 10:40:36,330 - INFO - 实际视频时长: 3.46秒
2025-07-29 10:40:36,330 - INFO - 时长差异: 0.04秒 (1.32%)
2025-07-29 10:40:36,330 - INFO - ==========================================
2025-07-29 10:40:36,330 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:36,330 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-07-29 10:40:36,331 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyn0hct_6
2025-07-29 10:40:36,373 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:36,373 - INFO -   - 音频时长: 3.42秒
2025-07-29 10:40:36,373 - INFO -   - 视频时长: 3.46秒
2025-07-29 10:40:36,373 - INFO -   - 时长差异: 0.04秒 (1.32%)
2025-07-29 10:40:36,373 - INFO - 
字幕 #15 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:36,373 - INFO - 生成的视频文件:
2025-07-29 10:40:36,373 - INFO -   1. F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-29 10:40:36,373 - INFO -   2. F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-07-29 10:40:36,373 - INFO -   3. F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-07-29 10:40:36,373 - INFO - ========== 字幕 #15 处理结束 ==========

