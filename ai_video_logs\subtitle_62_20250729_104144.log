2025-07-29 10:41:44,215 - INFO - ========== 字幕 #62 处理开始 ==========
2025-07-29 10:41:44,215 - INFO - 字幕内容: 哥哥坦言自己做不到一生一世一双人，无法娶她。
2025-07-29 10:41:44,215 - INFO - 字幕序号: [1244, 1250]
2025-07-29 10:41:44,215 - INFO - 音频文件详情:
2025-07-29 10:41:44,215 - INFO -   - 路径: output\62.wav
2025-07-29 10:41:44,216 - INFO -   - 时长: 3.25秒
2025-07-29 10:41:44,216 - INFO -   - 验证音频时长: 3.25秒
2025-07-29 10:41:44,216 - INFO - 字幕时间戳信息:
2025-07-29 10:41:44,216 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:44,216 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:44,216 - INFO -   - 根据生成的音频时长(3.25秒)已调整字幕时间戳
2025-07-29 10:41:44,216 - INFO - ========== 新模式：为字幕 #62 生成4套场景方案 ==========
2025-07-29 10:41:44,216 - INFO - 字幕序号列表: [1244, 1250]
2025-07-29 10:41:44,216 - INFO - 
--- 生成方案 #1：基于字幕序号 #1244 ---
2025-07-29 10:41:44,216 - INFO - 开始为单个字幕序号 #1244 匹配场景，目标时长: 3.25秒
2025-07-29 10:41:44,216 - INFO - 开始查找字幕序号 [1244] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:44,217 - INFO - 找到related_overlap场景: scene_id=1603, 字幕#1244
2025-07-29 10:41:44,219 - INFO - 字幕 #1244 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:44,219 - INFO - 字幕序号 #1244 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:44,219 - INFO - 选择第一个overlap场景作为起点: scene_id=1603
2025-07-29 10:41:44,219 - INFO - 添加起点场景: scene_id=1603, 时长=1.52秒, 累计时长=1.52秒
2025-07-29 10:41:44,219 - INFO - 起点场景时长不足，需要延伸填充 1.73秒
2025-07-29 10:41:44,220 - INFO - 起点场景在原始列表中的索引: 1602
2025-07-29 10:41:44,220 - INFO - 延伸添加场景: scene_id=1604 (裁剪至 1.73秒)
2025-07-29 10:41:44,220 - INFO - 累计时长: 3.25秒
2025-07-29 10:41:44,220 - INFO - 字幕序号 #1244 场景匹配完成，共选择 2 个场景，总时长: 3.25秒
2025-07-29 10:41:44,220 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:44,220 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:44,220 - INFO - 
--- 生成方案 #2：基于字幕序号 #1250 ---
2025-07-29 10:41:44,220 - INFO - 开始为单个字幕序号 #1250 匹配场景，目标时长: 3.25秒
2025-07-29 10:41:44,220 - INFO - 开始查找字幕序号 [1250] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:44,220 - INFO - 找到related_overlap场景: scene_id=1606, 字幕#1250
2025-07-29 10:41:44,222 - INFO - 字幕 #1250 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:44,222 - INFO - 字幕序号 #1250 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:44,222 - INFO - 选择第一个overlap场景作为起点: scene_id=1606
2025-07-29 10:41:44,222 - INFO - 添加起点场景: scene_id=1606, 时长=1.32秒, 累计时长=1.32秒
2025-07-29 10:41:44,222 - INFO - 起点场景时长不足，需要延伸填充 1.93秒
2025-07-29 10:41:44,222 - INFO - 起点场景在原始列表中的索引: 1605
2025-07-29 10:41:44,222 - INFO - 延伸添加场景: scene_id=1607 (裁剪至 1.93秒)
2025-07-29 10:41:44,222 - INFO - 累计时长: 3.25秒
2025-07-29 10:41:44,222 - INFO - 字幕序号 #1250 场景匹配完成，共选择 2 个场景，总时长: 3.25秒
2025-07-29 10:41:44,222 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:44,222 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:44,222 - INFO - ========== 当前模式：为字幕 #62 生成 1 套场景方案 ==========
2025-07-29 10:41:44,222 - INFO - 开始查找字幕序号 [1244, 1250] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:44,223 - INFO - 找到related_overlap场景: scene_id=1603, 字幕#1244
2025-07-29 10:41:44,223 - INFO - 找到related_overlap场景: scene_id=1606, 字幕#1250
2025-07-29 10:41:44,224 - INFO - 字幕 #1244 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:44,224 - INFO - 字幕 #1250 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:44,225 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:44,225 - INFO - 开始生成方案 #1
2025-07-29 10:41:44,225 - INFO - 方案 #1: 为字幕#1244选择初始化overlap场景id=1603
2025-07-29 10:41:44,225 - INFO - 方案 #1: 为字幕#1250选择初始化overlap场景id=1606
2025-07-29 10:41:44,225 - INFO - 方案 #1: 初始选择后，当前总时长=2.84秒
2025-07-29 10:41:44,225 - INFO - 方案 #1: 额外between选择后，当前总时长=2.84秒
2025-07-29 10:41:44,225 - INFO - 方案 #1: 场景总时长(2.84秒)小于音频时长(3.25秒)，需要延伸填充
2025-07-29 10:41:44,225 - INFO - 方案 #1: 最后一个场景ID: 1606
2025-07-29 10:41:44,225 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 1605
2025-07-29 10:41:44,225 - INFO - 方案 #1: 需要填充时长: 0.41秒
2025-07-29 10:41:44,225 - INFO - 方案 #1: 追加场景 scene_id=1607 (裁剪至 0.41秒)
2025-07-29 10:41:44,225 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 10:41:44,225 - INFO - 方案 #1 调整/填充后最终总时长: 3.25秒
2025-07-29 10:41:44,225 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:44,225 - INFO - ========== 当前模式：字幕 #62 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:44,225 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:44,225 - INFO - ========== 新模式：字幕 #62 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:44,225 - INFO - 
----- 处理字幕 #62 的方案 #1 -----
2025-07-29 10:41:44,225 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-29 10:41:44,226 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8p62q8u1
2025-07-29 10:41:44,227 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1603.mp4 (确认存在: True)
2025-07-29 10:41:44,227 - INFO - 添加场景ID=1603，时长=1.52秒，累计时长=1.52秒
2025-07-29 10:41:44,227 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1604.mp4 (确认存在: True)
2025-07-29 10:41:44,227 - INFO - 添加场景ID=1604，时长=4.56秒，累计时长=6.08秒
2025-07-29 10:41:44,227 - INFO - 场景总时长(6.08秒)已达到音频时长(3.25秒)的1.5倍，停止添加场景
2025-07-29 10:41:44,228 - INFO - 准备合并 2 个场景文件，总时长约 6.08秒
2025-07-29 10:41:44,228 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1603.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1604.mp4'

2025-07-29 10:41:44,228 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8p62q8u1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8p62q8u1\temp_combined.mp4
2025-07-29 10:41:44,394 - INFO - 合并后的视频时长: 6.13秒，目标音频时长: 3.25秒
2025-07-29 10:41:44,394 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8p62q8u1\temp_combined.mp4 -ss 0 -to 3.25 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-29 10:41:44,719 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:44,719 - INFO - 目标音频时长: 3.25秒
2025-07-29 10:41:44,719 - INFO - 实际视频时长: 3.30秒
2025-07-29 10:41:44,719 - INFO - 时长差异: 0.05秒 (1.63%)
2025-07-29 10:41:44,719 - INFO - ==========================================
2025-07-29 10:41:44,719 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:44,719 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-29 10:41:44,721 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8p62q8u1
2025-07-29 10:41:44,775 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:44,775 - INFO -   - 音频时长: 3.25秒
2025-07-29 10:41:44,775 - INFO -   - 视频时长: 3.30秒
2025-07-29 10:41:44,775 - INFO -   - 时长差异: 0.05秒 (1.63%)
2025-07-29 10:41:44,775 - INFO - 
----- 处理字幕 #62 的方案 #2 -----
2025-07-29 10:41:44,775 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-29 10:41:44,776 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpipndzsvk
2025-07-29 10:41:44,776 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1606.mp4 (确认存在: True)
2025-07-29 10:41:44,776 - INFO - 添加场景ID=1606，时长=1.32秒，累计时长=1.32秒
2025-07-29 10:41:44,776 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1607.mp4 (确认存在: True)
2025-07-29 10:41:44,776 - INFO - 添加场景ID=1607，时长=2.72秒，累计时长=4.04秒
2025-07-29 10:41:44,776 - INFO - 准备合并 2 个场景文件，总时长约 4.04秒
2025-07-29 10:41:44,777 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1606.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1607.mp4'

2025-07-29 10:41:44,777 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpipndzsvk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpipndzsvk\temp_combined.mp4
2025-07-29 10:41:44,921 - INFO - 合并后的视频时长: 4.09秒，目标音频时长: 3.25秒
2025-07-29 10:41:44,921 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpipndzsvk\temp_combined.mp4 -ss 0 -to 3.25 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-29 10:41:45,199 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:45,199 - INFO - 目标音频时长: 3.25秒
2025-07-29 10:41:45,200 - INFO - 实际视频时长: 3.30秒
2025-07-29 10:41:45,200 - INFO - 时长差异: 0.05秒 (1.63%)
2025-07-29 10:41:45,200 - INFO - ==========================================
2025-07-29 10:41:45,200 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:45,200 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-29 10:41:45,201 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpipndzsvk
2025-07-29 10:41:45,251 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:45,251 - INFO -   - 音频时长: 3.25秒
2025-07-29 10:41:45,251 - INFO -   - 视频时长: 3.30秒
2025-07-29 10:41:45,251 - INFO -   - 时长差异: 0.05秒 (1.63%)
2025-07-29 10:41:45,251 - INFO - 
----- 处理字幕 #62 的方案 #3 -----
2025-07-29 10:41:45,251 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\62_3.mp4
2025-07-29 10:41:45,252 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvi5k976u
2025-07-29 10:41:45,252 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1603.mp4 (确认存在: True)
2025-07-29 10:41:45,252 - INFO - 添加场景ID=1603，时长=1.52秒，累计时长=1.52秒
2025-07-29 10:41:45,253 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1606.mp4 (确认存在: True)
2025-07-29 10:41:45,253 - INFO - 添加场景ID=1606，时长=1.32秒，累计时长=2.84秒
2025-07-29 10:41:45,253 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1607.mp4 (确认存在: True)
2025-07-29 10:41:45,253 - INFO - 添加场景ID=1607，时长=2.72秒，累计时长=5.56秒
2025-07-29 10:41:45,253 - INFO - 场景总时长(5.56秒)已达到音频时长(3.25秒)的1.5倍，停止添加场景
2025-07-29 10:41:45,253 - INFO - 准备合并 3 个场景文件，总时长约 5.56秒
2025-07-29 10:41:45,253 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1603.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1606.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1607.mp4'

2025-07-29 10:41:45,253 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpvi5k976u\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpvi5k976u\temp_combined.mp4
2025-07-29 10:41:45,447 - INFO - 合并后的视频时长: 5.63秒，目标音频时长: 3.25秒
2025-07-29 10:41:45,448 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpvi5k976u\temp_combined.mp4 -ss 0 -to 3.25 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\62_3.mp4
2025-07-29 10:41:45,760 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:45,760 - INFO - 目标音频时长: 3.25秒
2025-07-29 10:41:45,761 - INFO - 实际视频时长: 3.30秒
2025-07-29 10:41:45,761 - INFO - 时长差异: 0.05秒 (1.63%)
2025-07-29 10:41:45,761 - INFO - ==========================================
2025-07-29 10:41:45,761 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:45,761 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\62_3.mp4
2025-07-29 10:41:45,762 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvi5k976u
2025-07-29 10:41:45,822 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:45,822 - INFO -   - 音频时长: 3.25秒
2025-07-29 10:41:45,822 - INFO -   - 视频时长: 3.30秒
2025-07-29 10:41:45,822 - INFO -   - 时长差异: 0.05秒 (1.63%)
2025-07-29 10:41:45,822 - INFO - 
字幕 #62 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:45,822 - INFO - 生成的视频文件:
2025-07-29 10:41:45,822 - INFO -   1. F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-07-29 10:41:45,822 - INFO -   2. F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-07-29 10:41:45,822 - INFO -   3. F:/github/aicut_auto/newcut_ai\62_3.mp4
2025-07-29 10:41:45,822 - INFO - ========== 字幕 #62 处理结束 ==========

