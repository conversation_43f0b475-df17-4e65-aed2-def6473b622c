2025-07-29 10:41:57,209 - INFO - ========== 字幕 #70 处理开始 ==========
2025-07-29 10:41:57,209 - INFO - 字幕内容: 她宁愿自己顶罪，却被哥哥怒斥不懂事。
2025-07-29 10:41:57,209 - INFO - 字幕序号: [1318, 1323]
2025-07-29 10:41:57,209 - INFO - 音频文件详情:
2025-07-29 10:41:57,210 - INFO -   - 路径: output\70.wav
2025-07-29 10:41:57,210 - INFO -   - 时长: 3.79秒
2025-07-29 10:41:57,210 - INFO -   - 验证音频时长: 3.79秒
2025-07-29 10:41:57,210 - INFO - 字幕时间戳信息:
2025-07-29 10:41:57,210 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:57,210 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:57,211 - INFO -   - 根据生成的音频时长(3.79秒)已调整字幕时间戳
2025-07-29 10:41:57,211 - INFO - ========== 新模式：为字幕 #70 生成4套场景方案 ==========
2025-07-29 10:41:57,211 - INFO - 字幕序号列表: [1318, 1323]
2025-07-29 10:41:57,211 - INFO - 
--- 生成方案 #1：基于字幕序号 #1318 ---
2025-07-29 10:41:57,211 - INFO - 开始为单个字幕序号 #1318 匹配场景，目标时长: 3.79秒
2025-07-29 10:41:57,211 - INFO - 开始查找字幕序号 [1318] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:57,212 - INFO - 找到related_overlap场景: scene_id=1666, 字幕#1318
2025-07-29 10:41:57,214 - INFO - 字幕 #1318 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:57,214 - INFO - 字幕序号 #1318 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:57,214 - INFO - 选择第一个overlap场景作为起点: scene_id=1666
2025-07-29 10:41:57,214 - INFO - 添加起点场景: scene_id=1666, 时长=4.00秒, 累计时长=4.00秒
2025-07-29 10:41:57,214 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:57,214 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:41:57,214 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:41:57,214 - INFO - 
--- 生成方案 #2：基于字幕序号 #1323 ---
2025-07-29 10:41:57,214 - INFO - 开始为单个字幕序号 #1323 匹配场景，目标时长: 3.79秒
2025-07-29 10:41:57,214 - INFO - 开始查找字幕序号 [1323] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:57,215 - INFO - 找到related_overlap场景: scene_id=1668, 字幕#1323
2025-07-29 10:41:57,217 - INFO - 字幕 #1323 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:57,217 - INFO - 字幕序号 #1323 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:57,217 - INFO - 选择第一个overlap场景作为起点: scene_id=1668
2025-07-29 10:41:57,217 - INFO - 添加起点场景: scene_id=1668, 时长=3.36秒, 累计时长=3.36秒
2025-07-29 10:41:57,217 - INFO - 起点场景时长不足，需要延伸填充 0.43秒
2025-07-29 10:41:57,217 - INFO - 起点场景在原始列表中的索引: 1667
2025-07-29 10:41:57,217 - INFO - 延伸添加场景: scene_id=1669 (裁剪至 0.43秒)
2025-07-29 10:41:57,217 - INFO - 累计时长: 3.79秒
2025-07-29 10:41:57,217 - INFO - 字幕序号 #1323 场景匹配完成，共选择 2 个场景，总时长: 3.79秒
2025-07-29 10:41:57,217 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:57,217 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:57,217 - INFO - ========== 当前模式：为字幕 #70 生成 1 套场景方案 ==========
2025-07-29 10:41:57,217 - INFO - 开始查找字幕序号 [1318, 1323] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:57,218 - INFO - 找到related_overlap场景: scene_id=1666, 字幕#1318
2025-07-29 10:41:57,218 - INFO - 找到related_overlap场景: scene_id=1668, 字幕#1323
2025-07-29 10:41:57,219 - INFO - 字幕 #1318 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:57,219 - INFO - 字幕 #1323 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:57,219 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:57,219 - INFO - 开始生成方案 #1
2025-07-29 10:41:57,219 - INFO - 方案 #1: 为字幕#1318选择初始化overlap场景id=1666
2025-07-29 10:41:57,219 - INFO - 方案 #1: 为字幕#1323选择初始化overlap场景id=1668
2025-07-29 10:41:57,220 - INFO - 方案 #1: 初始选择后，当前总时长=7.36秒
2025-07-29 10:41:57,220 - INFO - 方案 #1: 额外between选择后，当前总时长=7.36秒
2025-07-29 10:41:57,220 - INFO - 方案 #1: 场景总时长(7.36秒)大于音频时长(3.79秒)，需要裁剪
2025-07-29 10:41:57,220 - INFO - 调整前总时长: 7.36秒, 目标时长: 3.79秒
2025-07-29 10:41:57,220 - INFO - 需要裁剪 3.57秒
2025-07-29 10:41:57,220 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:57,220 - INFO - 裁剪场景ID=1666：从4.00秒裁剪至1.20秒
2025-07-29 10:41:57,220 - INFO - 裁剪场景ID=1668：从3.36秒裁剪至2.59秒
2025-07-29 10:41:57,220 - INFO - 调整后总时长: 3.79秒，与目标时长差异: 0.00秒
2025-07-29 10:41:57,220 - INFO - 方案 #1 调整/填充后最终总时长: 3.79秒
2025-07-29 10:41:57,220 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:57,220 - INFO - ========== 当前模式：字幕 #70 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:57,220 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:57,220 - INFO - ========== 新模式：字幕 #70 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:57,220 - INFO - 
----- 处理字幕 #70 的方案 #1 -----
2025-07-29 10:41:57,220 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-07-29 10:41:57,221 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpketn5m4d
2025-07-29 10:41:57,221 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1666.mp4 (确认存在: True)
2025-07-29 10:41:57,221 - INFO - 添加场景ID=1666，时长=4.00秒，累计时长=4.00秒
2025-07-29 10:41:57,221 - INFO - 准备合并 1 个场景文件，总时长约 4.00秒
2025-07-29 10:41:57,221 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1666.mp4'

2025-07-29 10:41:57,221 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpketn5m4d\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpketn5m4d\temp_combined.mp4
2025-07-29 10:41:57,366 - INFO - 合并后的视频时长: 4.02秒，目标音频时长: 3.79秒
2025-07-29 10:41:57,367 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpketn5m4d\temp_combined.mp4 -ss 0 -to 3.785 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-07-29 10:41:57,650 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:57,650 - INFO - 目标音频时长: 3.79秒
2025-07-29 10:41:57,650 - INFO - 实际视频时长: 3.82秒
2025-07-29 10:41:57,650 - INFO - 时长差异: 0.04秒 (1.00%)
2025-07-29 10:41:57,650 - INFO - ==========================================
2025-07-29 10:41:57,650 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:57,650 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-07-29 10:41:57,651 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpketn5m4d
2025-07-29 10:41:57,701 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:57,701 - INFO -   - 音频时长: 3.79秒
2025-07-29 10:41:57,701 - INFO -   - 视频时长: 3.82秒
2025-07-29 10:41:57,701 - INFO -   - 时长差异: 0.04秒 (1.00%)
2025-07-29 10:41:57,701 - INFO - 
----- 处理字幕 #70 的方案 #2 -----
2025-07-29 10:41:57,701 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-07-29 10:41:57,702 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt38lhz8z
2025-07-29 10:41:57,702 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1668.mp4 (确认存在: True)
2025-07-29 10:41:57,702 - INFO - 添加场景ID=1668，时长=3.36秒，累计时长=3.36秒
2025-07-29 10:41:57,702 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1669.mp4 (确认存在: True)
2025-07-29 10:41:57,702 - INFO - 添加场景ID=1669，时长=2.12秒，累计时长=5.48秒
2025-07-29 10:41:57,703 - INFO - 准备合并 2 个场景文件，总时长约 5.48秒
2025-07-29 10:41:57,703 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1668.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1669.mp4'

2025-07-29 10:41:57,703 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpt38lhz8z\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpt38lhz8z\temp_combined.mp4
2025-07-29 10:41:57,837 - INFO - 合并后的视频时长: 5.53秒，目标音频时长: 3.79秒
2025-07-29 10:41:57,837 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpt38lhz8z\temp_combined.mp4 -ss 0 -to 3.785 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-07-29 10:41:58,161 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:58,162 - INFO - 目标音频时长: 3.79秒
2025-07-29 10:41:58,162 - INFO - 实际视频时长: 3.82秒
2025-07-29 10:41:58,162 - INFO - 时长差异: 0.04秒 (1.00%)
2025-07-29 10:41:58,162 - INFO - ==========================================
2025-07-29 10:41:58,162 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:58,162 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-07-29 10:41:58,163 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt38lhz8z
2025-07-29 10:41:58,227 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:58,227 - INFO -   - 音频时长: 3.79秒
2025-07-29 10:41:58,227 - INFO -   - 视频时长: 3.82秒
2025-07-29 10:41:58,227 - INFO -   - 时长差异: 0.04秒 (1.00%)
2025-07-29 10:41:58,227 - INFO - 
----- 处理字幕 #70 的方案 #3 -----
2025-07-29 10:41:58,227 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\70_3.mp4
2025-07-29 10:41:58,228 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptzyldyrq
2025-07-29 10:41:58,228 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1666.mp4 (确认存在: True)
2025-07-29 10:41:58,229 - INFO - 添加场景ID=1666，时长=4.00秒，累计时长=4.00秒
2025-07-29 10:41:58,229 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1668.mp4 (确认存在: True)
2025-07-29 10:41:58,229 - INFO - 添加场景ID=1668，时长=3.36秒，累计时长=7.36秒
2025-07-29 10:41:58,229 - INFO - 场景总时长(7.36秒)已达到音频时长(3.79秒)的1.5倍，停止添加场景
2025-07-29 10:41:58,229 - INFO - 准备合并 2 个场景文件，总时长约 7.36秒
2025-07-29 10:41:58,229 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1666.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1668.mp4'

2025-07-29 10:41:58,229 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmptzyldyrq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmptzyldyrq\temp_combined.mp4
2025-07-29 10:41:58,384 - INFO - 合并后的视频时长: 7.41秒，目标音频时长: 3.79秒
2025-07-29 10:41:58,384 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmptzyldyrq\temp_combined.mp4 -ss 0 -to 3.785 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\70_3.mp4
2025-07-29 10:41:58,668 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:58,668 - INFO - 目标音频时长: 3.79秒
2025-07-29 10:41:58,668 - INFO - 实际视频时长: 3.82秒
2025-07-29 10:41:58,668 - INFO - 时长差异: 0.04秒 (1.00%)
2025-07-29 10:41:58,668 - INFO - ==========================================
2025-07-29 10:41:58,668 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:58,668 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\70_3.mp4
2025-07-29 10:41:58,669 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptzyldyrq
2025-07-29 10:41:58,727 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:58,727 - INFO -   - 音频时长: 3.79秒
2025-07-29 10:41:58,727 - INFO -   - 视频时长: 3.82秒
2025-07-29 10:41:58,727 - INFO -   - 时长差异: 0.04秒 (1.00%)
2025-07-29 10:41:58,727 - INFO - 
字幕 #70 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:58,727 - INFO - 生成的视频文件:
2025-07-29 10:41:58,727 - INFO -   1. F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-07-29 10:41:58,727 - INFO -   2. F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-07-29 10:41:58,727 - INFO -   3. F:/github/aicut_auto/newcut_ai\70_3.mp4
2025-07-29 10:41:58,727 - INFO - ========== 字幕 #70 处理结束 ==========

