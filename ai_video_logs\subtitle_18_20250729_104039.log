2025-07-29 10:40:39,089 - INFO - ========== 字幕 #18 处理开始 ==========
2025-07-29 10:40:39,089 - INFO - 字幕内容: 她误将身穿哥哥盔甲的摄政王认错，深情嘱托。
2025-07-29 10:40:39,089 - INFO - 字幕序号: [86, 90]
2025-07-29 10:40:39,089 - INFO - 音频文件详情:
2025-07-29 10:40:39,089 - INFO -   - 路径: output\18.wav
2025-07-29 10:40:39,089 - INFO -   - 时长: 3.21秒
2025-07-29 10:40:39,089 - INFO -   - 验证音频时长: 3.21秒
2025-07-29 10:40:39,089 - INFO - 字幕时间戳信息:
2025-07-29 10:40:39,089 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:39,089 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:39,089 - INFO -   - 根据生成的音频时长(3.21秒)已调整字幕时间戳
2025-07-29 10:40:39,089 - INFO - ========== 新模式：为字幕 #18 生成4套场景方案 ==========
2025-07-29 10:40:39,089 - INFO - 字幕序号列表: [86, 90]
2025-07-29 10:40:39,089 - INFO - 
--- 生成方案 #1：基于字幕序号 #86 ---
2025-07-29 10:40:39,089 - INFO - 开始为单个字幕序号 #86 匹配场景，目标时长: 3.21秒
2025-07-29 10:40:39,089 - INFO - 开始查找字幕序号 [86] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:39,089 - INFO - 找到related_overlap场景: scene_id=118, 字幕#86
2025-07-29 10:40:39,090 - INFO - 找到related_between场景: scene_id=119, 字幕#86
2025-07-29 10:40:39,090 - INFO - 找到related_between场景: scene_id=120, 字幕#86
2025-07-29 10:40:39,090 - INFO - 找到related_between场景: scene_id=121, 字幕#86
2025-07-29 10:40:39,090 - INFO - 找到related_between场景: scene_id=122, 字幕#86
2025-07-29 10:40:39,090 - INFO - 找到related_between场景: scene_id=123, 字幕#86
2025-07-29 10:40:39,090 - INFO - 找到related_between场景: scene_id=124, 字幕#86
2025-07-29 10:40:39,090 - INFO - 找到related_between场景: scene_id=125, 字幕#86
2025-07-29 10:40:39,090 - INFO - 找到related_between场景: scene_id=126, 字幕#86
2025-07-29 10:40:39,090 - INFO - 找到related_between场景: scene_id=127, 字幕#86
2025-07-29 10:40:39,091 - INFO - 字幕 #86 找到 1 个overlap场景, 9 个between场景
2025-07-29 10:40:39,091 - INFO - 字幕序号 #86 找到 1 个可用overlap场景, 9 个可用between场景
2025-07-29 10:40:39,091 - INFO - 选择第一个overlap场景作为起点: scene_id=118
2025-07-29 10:40:39,091 - INFO - 添加起点场景: scene_id=118, 时长=2.00秒, 累计时长=2.00秒
2025-07-29 10:40:39,091 - INFO - 起点场景时长不足，需要延伸填充 1.21秒
2025-07-29 10:40:39,091 - INFO - 起点场景在原始列表中的索引: 117
2025-07-29 10:40:39,091 - INFO - 延伸添加场景: scene_id=119 (完整时长 1.16秒)
2025-07-29 10:40:39,091 - INFO - 累计时长: 3.16秒
2025-07-29 10:40:39,091 - INFO - 延伸添加场景: scene_id=120 (裁剪至 0.05秒)
2025-07-29 10:40:39,091 - INFO - 累计时长: 3.21秒
2025-07-29 10:40:39,091 - INFO - 字幕序号 #86 场景匹配完成，共选择 3 个场景，总时长: 3.21秒
2025-07-29 10:40:39,091 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:40:39,091 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:40:39,092 - INFO - 
--- 生成方案 #2：基于字幕序号 #90 ---
2025-07-29 10:40:39,092 - INFO - 开始为单个字幕序号 #90 匹配场景，目标时长: 3.21秒
2025-07-29 10:40:39,092 - INFO - 开始查找字幕序号 [90] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:39,092 - INFO - 找到related_overlap场景: scene_id=131, 字幕#90
2025-07-29 10:40:39,092 - INFO - 找到related_between场景: scene_id=132, 字幕#90
2025-07-29 10:40:39,092 - INFO - 找到related_between场景: scene_id=133, 字幕#90
2025-07-29 10:40:39,092 - INFO - 找到related_between场景: scene_id=134, 字幕#90
2025-07-29 10:40:39,092 - INFO - 找到related_between场景: scene_id=135, 字幕#90
2025-07-29 10:40:39,092 - INFO - 找到related_between场景: scene_id=136, 字幕#90
2025-07-29 10:40:39,093 - INFO - 找到related_between场景: scene_id=137, 字幕#90
2025-07-29 10:40:39,093 - INFO - 字幕 #90 找到 1 个overlap场景, 6 个between场景
2025-07-29 10:40:39,093 - INFO - 字幕序号 #90 找到 1 个可用overlap场景, 6 个可用between场景
2025-07-29 10:40:39,093 - INFO - 选择第一个overlap场景作为起点: scene_id=131
2025-07-29 10:40:39,093 - INFO - 添加起点场景: scene_id=131, 时长=1.80秒, 累计时长=1.80秒
2025-07-29 10:40:39,093 - INFO - 起点场景时长不足，需要延伸填充 1.41秒
2025-07-29 10:40:39,093 - INFO - 起点场景在原始列表中的索引: 130
2025-07-29 10:40:39,093 - INFO - 延伸添加场景: scene_id=132 (裁剪至 1.41秒)
2025-07-29 10:40:39,093 - INFO - 累计时长: 3.21秒
2025-07-29 10:40:39,093 - INFO - 字幕序号 #90 场景匹配完成，共选择 2 个场景，总时长: 3.21秒
2025-07-29 10:40:39,093 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:39,093 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:39,093 - INFO - ========== 当前模式：为字幕 #18 生成 1 套场景方案 ==========
2025-07-29 10:40:39,093 - INFO - 开始查找字幕序号 [86, 90] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:39,093 - INFO - 找到related_overlap场景: scene_id=118, 字幕#86
2025-07-29 10:40:39,093 - INFO - 找到related_overlap场景: scene_id=131, 字幕#90
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=119, 字幕#86
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=120, 字幕#86
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=121, 字幕#86
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=122, 字幕#86
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=123, 字幕#86
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=124, 字幕#86
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=125, 字幕#86
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=126, 字幕#86
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=127, 字幕#86
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=132, 字幕#90
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=133, 字幕#90
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=134, 字幕#90
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=135, 字幕#90
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=136, 字幕#90
2025-07-29 10:40:39,094 - INFO - 找到related_between场景: scene_id=137, 字幕#90
2025-07-29 10:40:39,095 - INFO - 字幕 #86 找到 1 个overlap场景, 9 个between场景
2025-07-29 10:40:39,095 - INFO - 字幕 #90 找到 1 个overlap场景, 6 个between场景
2025-07-29 10:40:39,095 - INFO - 共收集 2 个未使用的overlap场景和 15 个未使用的between场景
2025-07-29 10:40:39,095 - INFO - 开始生成方案 #1
2025-07-29 10:40:39,095 - INFO - 方案 #1: 为字幕#86选择初始化overlap场景id=118
2025-07-29 10:40:39,095 - INFO - 方案 #1: 为字幕#90选择初始化overlap场景id=131
2025-07-29 10:40:39,095 - INFO - 方案 #1: 初始选择后，当前总时长=3.80秒
2025-07-29 10:40:39,095 - INFO - 方案 #1: 额外between选择后，当前总时长=3.80秒
2025-07-29 10:40:39,095 - INFO - 方案 #1: 场景总时长(3.80秒)大于音频时长(3.21秒)，需要裁剪
2025-07-29 10:40:39,095 - INFO - 调整前总时长: 3.80秒, 目标时长: 3.21秒
2025-07-29 10:40:39,095 - INFO - 需要裁剪 0.59秒
2025-07-29 10:40:39,095 - INFO - 裁剪最长场景ID=118：从2.00秒裁剪至1.41秒
2025-07-29 10:40:39,095 - INFO - 调整后总时长: 3.21秒，与目标时长差异: 0.00秒
2025-07-29 10:40:39,095 - INFO - 方案 #1 调整/填充后最终总时长: 3.21秒
2025-07-29 10:40:39,095 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:39,095 - INFO - ========== 当前模式：字幕 #18 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:39,095 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:39,095 - INFO - ========== 新模式：字幕 #18 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:39,095 - INFO - 
----- 处理字幕 #18 的方案 #1 -----
2025-07-29 10:40:39,095 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-29 10:40:39,096 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkb37c3wh
2025-07-29 10:40:39,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\118.mp4 (确认存在: True)
2025-07-29 10:40:39,096 - INFO - 添加场景ID=118，时长=2.00秒，累计时长=2.00秒
2025-07-29 10:40:39,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\119.mp4 (确认存在: True)
2025-07-29 10:40:39,096 - INFO - 添加场景ID=119，时长=1.16秒，累计时长=3.16秒
2025-07-29 10:40:39,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\120.mp4 (确认存在: True)
2025-07-29 10:40:39,096 - INFO - 添加场景ID=120，时长=2.20秒，累计时长=5.36秒
2025-07-29 10:40:39,096 - INFO - 场景总时长(5.36秒)已达到音频时长(3.21秒)的1.5倍，停止添加场景
2025-07-29 10:40:39,097 - INFO - 准备合并 3 个场景文件，总时长约 5.36秒
2025-07-29 10:40:39,097 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/118.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/119.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/120.mp4'

2025-07-29 10:40:39,097 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkb37c3wh\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkb37c3wh\temp_combined.mp4
2025-07-29 10:40:39,247 - INFO - 合并后的视频时长: 5.43秒，目标音频时长: 3.21秒
2025-07-29 10:40:39,247 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkb37c3wh\temp_combined.mp4 -ss 0 -to 3.213 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-29 10:40:39,517 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:39,517 - INFO - 目标音频时长: 3.21秒
2025-07-29 10:40:39,517 - INFO - 实际视频时长: 3.26秒
2025-07-29 10:40:39,517 - INFO - 时长差异: 0.05秒 (1.56%)
2025-07-29 10:40:39,517 - INFO - ==========================================
2025-07-29 10:40:39,517 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:39,517 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-29 10:40:39,517 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkb37c3wh
2025-07-29 10:40:39,557 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:39,557 - INFO -   - 音频时长: 3.21秒
2025-07-29 10:40:39,559 - INFO -   - 视频时长: 3.26秒
2025-07-29 10:40:39,559 - INFO -   - 时长差异: 0.05秒 (1.56%)
2025-07-29 10:40:39,559 - INFO - 
----- 处理字幕 #18 的方案 #2 -----
2025-07-29 10:40:39,559 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-29 10:40:39,559 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq915hwo7
2025-07-29 10:40:39,559 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\131.mp4 (确认存在: True)
2025-07-29 10:40:39,559 - INFO - 添加场景ID=131，时长=1.80秒，累计时长=1.80秒
2025-07-29 10:40:39,559 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\132.mp4 (确认存在: True)
2025-07-29 10:40:39,559 - INFO - 添加场景ID=132，时长=1.44秒，累计时长=3.24秒
2025-07-29 10:40:39,560 - INFO - 准备合并 2 个场景文件，总时长约 3.24秒
2025-07-29 10:40:39,560 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/131.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/132.mp4'

2025-07-29 10:40:39,560 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpq915hwo7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpq915hwo7\temp_combined.mp4
2025-07-29 10:40:39,666 - INFO - 合并后的视频时长: 3.29秒，目标音频时长: 3.21秒
2025-07-29 10:40:39,666 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpq915hwo7\temp_combined.mp4 -ss 0 -to 3.213 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-29 10:40:39,889 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:39,889 - INFO - 目标音频时长: 3.21秒
2025-07-29 10:40:39,889 - INFO - 实际视频时长: 3.26秒
2025-07-29 10:40:39,889 - INFO - 时长差异: 0.05秒 (1.56%)
2025-07-29 10:40:39,889 - INFO - ==========================================
2025-07-29 10:40:39,889 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:39,889 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-29 10:40:39,889 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq915hwo7
2025-07-29 10:40:39,933 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:39,933 - INFO -   - 音频时长: 3.21秒
2025-07-29 10:40:39,933 - INFO -   - 视频时长: 3.26秒
2025-07-29 10:40:39,933 - INFO -   - 时长差异: 0.05秒 (1.56%)
2025-07-29 10:40:39,933 - INFO - 
----- 处理字幕 #18 的方案 #3 -----
2025-07-29 10:40:39,933 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-29 10:40:39,933 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw36wdgxm
2025-07-29 10:40:39,934 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\118.mp4 (确认存在: True)
2025-07-29 10:40:39,934 - INFO - 添加场景ID=118，时长=2.00秒，累计时长=2.00秒
2025-07-29 10:40:39,934 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\131.mp4 (确认存在: True)
2025-07-29 10:40:39,934 - INFO - 添加场景ID=131，时长=1.80秒，累计时长=3.80秒
2025-07-29 10:40:39,934 - INFO - 准备合并 2 个场景文件，总时长约 3.80秒
2025-07-29 10:40:39,934 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/118.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/131.mp4'

2025-07-29 10:40:39,934 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpw36wdgxm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpw36wdgxm\temp_combined.mp4
2025-07-29 10:40:40,042 - INFO - 合并后的视频时长: 3.85秒，目标音频时长: 3.21秒
2025-07-29 10:40:40,042 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpw36wdgxm\temp_combined.mp4 -ss 0 -to 3.213 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-29 10:40:40,293 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:40,293 - INFO - 目标音频时长: 3.21秒
2025-07-29 10:40:40,293 - INFO - 实际视频时长: 3.26秒
2025-07-29 10:40:40,293 - INFO - 时长差异: 0.05秒 (1.56%)
2025-07-29 10:40:40,293 - INFO - ==========================================
2025-07-29 10:40:40,293 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:40,293 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-29 10:40:40,294 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw36wdgxm
2025-07-29 10:40:40,336 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:40,336 - INFO -   - 音频时长: 3.21秒
2025-07-29 10:40:40,336 - INFO -   - 视频时长: 3.26秒
2025-07-29 10:40:40,336 - INFO -   - 时长差异: 0.05秒 (1.56%)
2025-07-29 10:40:40,336 - INFO - 
字幕 #18 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:40,336 - INFO - 生成的视频文件:
2025-07-29 10:40:40,336 - INFO -   1. F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-29 10:40:40,336 - INFO -   2. F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-29 10:40:40,336 - INFO -   3. F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-29 10:40:40,336 - INFO - ========== 字幕 #18 处理结束 ==========

