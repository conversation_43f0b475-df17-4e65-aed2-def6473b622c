2025-07-29 10:40:51,584 - INFO - ========== 字幕 #28 处理开始 ==========
2025-07-29 10:40:51,584 - INFO - 字幕内容: 哥哥因不顾妹妹被祖母罚跪，祖母认为他该罚。
2025-07-29 10:40:51,584 - INFO - 字幕序号: [153, 159]
2025-07-29 10:40:51,584 - INFO - 音频文件详情:
2025-07-29 10:40:51,584 - INFO -   - 路径: output\28.wav
2025-07-29 10:40:51,584 - INFO -   - 时长: 3.51秒
2025-07-29 10:40:51,585 - INFO -   - 验证音频时长: 3.51秒
2025-07-29 10:40:51,585 - INFO - 字幕时间戳信息:
2025-07-29 10:40:51,585 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:51,585 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:51,585 - INFO -   - 根据生成的音频时长(3.51秒)已调整字幕时间戳
2025-07-29 10:40:51,585 - INFO - ========== 新模式：为字幕 #28 生成4套场景方案 ==========
2025-07-29 10:40:51,585 - INFO - 字幕序号列表: [153, 159]
2025-07-29 10:40:51,585 - INFO - 
--- 生成方案 #1：基于字幕序号 #153 ---
2025-07-29 10:40:51,585 - INFO - 开始为单个字幕序号 #153 匹配场景，目标时长: 3.51秒
2025-07-29 10:40:51,585 - INFO - 开始查找字幕序号 [153] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:51,585 - INFO - 找到related_overlap场景: scene_id=230, 字幕#153
2025-07-29 10:40:51,585 - INFO - 找到related_overlap场景: scene_id=231, 字幕#153
2025-07-29 10:40:51,586 - INFO - 找到related_between场景: scene_id=229, 字幕#153
2025-07-29 10:40:51,586 - INFO - 字幕 #153 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:51,586 - INFO - 字幕序号 #153 找到 2 个可用overlap场景, 1 个可用between场景
2025-07-29 10:40:51,586 - INFO - 选择第一个overlap场景作为起点: scene_id=230
2025-07-29 10:40:51,586 - INFO - 添加起点场景: scene_id=230, 时长=1.24秒, 累计时长=1.24秒
2025-07-29 10:40:51,586 - INFO - 起点场景时长不足，需要延伸填充 2.27秒
2025-07-29 10:40:51,586 - INFO - 起点场景在原始列表中的索引: 229
2025-07-29 10:40:51,586 - INFO - 延伸添加场景: scene_id=231 (完整时长 1.12秒)
2025-07-29 10:40:51,586 - INFO - 累计时长: 2.36秒
2025-07-29 10:40:51,586 - INFO - 延伸添加场景: scene_id=232 (裁剪至 1.15秒)
2025-07-29 10:40:51,586 - INFO - 累计时长: 3.51秒
2025-07-29 10:40:51,586 - INFO - 字幕序号 #153 场景匹配完成，共选择 3 个场景，总时长: 3.51秒
2025-07-29 10:40:51,586 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:40:51,586 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:40:51,586 - INFO - 
--- 生成方案 #2：基于字幕序号 #159 ---
2025-07-29 10:40:51,586 - INFO - 开始为单个字幕序号 #159 匹配场景，目标时长: 3.51秒
2025-07-29 10:40:51,586 - INFO - 开始查找字幕序号 [159] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:51,587 - INFO - 找到related_overlap场景: scene_id=235, 字幕#159
2025-07-29 10:40:51,587 - INFO - 找到related_overlap场景: scene_id=236, 字幕#159
2025-07-29 10:40:51,587 - INFO - 找到related_between场景: scene_id=237, 字幕#159
2025-07-29 10:40:51,587 - INFO - 字幕 #159 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:51,587 - INFO - 字幕序号 #159 找到 2 个可用overlap场景, 1 个可用between场景
2025-07-29 10:40:51,587 - INFO - 选择第一个overlap场景作为起点: scene_id=235
2025-07-29 10:40:51,587 - INFO - 添加起点场景: scene_id=235, 时长=1.20秒, 累计时长=1.20秒
2025-07-29 10:40:51,587 - INFO - 起点场景时长不足，需要延伸填充 2.31秒
2025-07-29 10:40:51,587 - INFO - 起点场景在原始列表中的索引: 234
2025-07-29 10:40:51,587 - INFO - 延伸添加场景: scene_id=236 (完整时长 1.84秒)
2025-07-29 10:40:51,587 - INFO - 累计时长: 3.04秒
2025-07-29 10:40:51,587 - INFO - 延伸添加场景: scene_id=237 (裁剪至 0.47秒)
2025-07-29 10:40:51,587 - INFO - 累计时长: 3.51秒
2025-07-29 10:40:51,587 - INFO - 字幕序号 #159 场景匹配完成，共选择 3 个场景，总时长: 3.51秒
2025-07-29 10:40:51,587 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:51,587 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:51,587 - INFO - ========== 当前模式：为字幕 #28 生成 1 套场景方案 ==========
2025-07-29 10:40:51,587 - INFO - 开始查找字幕序号 [153, 159] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:51,587 - INFO - 找到related_overlap场景: scene_id=230, 字幕#153
2025-07-29 10:40:51,587 - INFO - 找到related_overlap场景: scene_id=231, 字幕#153
2025-07-29 10:40:51,587 - INFO - 找到related_overlap场景: scene_id=235, 字幕#159
2025-07-29 10:40:51,587 - INFO - 找到related_overlap场景: scene_id=236, 字幕#159
2025-07-29 10:40:51,588 - INFO - 找到related_between场景: scene_id=229, 字幕#153
2025-07-29 10:40:51,588 - INFO - 找到related_between场景: scene_id=237, 字幕#159
2025-07-29 10:40:51,589 - INFO - 字幕 #153 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:51,589 - INFO - 字幕 #159 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:51,589 - INFO - 共收集 4 个未使用的overlap场景和 2 个未使用的between场景
2025-07-29 10:40:51,589 - INFO - 开始生成方案 #1
2025-07-29 10:40:51,589 - INFO - 方案 #1: 为字幕#153选择初始化overlap场景id=231
2025-07-29 10:40:51,589 - INFO - 方案 #1: 为字幕#159选择初始化overlap场景id=235
2025-07-29 10:40:51,589 - INFO - 方案 #1: 初始选择后，当前总时长=2.32秒
2025-07-29 10:40:51,589 - INFO - 方案 #1: 额外添加overlap场景id=230, 当前总时长=3.56秒
2025-07-29 10:40:51,589 - INFO - 方案 #1: 额外between选择后，当前总时长=3.56秒
2025-07-29 10:40:51,589 - INFO - 方案 #1: 场景总时长(3.56秒)大于音频时长(3.51秒)，需要裁剪
2025-07-29 10:40:51,589 - INFO - 调整前总时长: 3.56秒, 目标时长: 3.51秒
2025-07-29 10:40:51,589 - INFO - 需要裁剪 0.05秒
2025-07-29 10:40:51,589 - INFO - 裁剪最长场景ID=230：从1.24秒裁剪至1.19秒
2025-07-29 10:40:51,589 - INFO - 调整后总时长: 3.51秒，与目标时长差异: 0.00秒
2025-07-29 10:40:51,589 - INFO - 方案 #1 调整/填充后最终总时长: 3.51秒
2025-07-29 10:40:51,589 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:51,589 - INFO - ========== 当前模式：字幕 #28 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:51,589 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:51,589 - INFO - ========== 新模式：字幕 #28 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:51,589 - INFO - 
----- 处理字幕 #28 的方案 #1 -----
2025-07-29 10:40:51,589 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-29 10:40:51,590 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3squzji9
2025-07-29 10:40:51,590 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\230.mp4 (确认存在: True)
2025-07-29 10:40:51,590 - INFO - 添加场景ID=230，时长=1.24秒，累计时长=1.24秒
2025-07-29 10:40:51,590 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\231.mp4 (确认存在: True)
2025-07-29 10:40:51,591 - INFO - 添加场景ID=231，时长=1.12秒，累计时长=2.36秒
2025-07-29 10:40:51,591 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\232.mp4 (确认存在: True)
2025-07-29 10:40:51,591 - INFO - 添加场景ID=232，时长=4.12秒，累计时长=6.48秒
2025-07-29 10:40:51,591 - INFO - 场景总时长(6.48秒)已达到音频时长(3.51秒)的1.5倍，停止添加场景
2025-07-29 10:40:51,591 - INFO - 准备合并 3 个场景文件，总时长约 6.48秒
2025-07-29 10:40:51,591 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/230.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/231.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/232.mp4'

2025-07-29 10:40:51,591 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3squzji9\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3squzji9\temp_combined.mp4
2025-07-29 10:40:51,746 - INFO - 合并后的视频时长: 6.55秒，目标音频时长: 3.51秒
2025-07-29 10:40:51,746 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3squzji9\temp_combined.mp4 -ss 0 -to 3.507 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-29 10:40:52,017 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:52,017 - INFO - 目标音频时长: 3.51秒
2025-07-29 10:40:52,017 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:40:52,017 - INFO - 时长差异: 0.04秒 (1.03%)
2025-07-29 10:40:52,017 - INFO - ==========================================
2025-07-29 10:40:52,017 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:52,017 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-29 10:40:52,018 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3squzji9
2025-07-29 10:40:52,062 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:52,062 - INFO -   - 音频时长: 3.51秒
2025-07-29 10:40:52,062 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:40:52,062 - INFO -   - 时长差异: 0.04秒 (1.03%)
2025-07-29 10:40:52,062 - INFO - 
----- 处理字幕 #28 的方案 #2 -----
2025-07-29 10:40:52,062 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-29 10:40:52,062 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp828aem23
2025-07-29 10:40:52,063 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\235.mp4 (确认存在: True)
2025-07-29 10:40:52,063 - INFO - 添加场景ID=235，时长=1.20秒，累计时长=1.20秒
2025-07-29 10:40:52,063 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\236.mp4 (确认存在: True)
2025-07-29 10:40:52,063 - INFO - 添加场景ID=236，时长=1.84秒，累计时长=3.04秒
2025-07-29 10:40:52,063 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\237.mp4 (确认存在: True)
2025-07-29 10:40:52,063 - INFO - 添加场景ID=237，时长=1.64秒，累计时长=4.68秒
2025-07-29 10:40:52,063 - INFO - 准备合并 3 个场景文件，总时长约 4.68秒
2025-07-29 10:40:52,063 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/235.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/236.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/237.mp4'

2025-07-29 10:40:52,063 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp828aem23\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp828aem23\temp_combined.mp4
2025-07-29 10:40:52,210 - INFO - 合并后的视频时长: 4.75秒，目标音频时长: 3.51秒
2025-07-29 10:40:52,210 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp828aem23\temp_combined.mp4 -ss 0 -to 3.507 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-29 10:40:52,482 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:52,482 - INFO - 目标音频时长: 3.51秒
2025-07-29 10:40:52,482 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:40:52,482 - INFO - 时长差异: 0.04秒 (1.03%)
2025-07-29 10:40:52,482 - INFO - ==========================================
2025-07-29 10:40:52,482 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:52,482 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-29 10:40:52,483 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp828aem23
2025-07-29 10:40:52,526 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:52,526 - INFO -   - 音频时长: 3.51秒
2025-07-29 10:40:52,526 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:40:52,526 - INFO -   - 时长差异: 0.04秒 (1.03%)
2025-07-29 10:40:52,526 - INFO - 
----- 处理字幕 #28 的方案 #3 -----
2025-07-29 10:40:52,527 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-29 10:40:52,527 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpphbwgfyy
2025-07-29 10:40:52,527 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\231.mp4 (确认存在: True)
2025-07-29 10:40:52,527 - INFO - 添加场景ID=231，时长=1.12秒，累计时长=1.12秒
2025-07-29 10:40:52,527 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\235.mp4 (确认存在: True)
2025-07-29 10:40:52,527 - INFO - 添加场景ID=235，时长=1.20秒，累计时长=2.32秒
2025-07-29 10:40:52,527 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\230.mp4 (确认存在: True)
2025-07-29 10:40:52,528 - INFO - 添加场景ID=230，时长=1.24秒，累计时长=3.56秒
2025-07-29 10:40:52,528 - INFO - 准备合并 3 个场景文件，总时长约 3.56秒
2025-07-29 10:40:52,528 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/231.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/235.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/230.mp4'

2025-07-29 10:40:52,528 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpphbwgfyy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpphbwgfyy\temp_combined.mp4
2025-07-29 10:40:52,676 - INFO - 合并后的视频时长: 3.63秒，目标音频时长: 3.51秒
2025-07-29 10:40:52,676 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpphbwgfyy\temp_combined.mp4 -ss 0 -to 3.507 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-29 10:40:52,951 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:52,951 - INFO - 目标音频时长: 3.51秒
2025-07-29 10:40:52,951 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:40:52,951 - INFO - 时长差异: 0.04秒 (1.03%)
2025-07-29 10:40:52,951 - INFO - ==========================================
2025-07-29 10:40:52,951 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:52,951 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-29 10:40:52,952 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpphbwgfyy
2025-07-29 10:40:52,994 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:52,994 - INFO -   - 音频时长: 3.51秒
2025-07-29 10:40:52,994 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:40:52,994 - INFO -   - 时长差异: 0.04秒 (1.03%)
2025-07-29 10:40:52,994 - INFO - 
字幕 #28 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:52,994 - INFO - 生成的视频文件:
2025-07-29 10:40:52,994 - INFO -   1. F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-29 10:40:52,994 - INFO -   2. F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-29 10:40:52,994 - INFO -   3. F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-29 10:40:52,994 - INFO - ========== 字幕 #28 处理结束 ==========

