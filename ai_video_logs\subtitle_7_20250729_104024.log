2025-07-29 10:40:24,289 - INFO - ========== 字幕 #7 处理开始 ==========
2025-07-29 10:40:24,289 - INFO - 字幕内容: 祖母心疼她想为她议亲，她却只想留在祖母身边。
2025-07-29 10:40:24,289 - INFO - 字幕序号: [25, 29]
2025-07-29 10:40:24,289 - INFO - 音频文件详情:
2025-07-29 10:40:24,289 - INFO -   - 路径: output\7.wav
2025-07-29 10:40:24,289 - INFO -   - 时长: 3.64秒
2025-07-29 10:40:24,289 - INFO -   - 验证音频时长: 3.64秒
2025-07-29 10:40:24,289 - INFO - 字幕时间戳信息:
2025-07-29 10:40:24,289 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:24,299 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:24,299 - INFO -   - 根据生成的音频时长(3.64秒)已调整字幕时间戳
2025-07-29 10:40:24,299 - INFO - ========== 新模式：为字幕 #7 生成4套场景方案 ==========
2025-07-29 10:40:24,299 - INFO - 字幕序号列表: [25, 29]
2025-07-29 10:40:24,299 - INFO - 
--- 生成方案 #1：基于字幕序号 #25 ---
2025-07-29 10:40:24,299 - INFO - 开始为单个字幕序号 #25 匹配场景，目标时长: 3.64秒
2025-07-29 10:40:24,299 - INFO - 开始查找字幕序号 [25] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:24,299 - INFO - 找到related_overlap场景: scene_id=39, 字幕#25
2025-07-29 10:40:24,300 - INFO - 找到related_between场景: scene_id=38, 字幕#25
2025-07-29 10:40:24,301 - INFO - 字幕 #25 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:40:24,301 - INFO - 字幕序号 #25 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 10:40:24,301 - INFO - 选择第一个overlap场景作为起点: scene_id=39
2025-07-29 10:40:24,301 - INFO - 添加起点场景: scene_id=39, 时长=3.12秒, 累计时长=3.12秒
2025-07-29 10:40:24,301 - INFO - 起点场景时长不足，需要延伸填充 0.52秒
2025-07-29 10:40:24,301 - INFO - 起点场景在原始列表中的索引: 38
2025-07-29 10:40:24,301 - INFO - 延伸添加场景: scene_id=40 (裁剪至 0.52秒)
2025-07-29 10:40:24,301 - INFO - 累计时长: 3.64秒
2025-07-29 10:40:24,301 - INFO - 字幕序号 #25 场景匹配完成，共选择 2 个场景，总时长: 3.64秒
2025-07-29 10:40:24,301 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:24,301 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:24,301 - INFO - 
--- 生成方案 #2：基于字幕序号 #29 ---
2025-07-29 10:40:24,301 - INFO - 开始为单个字幕序号 #29 匹配场景，目标时长: 3.64秒
2025-07-29 10:40:24,301 - INFO - 开始查找字幕序号 [29] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:24,301 - INFO - 找到related_overlap场景: scene_id=42, 字幕#29
2025-07-29 10:40:24,301 - INFO - 找到related_overlap场景: scene_id=43, 字幕#29
2025-07-29 10:40:24,301 - INFO - 找到related_between场景: scene_id=44, 字幕#29
2025-07-29 10:40:24,301 - INFO - 找到related_between场景: scene_id=45, 字幕#29
2025-07-29 10:40:24,301 - INFO - 找到related_between场景: scene_id=46, 字幕#29
2025-07-29 10:40:24,302 - INFO - 字幕 #29 找到 2 个overlap场景, 3 个between场景
2025-07-29 10:40:24,302 - INFO - 字幕序号 #29 找到 2 个可用overlap场景, 3 个可用between场景
2025-07-29 10:40:24,302 - INFO - 选择第一个overlap场景作为起点: scene_id=42
2025-07-29 10:40:24,302 - INFO - 添加起点场景: scene_id=42, 时长=3.20秒, 累计时长=3.20秒
2025-07-29 10:40:24,302 - INFO - 起点场景时长不足，需要延伸填充 0.44秒
2025-07-29 10:40:24,302 - INFO - 起点场景在原始列表中的索引: 41
2025-07-29 10:40:24,302 - INFO - 延伸添加场景: scene_id=43 (裁剪至 0.44秒)
2025-07-29 10:40:24,302 - INFO - 累计时长: 3.64秒
2025-07-29 10:40:24,302 - INFO - 字幕序号 #29 场景匹配完成，共选择 2 个场景，总时长: 3.64秒
2025-07-29 10:40:24,302 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:24,302 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:24,302 - INFO - ========== 当前模式：为字幕 #7 生成 1 套场景方案 ==========
2025-07-29 10:40:24,302 - INFO - 开始查找字幕序号 [25, 29] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:24,302 - INFO - 找到related_overlap场景: scene_id=39, 字幕#25
2025-07-29 10:40:24,302 - INFO - 找到related_overlap场景: scene_id=42, 字幕#29
2025-07-29 10:40:24,302 - INFO - 找到related_overlap场景: scene_id=43, 字幕#29
2025-07-29 10:40:24,303 - INFO - 找到related_between场景: scene_id=38, 字幕#25
2025-07-29 10:40:24,303 - INFO - 找到related_between场景: scene_id=44, 字幕#29
2025-07-29 10:40:24,303 - INFO - 找到related_between场景: scene_id=45, 字幕#29
2025-07-29 10:40:24,303 - INFO - 找到related_between场景: scene_id=46, 字幕#29
2025-07-29 10:40:24,303 - INFO - 字幕 #25 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:40:24,303 - INFO - 字幕 #29 找到 2 个overlap场景, 3 个between场景
2025-07-29 10:40:24,303 - INFO - 共收集 3 个未使用的overlap场景和 4 个未使用的between场景
2025-07-29 10:40:24,303 - INFO - 开始生成方案 #1
2025-07-29 10:40:24,303 - INFO - 方案 #1: 为字幕#25选择初始化overlap场景id=39
2025-07-29 10:40:24,303 - INFO - 方案 #1: 为字幕#29选择初始化overlap场景id=43
2025-07-29 10:40:24,303 - INFO - 方案 #1: 初始选择后，当前总时长=4.48秒
2025-07-29 10:40:24,303 - INFO - 方案 #1: 额外between选择后，当前总时长=4.48秒
2025-07-29 10:40:24,303 - INFO - 方案 #1: 场景总时长(4.48秒)大于音频时长(3.64秒)，需要裁剪
2025-07-29 10:40:24,303 - INFO - 调整前总时长: 4.48秒, 目标时长: 3.64秒
2025-07-29 10:40:24,303 - INFO - 需要裁剪 0.84秒
2025-07-29 10:40:24,304 - INFO - 裁剪最长场景ID=39：从3.12秒裁剪至2.28秒
2025-07-29 10:40:24,304 - INFO - 调整后总时长: 3.64秒，与目标时长差异: 0.00秒
2025-07-29 10:40:24,304 - INFO - 方案 #1 调整/填充后最终总时长: 3.64秒
2025-07-29 10:40:24,304 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:24,304 - INFO - ========== 当前模式：字幕 #7 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:24,304 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:24,304 - INFO - ========== 新模式：字幕 #7 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:24,304 - INFO - 
----- 处理字幕 #7 的方案 #1 -----
2025-07-29 10:40:24,304 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-29 10:40:24,304 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6jw594cl
2025-07-29 10:40:24,305 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\39.mp4 (确认存在: True)
2025-07-29 10:40:24,305 - INFO - 添加场景ID=39，时长=3.12秒，累计时长=3.12秒
2025-07-29 10:40:24,305 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\40.mp4 (确认存在: True)
2025-07-29 10:40:24,305 - INFO - 添加场景ID=40，时长=3.08秒，累计时长=6.20秒
2025-07-29 10:40:24,305 - INFO - 场景总时长(6.20秒)已达到音频时长(3.64秒)的1.5倍，停止添加场景
2025-07-29 10:40:24,305 - INFO - 准备合并 2 个场景文件，总时长约 6.20秒
2025-07-29 10:40:24,305 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/39.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/40.mp4'

2025-07-29 10:40:24,305 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6jw594cl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6jw594cl\temp_combined.mp4
2025-07-29 10:40:24,436 - INFO - 合并后的视频时长: 6.25秒，目标音频时长: 3.64秒
2025-07-29 10:40:24,436 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6jw594cl\temp_combined.mp4 -ss 0 -to 3.636 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-29 10:40:24,716 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:24,716 - INFO - 目标音频时长: 3.64秒
2025-07-29 10:40:24,716 - INFO - 实际视频时长: 3.66秒
2025-07-29 10:40:24,716 - INFO - 时长差异: 0.03秒 (0.74%)
2025-07-29 10:40:24,716 - INFO - ==========================================
2025-07-29 10:40:24,716 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:24,716 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-29 10:40:24,717 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6jw594cl
2025-07-29 10:40:24,758 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:24,758 - INFO -   - 音频时长: 3.64秒
2025-07-29 10:40:24,758 - INFO -   - 视频时长: 3.66秒
2025-07-29 10:40:24,758 - INFO -   - 时长差异: 0.03秒 (0.74%)
2025-07-29 10:40:24,758 - INFO - 
----- 处理字幕 #7 的方案 #2 -----
2025-07-29 10:40:24,758 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-29 10:40:24,759 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl2sqx8pj
2025-07-29 10:40:24,760 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\42.mp4 (确认存在: True)
2025-07-29 10:40:24,760 - INFO - 添加场景ID=42，时长=3.20秒，累计时长=3.20秒
2025-07-29 10:40:24,760 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\43.mp4 (确认存在: True)
2025-07-29 10:40:24,760 - INFO - 添加场景ID=43，时长=1.36秒，累计时长=4.56秒
2025-07-29 10:40:24,760 - INFO - 准备合并 2 个场景文件，总时长约 4.56秒
2025-07-29 10:40:24,760 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/42.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/43.mp4'

2025-07-29 10:40:24,760 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpl2sqx8pj\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpl2sqx8pj\temp_combined.mp4
2025-07-29 10:40:24,877 - INFO - 合并后的视频时长: 4.61秒，目标音频时长: 3.64秒
2025-07-29 10:40:24,877 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpl2sqx8pj\temp_combined.mp4 -ss 0 -to 3.636 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-29 10:40:25,158 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:25,158 - INFO - 目标音频时长: 3.64秒
2025-07-29 10:40:25,158 - INFO - 实际视频时长: 3.66秒
2025-07-29 10:40:25,158 - INFO - 时长差异: 0.03秒 (0.74%)
2025-07-29 10:40:25,158 - INFO - ==========================================
2025-07-29 10:40:25,158 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:25,158 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-29 10:40:25,158 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl2sqx8pj
2025-07-29 10:40:25,200 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:25,200 - INFO -   - 音频时长: 3.64秒
2025-07-29 10:40:25,200 - INFO -   - 视频时长: 3.66秒
2025-07-29 10:40:25,200 - INFO -   - 时长差异: 0.03秒 (0.74%)
2025-07-29 10:40:25,200 - INFO - 
----- 处理字幕 #7 的方案 #3 -----
2025-07-29 10:40:25,201 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\7_3.mp4
2025-07-29 10:40:25,201 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppen0hiiy
2025-07-29 10:40:25,201 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\39.mp4 (确认存在: True)
2025-07-29 10:40:25,202 - INFO - 添加场景ID=39，时长=3.12秒，累计时长=3.12秒
2025-07-29 10:40:25,202 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\43.mp4 (确认存在: True)
2025-07-29 10:40:25,202 - INFO - 添加场景ID=43，时长=1.36秒，累计时长=4.48秒
2025-07-29 10:40:25,202 - INFO - 准备合并 2 个场景文件，总时长约 4.48秒
2025-07-29 10:40:25,202 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/39.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/43.mp4'

2025-07-29 10:40:25,202 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppen0hiiy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppen0hiiy\temp_combined.mp4
2025-07-29 10:40:25,312 - INFO - 合并后的视频时长: 4.53秒，目标音频时长: 3.64秒
2025-07-29 10:40:25,312 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppen0hiiy\temp_combined.mp4 -ss 0 -to 3.636 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\7_3.mp4
2025-07-29 10:40:25,590 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:25,590 - INFO - 目标音频时长: 3.64秒
2025-07-29 10:40:25,590 - INFO - 实际视频时长: 3.66秒
2025-07-29 10:40:25,590 - INFO - 时长差异: 0.03秒 (0.74%)
2025-07-29 10:40:25,590 - INFO - ==========================================
2025-07-29 10:40:25,590 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:25,590 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\7_3.mp4
2025-07-29 10:40:25,591 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppen0hiiy
2025-07-29 10:40:25,633 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:25,633 - INFO -   - 音频时长: 3.64秒
2025-07-29 10:40:25,633 - INFO -   - 视频时长: 3.66秒
2025-07-29 10:40:25,633 - INFO -   - 时长差异: 0.03秒 (0.74%)
2025-07-29 10:40:25,633 - INFO - 
字幕 #7 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:25,633 - INFO - 生成的视频文件:
2025-07-29 10:40:25,633 - INFO -   1. F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-29 10:40:25,633 - INFO -   2. F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-29 10:40:25,633 - INFO -   3. F:/github/aicut_auto/newcut_ai\7_3.mp4
2025-07-29 10:40:25,633 - INFO - ========== 字幕 #7 处理结束 ==========

