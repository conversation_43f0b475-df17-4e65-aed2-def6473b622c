2025-07-29 10:40:33,640 - INFO - ========== 字幕 #14 处理开始 ==========
2025-07-29 10:40:33,641 - INFO - 字幕内容: 宴会上，冷酷的摄政王现身，传闻他曾削掉投怀送抱之人的手臂。
2025-07-29 10:40:33,641 - INFO - 字幕序号: [61, 66]
2025-07-29 10:40:33,641 - INFO - 音频文件详情:
2025-07-29 10:40:33,641 - INFO -   - 路径: output\14.wav
2025-07-29 10:40:33,641 - INFO -   - 时长: 3.97秒
2025-07-29 10:40:33,641 - INFO -   - 验证音频时长: 3.97秒
2025-07-29 10:40:33,641 - INFO - 字幕时间戳信息:
2025-07-29 10:40:33,641 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:33,641 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:33,642 - INFO -   - 根据生成的音频时长(3.97秒)已调整字幕时间戳
2025-07-29 10:40:33,642 - INFO - ========== 新模式：为字幕 #14 生成4套场景方案 ==========
2025-07-29 10:40:33,642 - INFO - 字幕序号列表: [61, 66]
2025-07-29 10:40:33,642 - INFO - 
--- 生成方案 #1：基于字幕序号 #61 ---
2025-07-29 10:40:33,642 - INFO - 开始为单个字幕序号 #61 匹配场景，目标时长: 3.97秒
2025-07-29 10:40:33,642 - INFO - 开始查找字幕序号 [61] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:33,642 - INFO - 找到related_overlap场景: scene_id=84, 字幕#61
2025-07-29 10:40:33,642 - INFO - 找到related_between场景: scene_id=74, 字幕#61
2025-07-29 10:40:33,642 - INFO - 找到related_between场景: scene_id=75, 字幕#61
2025-07-29 10:40:33,642 - INFO - 找到related_between场景: scene_id=76, 字幕#61
2025-07-29 10:40:33,642 - INFO - 找到related_between场景: scene_id=77, 字幕#61
2025-07-29 10:40:33,642 - INFO - 找到related_between场景: scene_id=78, 字幕#61
2025-07-29 10:40:33,642 - INFO - 找到related_between场景: scene_id=79, 字幕#61
2025-07-29 10:40:33,642 - INFO - 找到related_between场景: scene_id=80, 字幕#61
2025-07-29 10:40:33,642 - INFO - 找到related_between场景: scene_id=81, 字幕#61
2025-07-29 10:40:33,642 - INFO - 找到related_between场景: scene_id=82, 字幕#61
2025-07-29 10:40:33,643 - INFO - 找到related_between场景: scene_id=83, 字幕#61
2025-07-29 10:40:33,643 - INFO - 字幕 #61 找到 1 个overlap场景, 10 个between场景
2025-07-29 10:40:33,643 - INFO - 字幕序号 #61 找到 1 个可用overlap场景, 10 个可用between场景
2025-07-29 10:40:33,643 - INFO - 选择第一个overlap场景作为起点: scene_id=84
2025-07-29 10:40:33,643 - INFO - 添加起点场景: scene_id=84, 时长=4.00秒, 累计时长=4.00秒
2025-07-29 10:40:33,643 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:40:33,643 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:40:33,643 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:40:33,643 - INFO - 
--- 生成方案 #2：基于字幕序号 #66 ---
2025-07-29 10:40:33,643 - INFO - 开始为单个字幕序号 #66 匹配场景，目标时长: 3.97秒
2025-07-29 10:40:33,643 - INFO - 开始查找字幕序号 [66] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:33,644 - INFO - 找到related_overlap场景: scene_id=87, 字幕#66
2025-07-29 10:40:33,644 - INFO - 找到related_overlap场景: scene_id=88, 字幕#66
2025-07-29 10:40:33,645 - INFO - 字幕 #66 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:33,645 - INFO - 字幕序号 #66 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:33,645 - INFO - 选择第一个overlap场景作为起点: scene_id=87
2025-07-29 10:40:33,645 - INFO - 添加起点场景: scene_id=87, 时长=2.32秒, 累计时长=2.32秒
2025-07-29 10:40:33,645 - INFO - 起点场景时长不足，需要延伸填充 1.65秒
2025-07-29 10:40:33,645 - INFO - 起点场景在原始列表中的索引: 86
2025-07-29 10:40:33,645 - INFO - 延伸添加场景: scene_id=88 (完整时长 1.64秒)
2025-07-29 10:40:33,645 - INFO - 累计时长: 3.96秒
2025-07-29 10:40:33,645 - INFO - 延伸添加场景: scene_id=89 (裁剪至 0.01秒)
2025-07-29 10:40:33,645 - INFO - 累计时长: 3.97秒
2025-07-29 10:40:33,645 - INFO - 字幕序号 #66 场景匹配完成，共选择 3 个场景，总时长: 3.97秒
2025-07-29 10:40:33,645 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:33,645 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:33,645 - INFO - ========== 当前模式：为字幕 #14 生成 1 套场景方案 ==========
2025-07-29 10:40:33,645 - INFO - 开始查找字幕序号 [61, 66] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:33,645 - INFO - 找到related_overlap场景: scene_id=84, 字幕#61
2025-07-29 10:40:33,645 - INFO - 找到related_overlap场景: scene_id=87, 字幕#66
2025-07-29 10:40:33,645 - INFO - 找到related_overlap场景: scene_id=88, 字幕#66
2025-07-29 10:40:33,646 - INFO - 找到related_between场景: scene_id=74, 字幕#61
2025-07-29 10:40:33,646 - INFO - 找到related_between场景: scene_id=75, 字幕#61
2025-07-29 10:40:33,646 - INFO - 找到related_between场景: scene_id=76, 字幕#61
2025-07-29 10:40:33,646 - INFO - 找到related_between场景: scene_id=77, 字幕#61
2025-07-29 10:40:33,646 - INFO - 找到related_between场景: scene_id=78, 字幕#61
2025-07-29 10:40:33,646 - INFO - 找到related_between场景: scene_id=79, 字幕#61
2025-07-29 10:40:33,646 - INFO - 找到related_between场景: scene_id=80, 字幕#61
2025-07-29 10:40:33,646 - INFO - 找到related_between场景: scene_id=81, 字幕#61
2025-07-29 10:40:33,646 - INFO - 找到related_between场景: scene_id=82, 字幕#61
2025-07-29 10:40:33,646 - INFO - 找到related_between场景: scene_id=83, 字幕#61
2025-07-29 10:40:33,646 - INFO - 字幕 #61 找到 1 个overlap场景, 10 个between场景
2025-07-29 10:40:33,646 - INFO - 字幕 #66 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:33,646 - INFO - 共收集 3 个未使用的overlap场景和 10 个未使用的between场景
2025-07-29 10:40:33,646 - INFO - 开始生成方案 #1
2025-07-29 10:40:33,646 - INFO - 方案 #1: 为字幕#61选择初始化overlap场景id=84
2025-07-29 10:40:33,646 - INFO - 方案 #1: 为字幕#66选择初始化overlap场景id=87
2025-07-29 10:40:33,646 - INFO - 方案 #1: 初始选择后，当前总时长=6.32秒
2025-07-29 10:40:33,646 - INFO - 方案 #1: 额外between选择后，当前总时长=6.32秒
2025-07-29 10:40:33,646 - INFO - 方案 #1: 场景总时长(6.32秒)大于音频时长(3.97秒)，需要裁剪
2025-07-29 10:40:33,646 - INFO - 调整前总时长: 6.32秒, 目标时长: 3.97秒
2025-07-29 10:40:33,646 - INFO - 需要裁剪 2.35秒
2025-07-29 10:40:33,646 - INFO - 裁剪最长场景ID=84：从4.00秒裁剪至1.65秒
2025-07-29 10:40:33,646 - INFO - 调整后总时长: 3.97秒，与目标时长差异: 0.00秒
2025-07-29 10:40:33,646 - INFO - 方案 #1 调整/填充后最终总时长: 3.97秒
2025-07-29 10:40:33,646 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:33,646 - INFO - ========== 当前模式：字幕 #14 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:33,646 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:33,646 - INFO - ========== 新模式：字幕 #14 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:33,646 - INFO - 
----- 处理字幕 #14 的方案 #1 -----
2025-07-29 10:40:33,647 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-29 10:40:33,647 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpa2s2tg76
2025-07-29 10:40:33,647 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\84.mp4 (确认存在: True)
2025-07-29 10:40:33,647 - INFO - 添加场景ID=84，时长=4.00秒，累计时长=4.00秒
2025-07-29 10:40:33,648 - INFO - 准备合并 1 个场景文件，总时长约 4.00秒
2025-07-29 10:40:33,648 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/84.mp4'

2025-07-29 10:40:33,648 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpa2s2tg76\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpa2s2tg76\temp_combined.mp4
2025-07-29 10:40:33,760 - INFO - 合并后的视频时长: 4.02秒，目标音频时长: 3.97秒
2025-07-29 10:40:33,760 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpa2s2tg76\temp_combined.mp4 -ss 0 -to 3.973 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-29 10:40:34,061 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:34,062 - INFO - 目标音频时长: 3.97秒
2025-07-29 10:40:34,062 - INFO - 实际视频时长: 4.02秒
2025-07-29 10:40:34,062 - INFO - 时长差异: 0.05秒 (1.26%)
2025-07-29 10:40:34,062 - INFO - ==========================================
2025-07-29 10:40:34,062 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:34,062 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-29 10:40:34,062 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpa2s2tg76
2025-07-29 10:40:34,104 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:34,104 - INFO -   - 音频时长: 3.97秒
2025-07-29 10:40:34,104 - INFO -   - 视频时长: 4.02秒
2025-07-29 10:40:34,104 - INFO -   - 时长差异: 0.05秒 (1.26%)
2025-07-29 10:40:34,104 - INFO - 
----- 处理字幕 #14 的方案 #2 -----
2025-07-29 10:40:34,104 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-29 10:40:34,104 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjqxgq5jl
2025-07-29 10:40:34,105 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\87.mp4 (确认存在: True)
2025-07-29 10:40:34,105 - INFO - 添加场景ID=87，时长=2.32秒，累计时长=2.32秒
2025-07-29 10:40:34,105 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\88.mp4 (确认存在: True)
2025-07-29 10:40:34,105 - INFO - 添加场景ID=88，时长=1.64秒，累计时长=3.96秒
2025-07-29 10:40:34,105 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\89.mp4 (确认存在: True)
2025-07-29 10:40:34,105 - INFO - 添加场景ID=89，时长=0.96秒，累计时长=4.92秒
2025-07-29 10:40:34,105 - INFO - 准备合并 3 个场景文件，总时长约 4.92秒
2025-07-29 10:40:34,105 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/87.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/88.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/89.mp4'

2025-07-29 10:40:34,105 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjqxgq5jl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjqxgq5jl\temp_combined.mp4
2025-07-29 10:40:34,233 - INFO - 合并后的视频时长: 4.99秒，目标音频时长: 3.97秒
2025-07-29 10:40:34,233 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjqxgq5jl\temp_combined.mp4 -ss 0 -to 3.973 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-29 10:40:34,537 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:34,537 - INFO - 目标音频时长: 3.97秒
2025-07-29 10:40:34,537 - INFO - 实际视频时长: 4.02秒
2025-07-29 10:40:34,537 - INFO - 时长差异: 0.05秒 (1.26%)
2025-07-29 10:40:34,537 - INFO - ==========================================
2025-07-29 10:40:34,537 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:34,537 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-29 10:40:34,538 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjqxgq5jl
2025-07-29 10:40:34,585 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:34,585 - INFO -   - 音频时长: 3.97秒
2025-07-29 10:40:34,585 - INFO -   - 视频时长: 4.02秒
2025-07-29 10:40:34,585 - INFO -   - 时长差异: 0.05秒 (1.26%)
2025-07-29 10:40:34,585 - INFO - 
----- 处理字幕 #14 的方案 #3 -----
2025-07-29 10:40:34,585 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-29 10:40:34,586 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr44bmaho
2025-07-29 10:40:34,586 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\84.mp4 (确认存在: True)
2025-07-29 10:40:34,586 - INFO - 添加场景ID=84，时长=4.00秒，累计时长=4.00秒
2025-07-29 10:40:34,586 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\87.mp4 (确认存在: True)
2025-07-29 10:40:34,586 - INFO - 添加场景ID=87，时长=2.32秒，累计时长=6.32秒
2025-07-29 10:40:34,586 - INFO - 场景总时长(6.32秒)已达到音频时长(3.97秒)的1.5倍，停止添加场景
2025-07-29 10:40:34,586 - INFO - 准备合并 2 个场景文件，总时长约 6.32秒
2025-07-29 10:40:34,586 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/84.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/87.mp4'

2025-07-29 10:40:34,587 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpr44bmaho\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpr44bmaho\temp_combined.mp4
2025-07-29 10:40:34,709 - INFO - 合并后的视频时长: 6.37秒，目标音频时长: 3.97秒
2025-07-29 10:40:34,709 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpr44bmaho\temp_combined.mp4 -ss 0 -to 3.973 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-29 10:40:34,983 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:34,983 - INFO - 目标音频时长: 3.97秒
2025-07-29 10:40:34,983 - INFO - 实际视频时长: 4.02秒
2025-07-29 10:40:34,983 - INFO - 时长差异: 0.05秒 (1.26%)
2025-07-29 10:40:34,983 - INFO - ==========================================
2025-07-29 10:40:34,983 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:34,983 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-29 10:40:34,984 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr44bmaho
2025-07-29 10:40:35,024 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:35,024 - INFO -   - 音频时长: 3.97秒
2025-07-29 10:40:35,024 - INFO -   - 视频时长: 4.02秒
2025-07-29 10:40:35,024 - INFO -   - 时长差异: 0.05秒 (1.26%)
2025-07-29 10:40:35,025 - INFO - 
字幕 #14 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:35,025 - INFO - 生成的视频文件:
2025-07-29 10:40:35,025 - INFO -   1. F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-29 10:40:35,025 - INFO -   2. F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-29 10:40:35,025 - INFO -   3. F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-29 10:40:35,025 - INFO - ========== 字幕 #14 处理结束 ==========

