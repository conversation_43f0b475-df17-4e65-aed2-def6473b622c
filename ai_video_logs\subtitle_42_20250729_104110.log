2025-07-29 10:41:10,277 - INFO - ========== 字幕 #42 处理开始 ==========
2025-07-29 10:41:10,277 - INFO - 字幕内容: 刘青书仗着皇亲身份，嚣张威胁要娶走她们兄妹。
2025-07-29 10:41:10,277 - INFO - 字幕序号: [240, 249]
2025-07-29 10:41:10,277 - INFO - 音频文件详情:
2025-07-29 10:41:10,277 - INFO -   - 路径: output\42.wav
2025-07-29 10:41:10,277 - INFO -   - 时长: 3.98秒
2025-07-29 10:41:10,277 - INFO -   - 验证音频时长: 3.98秒
2025-07-29 10:41:10,277 - INFO - 字幕时间戳信息:
2025-07-29 10:41:10,277 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:10,277 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:10,278 - INFO -   - 根据生成的音频时长(3.98秒)已调整字幕时间戳
2025-07-29 10:41:10,278 - INFO - ========== 新模式：为字幕 #42 生成4套场景方案 ==========
2025-07-29 10:41:10,278 - INFO - 字幕序号列表: [240, 249]
2025-07-29 10:41:10,278 - INFO - 
--- 生成方案 #1：基于字幕序号 #240 ---
2025-07-29 10:41:10,278 - INFO - 开始为单个字幕序号 #240 匹配场景，目标时长: 3.98秒
2025-07-29 10:41:10,278 - INFO - 开始查找字幕序号 [240] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:10,278 - INFO - 找到related_overlap场景: scene_id=359, 字幕#240
2025-07-29 10:41:10,279 - INFO - 字幕 #240 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:10,279 - INFO - 字幕序号 #240 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:10,279 - INFO - 选择第一个overlap场景作为起点: scene_id=359
2025-07-29 10:41:10,279 - INFO - 添加起点场景: scene_id=359, 时长=2.84秒, 累计时长=2.84秒
2025-07-29 10:41:10,279 - INFO - 起点场景时长不足，需要延伸填充 1.14秒
2025-07-29 10:41:10,279 - INFO - 起点场景在原始列表中的索引: 358
2025-07-29 10:41:10,279 - INFO - 延伸添加场景: scene_id=360 (裁剪至 1.14秒)
2025-07-29 10:41:10,279 - INFO - 累计时长: 3.98秒
2025-07-29 10:41:10,280 - INFO - 字幕序号 #240 场景匹配完成，共选择 2 个场景，总时长: 3.98秒
2025-07-29 10:41:10,280 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:10,280 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:10,280 - INFO - 
--- 生成方案 #2：基于字幕序号 #249 ---
2025-07-29 10:41:10,280 - INFO - 开始为单个字幕序号 #249 匹配场景，目标时长: 3.98秒
2025-07-29 10:41:10,280 - INFO - 开始查找字幕序号 [249] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:10,280 - INFO - 找到related_overlap场景: scene_id=369, 字幕#249
2025-07-29 10:41:10,281 - INFO - 字幕 #249 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:10,281 - INFO - 字幕序号 #249 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:10,281 - INFO - 选择第一个overlap场景作为起点: scene_id=369
2025-07-29 10:41:10,281 - INFO - 添加起点场景: scene_id=369, 时长=2.24秒, 累计时长=2.24秒
2025-07-29 10:41:10,281 - INFO - 起点场景时长不足，需要延伸填充 1.74秒
2025-07-29 10:41:10,281 - INFO - 起点场景在原始列表中的索引: 368
2025-07-29 10:41:10,281 - INFO - 延伸添加场景: scene_id=370 (完整时长 1.60秒)
2025-07-29 10:41:10,281 - INFO - 累计时长: 3.84秒
2025-07-29 10:41:10,281 - INFO - 延伸添加场景: scene_id=371 (裁剪至 0.14秒)
2025-07-29 10:41:10,281 - INFO - 累计时长: 3.98秒
2025-07-29 10:41:10,281 - INFO - 字幕序号 #249 场景匹配完成，共选择 3 个场景，总时长: 3.98秒
2025-07-29 10:41:10,281 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:41:10,281 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:10,281 - INFO - ========== 当前模式：为字幕 #42 生成 1 套场景方案 ==========
2025-07-29 10:41:10,281 - INFO - 开始查找字幕序号 [240, 249] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:10,281 - INFO - 找到related_overlap场景: scene_id=359, 字幕#240
2025-07-29 10:41:10,281 - INFO - 找到related_overlap场景: scene_id=369, 字幕#249
2025-07-29 10:41:10,282 - INFO - 字幕 #240 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:10,282 - INFO - 字幕 #249 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:10,282 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:10,282 - INFO - 开始生成方案 #1
2025-07-29 10:41:10,282 - INFO - 方案 #1: 为字幕#240选择初始化overlap场景id=359
2025-07-29 10:41:10,282 - INFO - 方案 #1: 为字幕#249选择初始化overlap场景id=369
2025-07-29 10:41:10,282 - INFO - 方案 #1: 初始选择后，当前总时长=5.08秒
2025-07-29 10:41:10,282 - INFO - 方案 #1: 额外between选择后，当前总时长=5.08秒
2025-07-29 10:41:10,282 - INFO - 方案 #1: 场景总时长(5.08秒)大于音频时长(3.98秒)，需要裁剪
2025-07-29 10:41:10,282 - INFO - 调整前总时长: 5.08秒, 目标时长: 3.98秒
2025-07-29 10:41:10,282 - INFO - 需要裁剪 1.10秒
2025-07-29 10:41:10,282 - INFO - 裁剪最长场景ID=359：从2.84秒裁剪至1.74秒
2025-07-29 10:41:10,282 - INFO - 调整后总时长: 3.98秒，与目标时长差异: 0.00秒
2025-07-29 10:41:10,282 - INFO - 方案 #1 调整/填充后最终总时长: 3.98秒
2025-07-29 10:41:10,282 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:10,282 - INFO - ========== 当前模式：字幕 #42 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:10,282 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:10,282 - INFO - ========== 新模式：字幕 #42 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:10,282 - INFO - 
----- 处理字幕 #42 的方案 #1 -----
2025-07-29 10:41:10,282 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-29 10:41:10,283 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp56n1975l
2025-07-29 10:41:10,283 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\359.mp4 (确认存在: True)
2025-07-29 10:41:10,283 - INFO - 添加场景ID=359，时长=2.84秒，累计时长=2.84秒
2025-07-29 10:41:10,283 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\360.mp4 (确认存在: True)
2025-07-29 10:41:10,283 - INFO - 添加场景ID=360，时长=2.44秒，累计时长=5.28秒
2025-07-29 10:41:10,283 - INFO - 准备合并 2 个场景文件，总时长约 5.28秒
2025-07-29 10:41:10,283 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/359.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/360.mp4'

2025-07-29 10:41:10,284 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp56n1975l\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp56n1975l\temp_combined.mp4
2025-07-29 10:41:10,403 - INFO - 合并后的视频时长: 5.33秒，目标音频时长: 3.98秒
2025-07-29 10:41:10,404 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp56n1975l\temp_combined.mp4 -ss 0 -to 3.984 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-29 10:41:10,770 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:10,770 - INFO - 目标音频时长: 3.98秒
2025-07-29 10:41:10,770 - INFO - 实际视频时长: 4.02秒
2025-07-29 10:41:10,770 - INFO - 时长差异: 0.04秒 (0.98%)
2025-07-29 10:41:10,770 - INFO - ==========================================
2025-07-29 10:41:10,770 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:10,770 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-29 10:41:10,771 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp56n1975l
2025-07-29 10:41:10,818 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:10,818 - INFO -   - 音频时长: 3.98秒
2025-07-29 10:41:10,818 - INFO -   - 视频时长: 4.02秒
2025-07-29 10:41:10,818 - INFO -   - 时长差异: 0.04秒 (0.98%)
2025-07-29 10:41:10,818 - INFO - 
----- 处理字幕 #42 的方案 #2 -----
2025-07-29 10:41:10,818 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-29 10:41:10,818 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8f8y11ht
2025-07-29 10:41:10,818 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\369.mp4 (确认存在: True)
2025-07-29 10:41:10,818 - INFO - 添加场景ID=369，时长=2.24秒，累计时长=2.24秒
2025-07-29 10:41:10,818 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\370.mp4 (确认存在: True)
2025-07-29 10:41:10,818 - INFO - 添加场景ID=370，时长=1.60秒，累计时长=3.84秒
2025-07-29 10:41:10,818 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\371.mp4 (确认存在: True)
2025-07-29 10:41:10,818 - INFO - 添加场景ID=371，时长=1.44秒，累计时长=5.28秒
2025-07-29 10:41:10,818 - INFO - 准备合并 3 个场景文件，总时长约 5.28秒
2025-07-29 10:41:10,819 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/369.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/370.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/371.mp4'

2025-07-29 10:41:10,819 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8f8y11ht\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8f8y11ht\temp_combined.mp4
2025-07-29 10:41:10,946 - INFO - 合并后的视频时长: 5.35秒，目标音频时长: 3.98秒
2025-07-29 10:41:10,946 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8f8y11ht\temp_combined.mp4 -ss 0 -to 3.984 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-29 10:41:11,256 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:11,256 - INFO - 目标音频时长: 3.98秒
2025-07-29 10:41:11,256 - INFO - 实际视频时长: 4.02秒
2025-07-29 10:41:11,256 - INFO - 时长差异: 0.04秒 (0.98%)
2025-07-29 10:41:11,256 - INFO - ==========================================
2025-07-29 10:41:11,256 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:11,256 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-29 10:41:11,257 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8f8y11ht
2025-07-29 10:41:11,302 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:11,302 - INFO -   - 音频时长: 3.98秒
2025-07-29 10:41:11,302 - INFO -   - 视频时长: 4.02秒
2025-07-29 10:41:11,302 - INFO -   - 时长差异: 0.04秒 (0.98%)
2025-07-29 10:41:11,302 - INFO - 
----- 处理字幕 #42 的方案 #3 -----
2025-07-29 10:41:11,302 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\42_3.mp4
2025-07-29 10:41:11,303 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpscay_zq1
2025-07-29 10:41:11,303 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\359.mp4 (确认存在: True)
2025-07-29 10:41:11,303 - INFO - 添加场景ID=359，时长=2.84秒，累计时长=2.84秒
2025-07-29 10:41:11,303 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\369.mp4 (确认存在: True)
2025-07-29 10:41:11,303 - INFO - 添加场景ID=369，时长=2.24秒，累计时长=5.08秒
2025-07-29 10:41:11,303 - INFO - 准备合并 2 个场景文件，总时长约 5.08秒
2025-07-29 10:41:11,303 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/359.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/369.mp4'

2025-07-29 10:41:11,304 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpscay_zq1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpscay_zq1\temp_combined.mp4
2025-07-29 10:41:11,427 - INFO - 合并后的视频时长: 5.13秒，目标音频时长: 3.98秒
2025-07-29 10:41:11,427 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpscay_zq1\temp_combined.mp4 -ss 0 -to 3.984 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\42_3.mp4
2025-07-29 10:41:11,713 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:11,713 - INFO - 目标音频时长: 3.98秒
2025-07-29 10:41:11,713 - INFO - 实际视频时长: 4.02秒
2025-07-29 10:41:11,713 - INFO - 时长差异: 0.04秒 (0.98%)
2025-07-29 10:41:11,713 - INFO - ==========================================
2025-07-29 10:41:11,713 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:11,713 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\42_3.mp4
2025-07-29 10:41:11,714 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpscay_zq1
2025-07-29 10:41:11,756 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:11,756 - INFO -   - 音频时长: 3.98秒
2025-07-29 10:41:11,756 - INFO -   - 视频时长: 4.02秒
2025-07-29 10:41:11,756 - INFO -   - 时长差异: 0.04秒 (0.98%)
2025-07-29 10:41:11,756 - INFO - 
字幕 #42 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:11,756 - INFO - 生成的视频文件:
2025-07-29 10:41:11,758 - INFO -   1. F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-29 10:41:11,758 - INFO -   2. F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-29 10:41:11,758 - INFO -   3. F:/github/aicut_auto/newcut_ai\42_3.mp4
2025-07-29 10:41:11,758 - INFO - ========== 字幕 #42 处理结束 ==========

