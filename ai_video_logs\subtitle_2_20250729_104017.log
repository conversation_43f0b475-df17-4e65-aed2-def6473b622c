2025-07-29 10:40:17,565 - INFO - ========== 字幕 #2 处理开始 ==========
2025-07-29 10:40:17,565 - INFO - 字幕内容: 她不顾阻拦，才知是新纳的九姨娘在房里。
2025-07-29 10:40:17,565 - INFO - 字幕序号: [4, 7]
2025-07-29 10:40:17,565 - INFO - 音频文件详情:
2025-07-29 10:40:17,565 - INFO -   - 路径: output\2.wav
2025-07-29 10:40:17,565 - INFO -   - 时长: 2.37秒
2025-07-29 10:40:17,565 - INFO -   - 验证音频时长: 2.37秒
2025-07-29 10:40:17,565 - INFO - 字幕时间戳信息:
2025-07-29 10:40:17,565 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:17,565 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:17,566 - INFO -   - 根据生成的音频时长(2.37秒)已调整字幕时间戳
2025-07-29 10:40:17,566 - INFO - ========== 新模式：为字幕 #2 生成4套场景方案 ==========
2025-07-29 10:40:17,566 - INFO - 字幕序号列表: [4, 7]
2025-07-29 10:40:17,566 - INFO - 
--- 生成方案 #1：基于字幕序号 #4 ---
2025-07-29 10:40:17,566 - INFO - 开始为单个字幕序号 #4 匹配场景，目标时长: 2.37秒
2025-07-29 10:40:17,566 - INFO - 开始查找字幕序号 [4] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:17,566 - INFO - 找到related_overlap场景: scene_id=18, 字幕#4
2025-07-29 10:40:17,566 - INFO - 找到related_overlap场景: scene_id=19, 字幕#4
2025-07-29 10:40:17,567 - INFO - 字幕 #4 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:17,567 - INFO - 字幕序号 #4 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:17,567 - INFO - 选择第一个overlap场景作为起点: scene_id=18
2025-07-29 10:40:17,567 - INFO - 添加起点场景: scene_id=18, 时长=2.04秒, 累计时长=2.04秒
2025-07-29 10:40:17,567 - INFO - 起点场景时长不足，需要延伸填充 0.33秒
2025-07-29 10:40:17,567 - INFO - 起点场景在原始列表中的索引: 17
2025-07-29 10:40:17,567 - INFO - 延伸添加场景: scene_id=19 (裁剪至 0.33秒)
2025-07-29 10:40:17,567 - INFO - 累计时长: 2.37秒
2025-07-29 10:40:17,567 - INFO - 字幕序号 #4 场景匹配完成，共选择 2 个场景，总时长: 2.37秒
2025-07-29 10:40:17,567 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:17,567 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:17,567 - INFO - 
--- 生成方案 #2：基于字幕序号 #7 ---
2025-07-29 10:40:17,567 - INFO - 开始为单个字幕序号 #7 匹配场景，目标时长: 2.37秒
2025-07-29 10:40:17,567 - INFO - 开始查找字幕序号 [7] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:17,567 - INFO - 找到related_overlap场景: scene_id=22, 字幕#7
2025-07-29 10:40:17,568 - INFO - 字幕 #7 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:17,568 - INFO - 字幕序号 #7 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:17,568 - INFO - 选择第一个overlap场景作为起点: scene_id=22
2025-07-29 10:40:17,568 - INFO - 添加起点场景: scene_id=22, 时长=4.48秒, 累计时长=4.48秒
2025-07-29 10:40:17,568 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:40:17,568 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:40:17,568 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:17,568 - INFO - ========== 当前模式：为字幕 #2 生成 1 套场景方案 ==========
2025-07-29 10:40:17,568 - INFO - 开始查找字幕序号 [4, 7] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:17,568 - INFO - 找到related_overlap场景: scene_id=18, 字幕#4
2025-07-29 10:40:17,568 - INFO - 找到related_overlap场景: scene_id=19, 字幕#4
2025-07-29 10:40:17,568 - INFO - 找到related_overlap场景: scene_id=22, 字幕#7
2025-07-29 10:40:17,569 - INFO - 字幕 #4 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:17,569 - INFO - 字幕 #7 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:17,569 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:40:17,569 - INFO - 开始生成方案 #1
2025-07-29 10:40:17,569 - INFO - 方案 #1: 为字幕#4选择初始化overlap场景id=19
2025-07-29 10:40:17,569 - INFO - 方案 #1: 为字幕#7选择初始化overlap场景id=22
2025-07-29 10:40:17,569 - INFO - 方案 #1: 初始选择后，当前总时长=5.12秒
2025-07-29 10:40:17,569 - INFO - 方案 #1: 额外between选择后，当前总时长=5.12秒
2025-07-29 10:40:17,569 - INFO - 方案 #1: 场景总时长(5.12秒)大于音频时长(2.37秒)，需要裁剪
2025-07-29 10:40:17,569 - INFO - 调整前总时长: 5.12秒, 目标时长: 2.37秒
2025-07-29 10:40:17,569 - INFO - 需要裁剪 2.75秒
2025-07-29 10:40:17,569 - INFO - 裁剪最长场景ID=22：从4.48秒裁剪至1.73秒
2025-07-29 10:40:17,569 - INFO - 调整后总时长: 2.37秒，与目标时长差异: 0.00秒
2025-07-29 10:40:17,569 - INFO - 方案 #1 调整/填充后最终总时长: 2.37秒
2025-07-29 10:40:17,569 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:17,569 - INFO - ========== 当前模式：字幕 #2 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:17,569 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:17,569 - INFO - ========== 新模式：字幕 #2 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:17,569 - INFO - 
----- 处理字幕 #2 的方案 #1 -----
2025-07-29 10:40:17,569 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-29 10:40:17,570 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1y8t3n6c
2025-07-29 10:40:17,570 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\18.mp4 (确认存在: True)
2025-07-29 10:40:17,570 - INFO - 添加场景ID=18，时长=2.04秒，累计时长=2.04秒
2025-07-29 10:40:17,570 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\19.mp4 (确认存在: True)
2025-07-29 10:40:17,570 - INFO - 添加场景ID=19，时长=0.64秒，累计时长=2.68秒
2025-07-29 10:40:17,570 - INFO - 准备合并 2 个场景文件，总时长约 2.68秒
2025-07-29 10:40:17,570 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/18.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/19.mp4'

2025-07-29 10:40:17,570 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1y8t3n6c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1y8t3n6c\temp_combined.mp4
2025-07-29 10:40:17,689 - INFO - 合并后的视频时长: 2.73秒，目标音频时长: 2.37秒
2025-07-29 10:40:17,689 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1y8t3n6c\temp_combined.mp4 -ss 0 -to 2.366 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-29 10:40:17,917 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:17,917 - INFO - 目标音频时长: 2.37秒
2025-07-29 10:40:17,917 - INFO - 实际视频时长: 2.42秒
2025-07-29 10:40:17,917 - INFO - 时长差异: 0.06秒 (2.41%)
2025-07-29 10:40:17,917 - INFO - ==========================================
2025-07-29 10:40:17,917 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:17,917 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-29 10:40:17,918 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1y8t3n6c
2025-07-29 10:40:17,959 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:17,959 - INFO -   - 音频时长: 2.37秒
2025-07-29 10:40:17,959 - INFO -   - 视频时长: 2.42秒
2025-07-29 10:40:17,959 - INFO -   - 时长差异: 0.06秒 (2.41%)
2025-07-29 10:40:17,959 - INFO - 
----- 处理字幕 #2 的方案 #2 -----
2025-07-29 10:40:17,959 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-29 10:40:17,959 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8cdnthro
2025-07-29 10:40:17,960 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\22.mp4 (确认存在: True)
2025-07-29 10:40:17,960 - INFO - 添加场景ID=22，时长=4.48秒，累计时长=4.48秒
2025-07-29 10:40:17,960 - INFO - 场景总时长(4.48秒)已达到音频时长(2.37秒)的1.5倍，停止添加场景
2025-07-29 10:40:17,960 - INFO - 准备合并 1 个场景文件，总时长约 4.48秒
2025-07-29 10:40:17,960 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/22.mp4'

2025-07-29 10:40:17,960 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8cdnthro\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8cdnthro\temp_combined.mp4
2025-07-29 10:40:18,064 - INFO - 合并后的视频时长: 4.50秒，目标音频时长: 2.37秒
2025-07-29 10:40:18,064 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8cdnthro\temp_combined.mp4 -ss 0 -to 2.366 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-29 10:40:18,310 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:18,311 - INFO - 目标音频时长: 2.37秒
2025-07-29 10:40:18,311 - INFO - 实际视频时长: 2.42秒
2025-07-29 10:40:18,311 - INFO - 时长差异: 0.06秒 (2.41%)
2025-07-29 10:40:18,311 - INFO - ==========================================
2025-07-29 10:40:18,311 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:18,311 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-29 10:40:18,311 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8cdnthro
2025-07-29 10:40:18,352 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:18,352 - INFO -   - 音频时长: 2.37秒
2025-07-29 10:40:18,352 - INFO -   - 视频时长: 2.42秒
2025-07-29 10:40:18,352 - INFO -   - 时长差异: 0.06秒 (2.41%)
2025-07-29 10:40:18,352 - INFO - 
----- 处理字幕 #2 的方案 #3 -----
2025-07-29 10:40:18,352 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-07-29 10:40:18,352 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprnjmet2m
2025-07-29 10:40:18,353 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\19.mp4 (确认存在: True)
2025-07-29 10:40:18,353 - INFO - 添加场景ID=19，时长=0.64秒，累计时长=0.64秒
2025-07-29 10:40:18,353 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\22.mp4 (确认存在: True)
2025-07-29 10:40:18,353 - INFO - 添加场景ID=22，时长=4.48秒，累计时长=5.12秒
2025-07-29 10:40:18,353 - INFO - 场景总时长(5.12秒)已达到音频时长(2.37秒)的1.5倍，停止添加场景
2025-07-29 10:40:18,353 - INFO - 准备合并 2 个场景文件，总时长约 5.12秒
2025-07-29 10:40:18,353 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/19.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/22.mp4'

2025-07-29 10:40:18,354 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmprnjmet2m\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmprnjmet2m\temp_combined.mp4
2025-07-29 10:40:18,474 - INFO - 合并后的视频时长: 5.17秒，目标音频时长: 2.37秒
2025-07-29 10:40:18,474 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmprnjmet2m\temp_combined.mp4 -ss 0 -to 2.366 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-07-29 10:40:18,717 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:18,717 - INFO - 目标音频时长: 2.37秒
2025-07-29 10:40:18,717 - INFO - 实际视频时长: 2.42秒
2025-07-29 10:40:18,717 - INFO - 时长差异: 0.06秒 (2.41%)
2025-07-29 10:40:18,717 - INFO - ==========================================
2025-07-29 10:40:18,717 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:18,718 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-07-29 10:40:18,718 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprnjmet2m
2025-07-29 10:40:18,761 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:18,761 - INFO -   - 音频时长: 2.37秒
2025-07-29 10:40:18,761 - INFO -   - 视频时长: 2.42秒
2025-07-29 10:40:18,761 - INFO -   - 时长差异: 0.06秒 (2.41%)
2025-07-29 10:40:18,761 - INFO - 
字幕 #2 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:18,761 - INFO - 生成的视频文件:
2025-07-29 10:40:18,761 - INFO -   1. F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-29 10:40:18,761 - INFO -   2. F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-07-29 10:40:18,761 - INFO -   3. F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-07-29 10:40:18,761 - INFO - ========== 字幕 #2 处理结束 ==========

