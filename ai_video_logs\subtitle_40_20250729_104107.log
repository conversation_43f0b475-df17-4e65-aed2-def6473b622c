2025-07-29 10:41:07,620 - INFO - ========== 字幕 #40 处理开始 ==========
2025-07-29 10:41:07,621 - INFO - 字幕内容: 哥哥赶到现场，却被告知她现在不方便见人。
2025-07-29 10:41:07,621 - INFO - 字幕序号: [228, 233]
2025-07-29 10:41:07,621 - INFO - 音频文件详情:
2025-07-29 10:41:07,621 - INFO -   - 路径: output\40.wav
2025-07-29 10:41:07,621 - INFO -   - 时长: 3.24秒
2025-07-29 10:41:07,621 - INFO -   - 验证音频时长: 3.24秒
2025-07-29 10:41:07,621 - INFO - 字幕时间戳信息:
2025-07-29 10:41:07,621 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:07,621 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:07,621 - INFO -   - 根据生成的音频时长(3.24秒)已调整字幕时间戳
2025-07-29 10:41:07,621 - INFO - ========== 新模式：为字幕 #40 生成4套场景方案 ==========
2025-07-29 10:41:07,621 - INFO - 字幕序号列表: [228, 233]
2025-07-29 10:41:07,621 - INFO - 
--- 生成方案 #1：基于字幕序号 #228 ---
2025-07-29 10:41:07,621 - INFO - 开始为单个字幕序号 #228 匹配场景，目标时长: 3.24秒
2025-07-29 10:41:07,621 - INFO - 开始查找字幕序号 [228] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:07,621 - INFO - 找到related_overlap场景: scene_id=336, 字幕#228
2025-07-29 10:41:07,622 - INFO - 找到related_between场景: scene_id=333, 字幕#228
2025-07-29 10:41:07,622 - INFO - 找到related_between场景: scene_id=334, 字幕#228
2025-07-29 10:41:07,622 - INFO - 找到related_between场景: scene_id=335, 字幕#228
2025-07-29 10:41:07,623 - INFO - 字幕 #228 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:41:07,623 - INFO - 字幕序号 #228 找到 1 个可用overlap场景, 3 个可用between场景
2025-07-29 10:41:07,623 - INFO - 选择第一个overlap场景作为起点: scene_id=336
2025-07-29 10:41:07,623 - INFO - 添加起点场景: scene_id=336, 时长=0.92秒, 累计时长=0.92秒
2025-07-29 10:41:07,623 - INFO - 起点场景时长不足，需要延伸填充 2.32秒
2025-07-29 10:41:07,623 - INFO - 起点场景在原始列表中的索引: 335
2025-07-29 10:41:07,623 - INFO - 延伸添加场景: scene_id=337 (完整时长 1.60秒)
2025-07-29 10:41:07,623 - INFO - 累计时长: 2.52秒
2025-07-29 10:41:07,623 - INFO - 延伸添加场景: scene_id=338 (裁剪至 0.72秒)
2025-07-29 10:41:07,623 - INFO - 累计时长: 3.24秒
2025-07-29 10:41:07,623 - INFO - 字幕序号 #228 场景匹配完成，共选择 3 个场景，总时长: 3.24秒
2025-07-29 10:41:07,623 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:07,623 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:07,623 - INFO - 
--- 生成方案 #2：基于字幕序号 #233 ---
2025-07-29 10:41:07,623 - INFO - 开始为单个字幕序号 #233 匹配场景，目标时长: 3.24秒
2025-07-29 10:41:07,623 - INFO - 开始查找字幕序号 [233] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:07,623 - INFO - 找到related_overlap场景: scene_id=347, 字幕#233
2025-07-29 10:41:07,624 - INFO - 找到related_between场景: scene_id=348, 字幕#233
2025-07-29 10:41:07,624 - INFO - 字幕 #233 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:07,624 - INFO - 字幕序号 #233 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 10:41:07,624 - INFO - 选择第一个overlap场景作为起点: scene_id=347
2025-07-29 10:41:07,624 - INFO - 添加起点场景: scene_id=347, 时长=2.32秒, 累计时长=2.32秒
2025-07-29 10:41:07,624 - INFO - 起点场景时长不足，需要延伸填充 0.92秒
2025-07-29 10:41:07,624 - INFO - 起点场景在原始列表中的索引: 346
2025-07-29 10:41:07,624 - INFO - 延伸添加场景: scene_id=348 (裁剪至 0.92秒)
2025-07-29 10:41:07,624 - INFO - 累计时长: 3.24秒
2025-07-29 10:41:07,624 - INFO - 字幕序号 #233 场景匹配完成，共选择 2 个场景，总时长: 3.24秒
2025-07-29 10:41:07,624 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:07,624 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:07,624 - INFO - ========== 当前模式：为字幕 #40 生成 1 套场景方案 ==========
2025-07-29 10:41:07,624 - INFO - 开始查找字幕序号 [228, 233] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:07,624 - INFO - 找到related_overlap场景: scene_id=336, 字幕#228
2025-07-29 10:41:07,624 - INFO - 找到related_overlap场景: scene_id=347, 字幕#233
2025-07-29 10:41:07,625 - INFO - 找到related_between场景: scene_id=333, 字幕#228
2025-07-29 10:41:07,625 - INFO - 找到related_between场景: scene_id=334, 字幕#228
2025-07-29 10:41:07,625 - INFO - 找到related_between场景: scene_id=335, 字幕#228
2025-07-29 10:41:07,625 - INFO - 找到related_between场景: scene_id=348, 字幕#233
2025-07-29 10:41:07,625 - INFO - 字幕 #228 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:41:07,625 - INFO - 字幕 #233 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:07,625 - INFO - 共收集 2 个未使用的overlap场景和 4 个未使用的between场景
2025-07-29 10:41:07,625 - INFO - 开始生成方案 #1
2025-07-29 10:41:07,625 - INFO - 方案 #1: 为字幕#228选择初始化overlap场景id=336
2025-07-29 10:41:07,625 - INFO - 方案 #1: 为字幕#233选择初始化overlap场景id=347
2025-07-29 10:41:07,625 - INFO - 方案 #1: 初始选择后，当前总时长=3.24秒
2025-07-29 10:41:07,625 - INFO - 方案 #1: 额外between选择后，当前总时长=3.24秒
2025-07-29 10:41:07,625 - INFO - 方案 #1: 场景总时长(3.24秒)大于音频时长(3.24秒)，需要裁剪
2025-07-29 10:41:07,625 - INFO - 调整前总时长: 3.24秒, 目标时长: 3.24秒
2025-07-29 10:41:07,625 - INFO - 场景时长已经匹配目标时长，无需调整
2025-07-29 10:41:07,626 - INFO - 方案 #1 调整/填充后最终总时长: 3.24秒
2025-07-29 10:41:07,626 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:07,626 - INFO - ========== 当前模式：字幕 #40 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:07,626 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:07,626 - INFO - ========== 新模式：字幕 #40 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:07,626 - INFO - 
----- 处理字幕 #40 的方案 #1 -----
2025-07-29 10:41:07,626 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-07-29 10:41:07,626 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpemhoqzrp
2025-07-29 10:41:07,626 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\336.mp4 (确认存在: True)
2025-07-29 10:41:07,626 - INFO - 添加场景ID=336，时长=0.92秒，累计时长=0.92秒
2025-07-29 10:41:07,626 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\337.mp4 (确认存在: True)
2025-07-29 10:41:07,626 - INFO - 添加场景ID=337，时长=1.60秒，累计时长=2.52秒
2025-07-29 10:41:07,626 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\338.mp4 (确认存在: True)
2025-07-29 10:41:07,626 - INFO - 添加场景ID=338，时长=1.16秒，累计时长=3.68秒
2025-07-29 10:41:07,626 - INFO - 准备合并 3 个场景文件，总时长约 3.68秒
2025-07-29 10:41:07,626 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/336.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/337.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/338.mp4'

2025-07-29 10:41:07,626 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpemhoqzrp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpemhoqzrp\temp_combined.mp4
2025-07-29 10:41:07,780 - INFO - 合并后的视频时长: 3.75秒，目标音频时长: 3.24秒
2025-07-29 10:41:07,780 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpemhoqzrp\temp_combined.mp4 -ss 0 -to 3.236 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-07-29 10:41:08,062 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:08,062 - INFO - 目标音频时长: 3.24秒
2025-07-29 10:41:08,062 - INFO - 实际视频时长: 3.26秒
2025-07-29 10:41:08,062 - INFO - 时长差异: 0.03秒 (0.83%)
2025-07-29 10:41:08,062 - INFO - ==========================================
2025-07-29 10:41:08,062 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:08,062 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-07-29 10:41:08,063 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpemhoqzrp
2025-07-29 10:41:08,106 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:08,106 - INFO -   - 音频时长: 3.24秒
2025-07-29 10:41:08,106 - INFO -   - 视频时长: 3.26秒
2025-07-29 10:41:08,106 - INFO -   - 时长差异: 0.03秒 (0.83%)
2025-07-29 10:41:08,106 - INFO - 
----- 处理字幕 #40 的方案 #2 -----
2025-07-29 10:41:08,106 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-07-29 10:41:08,107 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0av_rtof
2025-07-29 10:41:08,108 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\347.mp4 (确认存在: True)
2025-07-29 10:41:08,108 - INFO - 添加场景ID=347，时长=2.32秒，累计时长=2.32秒
2025-07-29 10:41:08,108 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\348.mp4 (确认存在: True)
2025-07-29 10:41:08,108 - INFO - 添加场景ID=348，时长=1.00秒，累计时长=3.32秒
2025-07-29 10:41:08,108 - INFO - 准备合并 2 个场景文件，总时长约 3.32秒
2025-07-29 10:41:08,108 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/347.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/348.mp4'

2025-07-29 10:41:08,108 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0av_rtof\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0av_rtof\temp_combined.mp4
2025-07-29 10:41:08,228 - INFO - 合并后的视频时长: 3.37秒，目标音频时长: 3.24秒
2025-07-29 10:41:08,228 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0av_rtof\temp_combined.mp4 -ss 0 -to 3.236 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-07-29 10:41:08,480 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:08,480 - INFO - 目标音频时长: 3.24秒
2025-07-29 10:41:08,480 - INFO - 实际视频时长: 3.26秒
2025-07-29 10:41:08,480 - INFO - 时长差异: 0.03秒 (0.83%)
2025-07-29 10:41:08,480 - INFO - ==========================================
2025-07-29 10:41:08,480 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:08,480 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-07-29 10:41:08,480 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0av_rtof
2025-07-29 10:41:08,523 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:08,523 - INFO -   - 音频时长: 3.24秒
2025-07-29 10:41:08,524 - INFO -   - 视频时长: 3.26秒
2025-07-29 10:41:08,524 - INFO -   - 时长差异: 0.03秒 (0.83%)
2025-07-29 10:41:08,524 - INFO - 
----- 处理字幕 #40 的方案 #3 -----
2025-07-29 10:41:08,524 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-07-29 10:41:08,524 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppboaja_q
2025-07-29 10:41:08,524 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\336.mp4 (确认存在: True)
2025-07-29 10:41:08,524 - INFO - 添加场景ID=336，时长=0.92秒，累计时长=0.92秒
2025-07-29 10:41:08,524 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\347.mp4 (确认存在: True)
2025-07-29 10:41:08,525 - INFO - 添加场景ID=347，时长=2.32秒，累计时长=3.24秒
2025-07-29 10:41:08,525 - INFO - 准备合并 2 个场景文件，总时长约 3.24秒
2025-07-29 10:41:08,525 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/336.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/347.mp4'

2025-07-29 10:41:08,525 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppboaja_q\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppboaja_q\temp_combined.mp4
2025-07-29 10:41:08,647 - INFO - 合并后的视频时长: 3.29秒，目标音频时长: 3.24秒
2025-07-29 10:41:08,647 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppboaja_q\temp_combined.mp4 -ss 0 -to 3.236 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_3.mp4
