2025-07-29 10:41:30,553 - INFO - ========== 字幕 #54 处理开始 ==========
2025-07-29 10:41:30,554 - INFO - 字幕内容: 见她失落，哥哥假意要罚，她只能强颜欢笑。
2025-07-29 10:41:30,554 - INFO - 字幕序号: [308, 313]
2025-07-29 10:41:30,554 - INFO - 音频文件详情:
2025-07-29 10:41:30,554 - INFO -   - 路径: output\54.wav
2025-07-29 10:41:30,554 - INFO -   - 时长: 3.21秒
2025-07-29 10:41:30,554 - INFO -   - 验证音频时长: 3.21秒
2025-07-29 10:41:30,555 - INFO - 字幕时间戳信息:
2025-07-29 10:41:30,555 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:30,555 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:30,555 - INFO -   - 根据生成的音频时长(3.21秒)已调整字幕时间戳
2025-07-29 10:41:30,555 - INFO - ========== 新模式：为字幕 #54 生成4套场景方案 ==========
2025-07-29 10:41:30,555 - INFO - 字幕序号列表: [308, 313]
2025-07-29 10:41:30,555 - INFO - 
--- 生成方案 #1：基于字幕序号 #308 ---
2025-07-29 10:41:30,555 - INFO - 开始为单个字幕序号 #308 匹配场景，目标时长: 3.21秒
2025-07-29 10:41:30,555 - INFO - 开始查找字幕序号 [308] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:30,555 - INFO - 找到related_overlap场景: scene_id=450, 字幕#308
2025-07-29 10:41:30,559 - INFO - 字幕 #308 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:30,559 - INFO - 字幕序号 #308 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:30,559 - INFO - 选择第一个overlap场景作为起点: scene_id=450
2025-07-29 10:41:30,559 - INFO - 添加起点场景: scene_id=450, 时长=4.20秒, 累计时长=4.20秒
2025-07-29 10:41:30,560 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:30,560 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:41:30,560 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:41:30,560 - INFO - 
--- 生成方案 #2：基于字幕序号 #313 ---
2025-07-29 10:41:30,560 - INFO - 开始为单个字幕序号 #313 匹配场景，目标时长: 3.21秒
2025-07-29 10:41:30,560 - INFO - 开始查找字幕序号 [313] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:30,560 - INFO - 找到related_overlap场景: scene_id=453, 字幕#313
2025-07-29 10:41:30,562 - INFO - 字幕 #313 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:30,562 - INFO - 字幕序号 #313 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:30,563 - INFO - 选择第一个overlap场景作为起点: scene_id=453
2025-07-29 10:41:30,563 - INFO - 添加起点场景: scene_id=453, 时长=3.20秒, 累计时长=3.20秒
2025-07-29 10:41:30,563 - INFO - 起点场景时长不足，需要延伸填充 0.01秒
2025-07-29 10:41:30,563 - INFO - 起点场景在原始列表中的索引: 452
2025-07-29 10:41:30,563 - INFO - 延伸添加场景: scene_id=454 (裁剪至 0.01秒)
2025-07-29 10:41:30,563 - INFO - 累计时长: 3.21秒
2025-07-29 10:41:30,563 - INFO - 字幕序号 #313 场景匹配完成，共选择 2 个场景，总时长: 3.21秒
2025-07-29 10:41:30,563 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:30,563 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:30,563 - INFO - ========== 当前模式：为字幕 #54 生成 1 套场景方案 ==========
2025-07-29 10:41:30,563 - INFO - 开始查找字幕序号 [308, 313] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:30,563 - INFO - 找到related_overlap场景: scene_id=450, 字幕#308
2025-07-29 10:41:30,563 - INFO - 找到related_overlap场景: scene_id=453, 字幕#313
2025-07-29 10:41:30,566 - INFO - 字幕 #308 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:30,566 - INFO - 字幕 #313 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:30,566 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:30,566 - INFO - 开始生成方案 #1
2025-07-29 10:41:30,566 - INFO - 方案 #1: 为字幕#308选择初始化overlap场景id=450
2025-07-29 10:41:30,566 - INFO - 方案 #1: 为字幕#313选择初始化overlap场景id=453
2025-07-29 10:41:30,566 - INFO - 方案 #1: 初始选择后，当前总时长=7.40秒
2025-07-29 10:41:30,566 - INFO - 方案 #1: 额外between选择后，当前总时长=7.40秒
2025-07-29 10:41:30,566 - INFO - 方案 #1: 场景总时长(7.40秒)大于音频时长(3.21秒)，需要裁剪
2025-07-29 10:41:30,566 - INFO - 调整前总时长: 7.40秒, 目标时长: 3.21秒
2025-07-29 10:41:30,566 - INFO - 需要裁剪 4.19秒
2025-07-29 10:41:30,566 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:30,566 - INFO - 裁剪场景ID=450：从4.20秒裁剪至1.26秒
2025-07-29 10:41:30,566 - INFO - 裁剪场景ID=453：从3.20秒裁剪至1.95秒
2025-07-29 10:41:30,566 - INFO - 调整后总时长: 3.21秒，与目标时长差异: 0.00秒
2025-07-29 10:41:30,566 - INFO - 方案 #1 调整/填充后最终总时长: 3.21秒
2025-07-29 10:41:30,566 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:30,566 - INFO - ========== 当前模式：字幕 #54 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:30,566 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:30,566 - INFO - ========== 新模式：字幕 #54 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:30,566 - INFO - 
----- 处理字幕 #54 的方案 #1 -----
2025-07-29 10:41:30,567 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-29 10:41:30,567 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_2629mx0
2025-07-29 10:41:30,568 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\450.mp4 (确认存在: True)
2025-07-29 10:41:30,568 - INFO - 添加场景ID=450，时长=4.20秒，累计时长=4.20秒
2025-07-29 10:41:30,568 - INFO - 准备合并 1 个场景文件，总时长约 4.20秒
2025-07-29 10:41:30,568 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/450.mp4'

2025-07-29 10:41:30,568 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_2629mx0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_2629mx0\temp_combined.mp4
2025-07-29 10:41:30,736 - INFO - 合并后的视频时长: 4.22秒，目标音频时长: 3.21秒
2025-07-29 10:41:30,736 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_2629mx0\temp_combined.mp4 -ss 0 -to 3.206 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-29 10:41:31,095 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:31,095 - INFO - 目标音频时长: 3.21秒
2025-07-29 10:41:31,095 - INFO - 实际视频时长: 3.26秒
2025-07-29 10:41:31,095 - INFO - 时长差异: 0.06秒 (1.78%)
2025-07-29 10:41:31,095 - INFO - ==========================================
2025-07-29 10:41:31,095 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:31,095 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-29 10:41:31,096 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_2629mx0
2025-07-29 10:41:31,160 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:31,160 - INFO -   - 音频时长: 3.21秒
2025-07-29 10:41:31,160 - INFO -   - 视频时长: 3.26秒
2025-07-29 10:41:31,160 - INFO -   - 时长差异: 0.06秒 (1.78%)
2025-07-29 10:41:31,160 - INFO - 
----- 处理字幕 #54 的方案 #2 -----
2025-07-29 10:41:31,160 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-29 10:41:31,161 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphvt8asmd
2025-07-29 10:41:31,162 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\453.mp4 (确认存在: True)
2025-07-29 10:41:31,162 - INFO - 添加场景ID=453，时长=3.20秒，累计时长=3.20秒
2025-07-29 10:41:31,162 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\454.mp4 (确认存在: True)
2025-07-29 10:41:31,162 - INFO - 添加场景ID=454，时长=2.72秒，累计时长=5.92秒
2025-07-29 10:41:31,162 - INFO - 场景总时长(5.92秒)已达到音频时长(3.21秒)的1.5倍，停止添加场景
2025-07-29 10:41:31,162 - INFO - 准备合并 2 个场景文件，总时长约 5.92秒
2025-07-29 10:41:31,162 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/453.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/454.mp4'

2025-07-29 10:41:31,163 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphvt8asmd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphvt8asmd\temp_combined.mp4
2025-07-29 10:41:31,318 - INFO - 合并后的视频时长: 5.97秒，目标音频时长: 3.21秒
2025-07-29 10:41:31,318 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphvt8asmd\temp_combined.mp4 -ss 0 -to 3.206 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-29 10:41:31,649 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:31,649 - INFO - 目标音频时长: 3.21秒
2025-07-29 10:41:31,649 - INFO - 实际视频时长: 3.26秒
2025-07-29 10:41:31,649 - INFO - 时长差异: 0.06秒 (1.78%)
2025-07-29 10:41:31,649 - INFO - ==========================================
2025-07-29 10:41:31,649 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:31,649 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-29 10:41:31,651 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphvt8asmd
2025-07-29 10:41:31,714 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:31,714 - INFO -   - 音频时长: 3.21秒
2025-07-29 10:41:31,714 - INFO -   - 视频时长: 3.26秒
2025-07-29 10:41:31,714 - INFO -   - 时长差异: 0.06秒 (1.78%)
2025-07-29 10:41:31,714 - INFO - 
----- 处理字幕 #54 的方案 #3 -----
2025-07-29 10:41:31,714 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-07-29 10:41:31,715 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8h8romya
2025-07-29 10:41:31,715 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\450.mp4 (确认存在: True)
2025-07-29 10:41:31,715 - INFO - 添加场景ID=450，时长=4.20秒，累计时长=4.20秒
2025-07-29 10:41:31,715 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\453.mp4 (确认存在: True)
2025-07-29 10:41:31,715 - INFO - 添加场景ID=453，时长=3.20秒，累计时长=7.40秒
2025-07-29 10:41:31,715 - INFO - 场景总时长(7.40秒)已达到音频时长(3.21秒)的1.5倍，停止添加场景
2025-07-29 10:41:31,716 - INFO - 准备合并 2 个场景文件，总时长约 7.40秒
2025-07-29 10:41:31,716 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/450.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/453.mp4'

2025-07-29 10:41:31,716 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8h8romya\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8h8romya\temp_combined.mp4
2025-07-29 10:41:31,875 - INFO - 合并后的视频时长: 7.45秒，目标音频时长: 3.21秒
2025-07-29 10:41:31,875 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8h8romya\temp_combined.mp4 -ss 0 -to 3.206 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-07-29 10:41:32,180 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:32,180 - INFO - 目标音频时长: 3.21秒
2025-07-29 10:41:32,180 - INFO - 实际视频时长: 3.26秒
2025-07-29 10:41:32,180 - INFO - 时长差异: 0.06秒 (1.78%)
2025-07-29 10:41:32,180 - INFO - ==========================================
2025-07-29 10:41:32,180 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:32,180 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-07-29 10:41:32,182 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8h8romya
2025-07-29 10:41:32,244 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:32,244 - INFO -   - 音频时长: 3.21秒
2025-07-29 10:41:32,244 - INFO -   - 视频时长: 3.26秒
2025-07-29 10:41:32,244 - INFO -   - 时长差异: 0.06秒 (1.78%)
2025-07-29 10:41:32,244 - INFO - 
字幕 #54 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:32,245 - INFO - 生成的视频文件:
2025-07-29 10:41:32,245 - INFO -   1. F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-07-29 10:41:32,245 - INFO -   2. F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-07-29 10:41:32,245 - INFO -   3. F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-07-29 10:41:32,245 - INFO - ========== 字幕 #54 处理结束 ==========

