2025-07-29 10:41:32,245 - INFO - ========== 字幕 #55 处理开始 ==========
2025-07-29 10:41:32,246 - INFO - 字幕内容: 祖母心疼她，她却只能强撑着说自己不怕。
2025-07-29 10:41:32,246 - INFO - 字幕序号: [314, 318]
2025-07-29 10:41:32,246 - INFO - 音频文件详情:
2025-07-29 10:41:32,246 - INFO -   - 路径: output\55.wav
2025-07-29 10:41:32,246 - INFO -   - 时长: 2.55秒
2025-07-29 10:41:32,246 - INFO -   - 验证音频时长: 2.55秒
2025-07-29 10:41:32,247 - INFO - 字幕时间戳信息:
2025-07-29 10:41:32,247 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:32,247 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:32,247 - INFO -   - 根据生成的音频时长(2.55秒)已调整字幕时间戳
2025-07-29 10:41:32,247 - INFO - ========== 新模式：为字幕 #55 生成4套场景方案 ==========
2025-07-29 10:41:32,247 - INFO - 字幕序号列表: [314, 318]
2025-07-29 10:41:32,247 - INFO - 
--- 生成方案 #1：基于字幕序号 #314 ---
2025-07-29 10:41:32,247 - INFO - 开始为单个字幕序号 #314 匹配场景，目标时长: 2.55秒
2025-07-29 10:41:32,247 - INFO - 开始查找字幕序号 [314] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:32,248 - INFO - 找到related_overlap场景: scene_id=454, 字幕#314
2025-07-29 10:41:32,251 - INFO - 字幕 #314 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:32,251 - INFO - 字幕序号 #314 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:32,251 - INFO - 选择第一个overlap场景作为起点: scene_id=454
2025-07-29 10:41:32,251 - INFO - 添加起点场景: scene_id=454, 时长=2.72秒, 累计时长=2.72秒
2025-07-29 10:41:32,251 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:32,251 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:41:32,251 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:41:32,251 - INFO - 
--- 生成方案 #2：基于字幕序号 #318 ---
2025-07-29 10:41:32,251 - INFO - 开始为单个字幕序号 #318 匹配场景，目标时长: 2.55秒
2025-07-29 10:41:32,252 - INFO - 开始查找字幕序号 [318] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:32,252 - INFO - 找到related_overlap场景: scene_id=456, 字幕#318
2025-07-29 10:41:32,255 - INFO - 字幕 #318 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:32,255 - INFO - 字幕序号 #318 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:32,255 - INFO - 选择第一个overlap场景作为起点: scene_id=456
2025-07-29 10:41:32,255 - INFO - 添加起点场景: scene_id=456, 时长=5.76秒, 累计时长=5.76秒
2025-07-29 10:41:32,255 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:32,255 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:41:32,255 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:32,255 - INFO - ========== 当前模式：为字幕 #55 生成 1 套场景方案 ==========
2025-07-29 10:41:32,255 - INFO - 开始查找字幕序号 [314, 318] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:32,255 - INFO - 找到related_overlap场景: scene_id=454, 字幕#314
2025-07-29 10:41:32,255 - INFO - 找到related_overlap场景: scene_id=456, 字幕#318
2025-07-29 10:41:32,257 - INFO - 字幕 #314 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:32,257 - INFO - 字幕 #318 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:32,257 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:32,257 - INFO - 开始生成方案 #1
2025-07-29 10:41:32,257 - INFO - 方案 #1: 为字幕#314选择初始化overlap场景id=454
2025-07-29 10:41:32,257 - INFO - 方案 #1: 为字幕#318选择初始化overlap场景id=456
2025-07-29 10:41:32,257 - INFO - 方案 #1: 初始选择后，当前总时长=8.48秒
2025-07-29 10:41:32,257 - INFO - 方案 #1: 额外between选择后，当前总时长=8.48秒
2025-07-29 10:41:32,257 - INFO - 方案 #1: 场景总时长(8.48秒)大于音频时长(2.55秒)，需要裁剪
2025-07-29 10:41:32,257 - INFO - 调整前总时长: 8.48秒, 目标时长: 2.55秒
2025-07-29 10:41:32,257 - INFO - 需要裁剪 5.92秒
2025-07-29 10:41:32,257 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:32,257 - INFO - 裁剪场景ID=456：从5.76秒裁剪至1.73秒
2025-07-29 10:41:32,257 - INFO - 裁剪场景ID=454：从2.72秒裁剪至1.73秒
2025-07-29 10:41:32,257 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.90秒
2025-07-29 10:41:32,258 - INFO - 调整后总时长: 3.46秒，与目标时长差异: 0.90秒
2025-07-29 10:41:32,258 - INFO - 方案 #1 调整/填充后最终总时长: 3.46秒
2025-07-29 10:41:32,258 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:32,258 - INFO - ========== 当前模式：字幕 #55 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:32,258 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:32,258 - INFO - ========== 新模式：字幕 #55 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:32,258 - INFO - 
----- 处理字幕 #55 的方案 #1 -----
2025-07-29 10:41:32,258 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-29 10:41:32,258 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwi06zy7j
2025-07-29 10:41:32,259 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\454.mp4 (确认存在: True)
2025-07-29 10:41:32,259 - INFO - 添加场景ID=454，时长=2.72秒，累计时长=2.72秒
2025-07-29 10:41:32,259 - INFO - 准备合并 1 个场景文件，总时长约 2.72秒
2025-07-29 10:41:32,260 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/454.mp4'

2025-07-29 10:41:32,260 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwi06zy7j\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwi06zy7j\temp_combined.mp4
2025-07-29 10:41:32,403 - INFO - 合并后的视频时长: 2.74秒，目标音频时长: 2.55秒
2025-07-29 10:41:32,403 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwi06zy7j\temp_combined.mp4 -ss 0 -to 2.554 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-29 10:41:32,686 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:32,686 - INFO - 目标音频时长: 2.55秒
2025-07-29 10:41:32,686 - INFO - 实际视频时长: 2.58秒
2025-07-29 10:41:32,686 - INFO - 时长差异: 0.03秒 (1.14%)
2025-07-29 10:41:32,687 - INFO - ==========================================
2025-07-29 10:41:32,687 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:32,687 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-29 10:41:32,688 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwi06zy7j
2025-07-29 10:41:32,750 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:32,750 - INFO -   - 音频时长: 2.55秒
2025-07-29 10:41:32,750 - INFO -   - 视频时长: 2.58秒
2025-07-29 10:41:32,750 - INFO -   - 时长差异: 0.03秒 (1.14%)
2025-07-29 10:41:32,750 - INFO - 
----- 处理字幕 #55 的方案 #2 -----
2025-07-29 10:41:32,750 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-29 10:41:32,751 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpix5ixfsz
2025-07-29 10:41:32,752 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\456.mp4 (确认存在: True)
2025-07-29 10:41:32,752 - INFO - 添加场景ID=456，时长=5.76秒，累计时长=5.76秒
2025-07-29 10:41:32,752 - INFO - 场景总时长(5.76秒)已达到音频时长(2.55秒)的1.5倍，停止添加场景
2025-07-29 10:41:32,752 - INFO - 准备合并 1 个场景文件，总时长约 5.76秒
2025-07-29 10:41:32,752 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/456.mp4'

2025-07-29 10:41:32,752 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpix5ixfsz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpix5ixfsz\temp_combined.mp4
2025-07-29 10:41:32,914 - INFO - 合并后的视频时长: 5.78秒，目标音频时长: 2.55秒
2025-07-29 10:41:32,914 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpix5ixfsz\temp_combined.mp4 -ss 0 -to 2.554 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-29 10:41:33,230 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:33,231 - INFO - 目标音频时长: 2.55秒
2025-07-29 10:41:33,231 - INFO - 实际视频时长: 2.58秒
2025-07-29 10:41:33,231 - INFO - 时长差异: 0.03秒 (1.14%)
2025-07-29 10:41:33,231 - INFO - ==========================================
2025-07-29 10:41:33,231 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:33,231 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-29 10:41:33,232 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpix5ixfsz
2025-07-29 10:41:33,289 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:33,289 - INFO -   - 音频时长: 2.55秒
2025-07-29 10:41:33,289 - INFO -   - 视频时长: 2.58秒
2025-07-29 10:41:33,289 - INFO -   - 时长差异: 0.03秒 (1.14%)
2025-07-29 10:41:33,289 - INFO - 
----- 处理字幕 #55 的方案 #3 -----
2025-07-29 10:41:33,289 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\55_3.mp4
2025-07-29 10:41:33,289 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpou5gk9_3
2025-07-29 10:41:33,290 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\454.mp4 (确认存在: True)
2025-07-29 10:41:33,290 - INFO - 添加场景ID=454，时长=2.72秒，累计时长=2.72秒
2025-07-29 10:41:33,290 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\456.mp4 (确认存在: True)
2025-07-29 10:41:33,290 - INFO - 添加场景ID=456，时长=5.76秒，累计时长=8.48秒
2025-07-29 10:41:33,290 - INFO - 场景总时长(8.48秒)已达到音频时长(2.55秒)的1.5倍，停止添加场景
2025-07-29 10:41:33,291 - INFO - 准备合并 2 个场景文件，总时长约 8.48秒
2025-07-29 10:41:33,291 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/454.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/456.mp4'

2025-07-29 10:41:33,291 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpou5gk9_3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpou5gk9_3\temp_combined.mp4
2025-07-29 10:41:33,450 - INFO - 合并后的视频时长: 8.53秒，目标音频时长: 2.55秒
2025-07-29 10:41:33,450 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpou5gk9_3\temp_combined.mp4 -ss 0 -to 2.554 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\55_3.mp4
2025-07-29 10:41:33,776 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:33,776 - INFO - 目标音频时长: 2.55秒
2025-07-29 10:41:33,776 - INFO - 实际视频时长: 2.58秒
2025-07-29 10:41:33,776 - INFO - 时长差异: 0.03秒 (1.14%)
2025-07-29 10:41:33,776 - INFO - ==========================================
2025-07-29 10:41:33,776 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:33,776 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\55_3.mp4
2025-07-29 10:41:33,777 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpou5gk9_3
2025-07-29 10:41:33,835 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:33,835 - INFO -   - 音频时长: 2.55秒
2025-07-29 10:41:33,835 - INFO -   - 视频时长: 2.58秒
2025-07-29 10:41:33,835 - INFO -   - 时长差异: 0.03秒 (1.14%)
2025-07-29 10:41:33,835 - INFO - 
字幕 #55 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:33,835 - INFO - 生成的视频文件:
2025-07-29 10:41:33,835 - INFO -   1. F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-07-29 10:41:33,835 - INFO -   2. F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-07-29 10:41:33,835 - INFO -   3. F:/github/aicut_auto/newcut_ai\55_3.mp4
2025-07-29 10:41:33,835 - INFO - ========== 字幕 #55 处理结束 ==========

