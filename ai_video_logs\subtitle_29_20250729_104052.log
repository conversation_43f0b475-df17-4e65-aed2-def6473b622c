2025-07-29 10:40:52,994 - INFO - ========== 字幕 #29 处理开始 ==========
2025-07-29 10:40:52,994 - INFO - 字幕内容: 九姨娘趁机推荐刘青书，称其与女人家世相配。
2025-07-29 10:40:52,994 - INFO - 字幕序号: [160, 166]
2025-07-29 10:40:52,995 - INFO - 音频文件详情:
2025-07-29 10:40:52,995 - INFO -   - 路径: output\29.wav
2025-07-29 10:40:52,995 - INFO -   - 时长: 3.96秒
2025-07-29 10:40:52,995 - INFO -   - 验证音频时长: 3.96秒
2025-07-29 10:40:52,995 - INFO - 字幕时间戳信息:
2025-07-29 10:40:52,995 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:52,995 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:52,995 - INFO -   - 根据生成的音频时长(3.96秒)已调整字幕时间戳
2025-07-29 10:40:52,995 - INFO - ========== 新模式：为字幕 #29 生成4套场景方案 ==========
2025-07-29 10:40:52,995 - INFO - 字幕序号列表: [160, 166]
2025-07-29 10:40:52,995 - INFO - 
--- 生成方案 #1：基于字幕序号 #160 ---
2025-07-29 10:40:52,995 - INFO - 开始为单个字幕序号 #160 匹配场景，目标时长: 3.96秒
2025-07-29 10:40:52,995 - INFO - 开始查找字幕序号 [160] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:52,995 - INFO - 找到related_overlap场景: scene_id=238, 字幕#160
2025-07-29 10:40:52,997 - INFO - 找到related_between场景: scene_id=237, 字幕#160
2025-07-29 10:40:52,997 - INFO - 字幕 #160 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:40:52,997 - INFO - 字幕序号 #160 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 10:40:52,997 - INFO - 选择第一个overlap场景作为起点: scene_id=238
2025-07-29 10:40:52,997 - INFO - 添加起点场景: scene_id=238, 时长=2.04秒, 累计时长=2.04秒
2025-07-29 10:40:52,997 - INFO - 起点场景时长不足，需要延伸填充 1.92秒
2025-07-29 10:40:52,997 - INFO - 起点场景在原始列表中的索引: 237
2025-07-29 10:40:52,997 - INFO - 延伸添加场景: scene_id=239 (裁剪至 1.92秒)
2025-07-29 10:40:52,997 - INFO - 累计时长: 3.96秒
2025-07-29 10:40:52,997 - INFO - 字幕序号 #160 场景匹配完成，共选择 2 个场景，总时长: 3.96秒
2025-07-29 10:40:52,997 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:52,997 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:52,997 - INFO - 
--- 生成方案 #2：基于字幕序号 #166 ---
2025-07-29 10:40:52,997 - INFO - 开始为单个字幕序号 #166 匹配场景，目标时长: 3.96秒
2025-07-29 10:40:52,997 - INFO - 开始查找字幕序号 [166] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:52,997 - INFO - 找到related_overlap场景: scene_id=243, 字幕#166
2025-07-29 10:40:52,997 - INFO - 找到related_overlap场景: scene_id=244, 字幕#166
2025-07-29 10:40:52,998 - INFO - 字幕 #166 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:52,998 - INFO - 字幕序号 #166 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:52,998 - INFO - 选择第一个overlap场景作为起点: scene_id=243
2025-07-29 10:40:52,998 - INFO - 添加起点场景: scene_id=243, 时长=1.44秒, 累计时长=1.44秒
2025-07-29 10:40:52,998 - INFO - 起点场景时长不足，需要延伸填充 2.52秒
2025-07-29 10:40:52,998 - INFO - 起点场景在原始列表中的索引: 242
2025-07-29 10:40:52,998 - INFO - 延伸添加场景: scene_id=244 (完整时长 1.16秒)
2025-07-29 10:40:52,998 - INFO - 累计时长: 2.60秒
2025-07-29 10:40:52,998 - INFO - 延伸添加场景: scene_id=245 (裁剪至 1.36秒)
2025-07-29 10:40:52,998 - INFO - 累计时长: 3.96秒
2025-07-29 10:40:52,998 - INFO - 字幕序号 #166 场景匹配完成，共选择 3 个场景，总时长: 3.96秒
2025-07-29 10:40:52,998 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:52,998 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:52,998 - INFO - ========== 当前模式：为字幕 #29 生成 1 套场景方案 ==========
2025-07-29 10:40:52,998 - INFO - 开始查找字幕序号 [160, 166] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:52,998 - INFO - 找到related_overlap场景: scene_id=238, 字幕#160
2025-07-29 10:40:52,998 - INFO - 找到related_overlap场景: scene_id=243, 字幕#166
2025-07-29 10:40:52,998 - INFO - 找到related_overlap场景: scene_id=244, 字幕#166
2025-07-29 10:40:52,999 - INFO - 找到related_between场景: scene_id=237, 字幕#160
2025-07-29 10:40:52,999 - INFO - 字幕 #160 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:40:52,999 - INFO - 字幕 #166 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:52,999 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 10:40:52,999 - INFO - 开始生成方案 #1
2025-07-29 10:40:52,999 - INFO - 方案 #1: 为字幕#160选择初始化overlap场景id=238
2025-07-29 10:40:52,999 - INFO - 方案 #1: 为字幕#166选择初始化overlap场景id=244
2025-07-29 10:40:52,999 - INFO - 方案 #1: 初始选择后，当前总时长=3.20秒
2025-07-29 10:40:52,999 - INFO - 方案 #1: 额外添加overlap场景id=243, 当前总时长=4.64秒
2025-07-29 10:40:52,999 - INFO - 方案 #1: 额外between选择后，当前总时长=4.64秒
2025-07-29 10:40:52,999 - INFO - 方案 #1: 场景总时长(4.64秒)大于音频时长(3.96秒)，需要裁剪
2025-07-29 10:40:52,999 - INFO - 调整前总时长: 4.64秒, 目标时长: 3.96秒
2025-07-29 10:40:52,999 - INFO - 需要裁剪 0.68秒
2025-07-29 10:40:52,999 - INFO - 裁剪最长场景ID=238：从2.04秒裁剪至1.36秒
2025-07-29 10:40:52,999 - INFO - 调整后总时长: 3.96秒，与目标时长差异: 0.00秒
2025-07-29 10:40:52,999 - INFO - 方案 #1 调整/填充后最终总时长: 3.96秒
2025-07-29 10:40:53,000 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:53,000 - INFO - ========== 当前模式：字幕 #29 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:53,000 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:53,000 - INFO - ========== 新模式：字幕 #29 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:53,000 - INFO - 
----- 处理字幕 #29 的方案 #1 -----
2025-07-29 10:40:53,000 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-07-29 10:40:53,000 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0z1k6sc_
2025-07-29 10:40:53,001 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\238.mp4 (确认存在: True)
2025-07-29 10:40:53,001 - INFO - 添加场景ID=238，时长=2.04秒，累计时长=2.04秒
2025-07-29 10:40:53,001 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\239.mp4 (确认存在: True)
2025-07-29 10:40:53,001 - INFO - 添加场景ID=239，时长=2.72秒，累计时长=4.76秒
2025-07-29 10:40:53,001 - INFO - 准备合并 2 个场景文件，总时长约 4.76秒
2025-07-29 10:40:53,001 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/238.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/239.mp4'

2025-07-29 10:40:53,001 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0z1k6sc_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0z1k6sc_\temp_combined.mp4
2025-07-29 10:40:53,132 - INFO - 合并后的视频时长: 4.81秒，目标音频时长: 3.96秒
2025-07-29 10:40:53,132 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0z1k6sc_\temp_combined.mp4 -ss 0 -to 3.958 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-07-29 10:40:53,423 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:53,423 - INFO - 目标音频时长: 3.96秒
2025-07-29 10:40:53,423 - INFO - 实际视频时长: 3.98秒
2025-07-29 10:40:53,423 - INFO - 时长差异: 0.02秒 (0.63%)
2025-07-29 10:40:53,423 - INFO - ==========================================
2025-07-29 10:40:53,423 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:53,423 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-07-29 10:40:53,424 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0z1k6sc_
2025-07-29 10:40:53,470 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:53,470 - INFO -   - 音频时长: 3.96秒
2025-07-29 10:40:53,470 - INFO -   - 视频时长: 3.98秒
2025-07-29 10:40:53,471 - INFO -   - 时长差异: 0.02秒 (0.63%)
2025-07-29 10:40:53,471 - INFO - 
----- 处理字幕 #29 的方案 #2 -----
2025-07-29 10:40:53,471 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-07-29 10:40:53,471 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_goq96rj
2025-07-29 10:40:53,471 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\243.mp4 (确认存在: True)
2025-07-29 10:40:53,471 - INFO - 添加场景ID=243，时长=1.44秒，累计时长=1.44秒
2025-07-29 10:40:53,471 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\244.mp4 (确认存在: True)
2025-07-29 10:40:53,472 - INFO - 添加场景ID=244，时长=1.16秒，累计时长=2.60秒
2025-07-29 10:40:53,472 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\245.mp4 (确认存在: True)
2025-07-29 10:40:53,472 - INFO - 添加场景ID=245，时长=1.44秒，累计时长=4.04秒
2025-07-29 10:40:53,472 - INFO - 准备合并 3 个场景文件，总时长约 4.04秒
2025-07-29 10:40:53,472 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/243.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/244.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/245.mp4'

2025-07-29 10:40:53,472 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_goq96rj\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_goq96rj\temp_combined.mp4
2025-07-29 10:40:53,625 - INFO - 合并后的视频时长: 4.11秒，目标音频时长: 3.96秒
2025-07-29 10:40:53,625 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_goq96rj\temp_combined.mp4 -ss 0 -to 3.958 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\29_2.mp4
