2025-07-29 10:42:00,369 - INFO - ========== 字幕 #72 处理开始 ==========
2025-07-29 10:42:00,369 - INFO - 字幕内容: 千钧一发，赐婚圣旨到，摄政王“手滑”摔碎证物，将她护在身后。
2025-07-29 10:42:00,369 - INFO - 字幕序号: [1346, 1366]
2025-07-29 10:42:00,369 - INFO - 音频文件详情:
2025-07-29 10:42:00,369 - INFO -   - 路径: output\72.wav
2025-07-29 10:42:00,369 - INFO -   - 时长: 4.78秒
2025-07-29 10:42:00,369 - INFO -   - 验证音频时长: 4.78秒
2025-07-29 10:42:00,369 - INFO - 字幕时间戳信息:
2025-07-29 10:42:00,369 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:42:00,369 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:42:00,369 - INFO -   - 根据生成的音频时长(4.78秒)已调整字幕时间戳
2025-07-29 10:42:00,370 - INFO - ========== 新模式：为字幕 #72 生成4套场景方案 ==========
2025-07-29 10:42:00,370 - INFO - 字幕序号列表: [1346, 1366]
2025-07-29 10:42:00,370 - INFO - 
--- 生成方案 #1：基于字幕序号 #1346 ---
2025-07-29 10:42:00,370 - INFO - 开始为单个字幕序号 #1346 匹配场景，目标时长: 4.78秒
2025-07-29 10:42:00,370 - INFO - 开始查找字幕序号 [1346] 对应的场景，共有 3443 个场景可选
2025-07-29 10:42:00,370 - INFO - 找到related_overlap场景: scene_id=1685, 字幕#1346
2025-07-29 10:42:00,370 - INFO - 找到related_overlap场景: scene_id=1686, 字幕#1346
2025-07-29 10:42:00,371 - INFO - 找到related_between场景: scene_id=1687, 字幕#1346
2025-07-29 10:42:00,371 - INFO - 字幕 #1346 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:42:00,371 - INFO - 字幕序号 #1346 找到 2 个可用overlap场景, 1 个可用between场景
2025-07-29 10:42:00,371 - INFO - 选择第一个overlap场景作为起点: scene_id=1685
2025-07-29 10:42:00,371 - INFO - 添加起点场景: scene_id=1685, 时长=2.76秒, 累计时长=2.76秒
2025-07-29 10:42:00,371 - INFO - 起点场景时长不足，需要延伸填充 2.02秒
2025-07-29 10:42:00,371 - INFO - 起点场景在原始列表中的索引: 1684
2025-07-29 10:42:00,371 - INFO - 延伸添加场景: scene_id=1686 (裁剪至 2.02秒)
2025-07-29 10:42:00,371 - INFO - 累计时长: 4.78秒
2025-07-29 10:42:00,371 - INFO - 字幕序号 #1346 场景匹配完成，共选择 2 个场景，总时长: 4.78秒
2025-07-29 10:42:00,371 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:42:00,371 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:42:00,371 - INFO - 
--- 生成方案 #2：基于字幕序号 #1366 ---
2025-07-29 10:42:00,371 - INFO - 开始为单个字幕序号 #1366 匹配场景，目标时长: 4.78秒
2025-07-29 10:42:00,371 - INFO - 开始查找字幕序号 [1366] 对应的场景，共有 3443 个场景可选
2025-07-29 10:42:00,371 - INFO - 找到related_overlap场景: scene_id=1721, 字幕#1366
2025-07-29 10:42:00,372 - INFO - 找到related_overlap场景: scene_id=1722, 字幕#1366
2025-07-29 10:42:00,372 - INFO - 找到related_between场景: scene_id=1718, 字幕#1366
2025-07-29 10:42:00,372 - INFO - 找到related_between场景: scene_id=1719, 字幕#1366
2025-07-29 10:42:00,372 - INFO - 找到related_between场景: scene_id=1720, 字幕#1366
2025-07-29 10:42:00,372 - INFO - 找到related_between场景: scene_id=1723, 字幕#1366
2025-07-29 10:42:00,372 - INFO - 字幕 #1366 找到 2 个overlap场景, 4 个between场景
2025-07-29 10:42:00,372 - INFO - 字幕序号 #1366 找到 2 个可用overlap场景, 4 个可用between场景
2025-07-29 10:42:00,372 - INFO - 选择第一个overlap场景作为起点: scene_id=1721
2025-07-29 10:42:00,372 - INFO - 添加起点场景: scene_id=1721, 时长=2.12秒, 累计时长=2.12秒
2025-07-29 10:42:00,372 - INFO - 起点场景时长不足，需要延伸填充 2.66秒
2025-07-29 10:42:00,372 - INFO - 起点场景在原始列表中的索引: 1720
2025-07-29 10:42:00,372 - INFO - 延伸添加场景: scene_id=1722 (完整时长 2.00秒)
2025-07-29 10:42:00,372 - INFO - 累计时长: 4.12秒
2025-07-29 10:42:00,373 - INFO - 延伸添加场景: scene_id=1723 (裁剪至 0.66秒)
2025-07-29 10:42:00,373 - INFO - 累计时长: 4.78秒
2025-07-29 10:42:00,373 - INFO - 字幕序号 #1366 场景匹配完成，共选择 3 个场景，总时长: 4.78秒
2025-07-29 10:42:00,373 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:42:00,373 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:42:00,373 - INFO - ========== 当前模式：为字幕 #72 生成 1 套场景方案 ==========
2025-07-29 10:42:00,373 - INFO - 开始查找字幕序号 [1346, 1366] 对应的场景，共有 3443 个场景可选
2025-07-29 10:42:00,373 - INFO - 找到related_overlap场景: scene_id=1685, 字幕#1346
2025-07-29 10:42:00,373 - INFO - 找到related_overlap场景: scene_id=1686, 字幕#1346
2025-07-29 10:42:00,373 - INFO - 找到related_overlap场景: scene_id=1721, 字幕#1366
2025-07-29 10:42:00,373 - INFO - 找到related_overlap场景: scene_id=1722, 字幕#1366
2025-07-29 10:42:00,373 - INFO - 找到related_between场景: scene_id=1687, 字幕#1346
2025-07-29 10:42:00,373 - INFO - 找到related_between场景: scene_id=1718, 字幕#1366
2025-07-29 10:42:00,373 - INFO - 找到related_between场景: scene_id=1719, 字幕#1366
2025-07-29 10:42:00,373 - INFO - 找到related_between场景: scene_id=1720, 字幕#1366
2025-07-29 10:42:00,373 - INFO - 找到related_between场景: scene_id=1723, 字幕#1366
2025-07-29 10:42:00,374 - INFO - 字幕 #1346 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:42:00,374 - INFO - 字幕 #1366 找到 2 个overlap场景, 4 个between场景
2025-07-29 10:42:00,374 - INFO - 共收集 4 个未使用的overlap场景和 5 个未使用的between场景
2025-07-29 10:42:00,374 - INFO - 开始生成方案 #1
2025-07-29 10:42:00,374 - INFO - 方案 #1: 为字幕#1346选择初始化overlap场景id=1685
2025-07-29 10:42:00,374 - INFO - 方案 #1: 为字幕#1366选择初始化overlap场景id=1721
2025-07-29 10:42:00,374 - INFO - 方案 #1: 初始选择后，当前总时长=4.88秒
2025-07-29 10:42:00,374 - INFO - 方案 #1: 额外between选择后，当前总时长=4.88秒
2025-07-29 10:42:00,374 - INFO - 方案 #1: 场景总时长(4.88秒)大于音频时长(4.78秒)，需要裁剪
2025-07-29 10:42:00,374 - INFO - 调整前总时长: 4.88秒, 目标时长: 4.78秒
2025-07-29 10:42:00,374 - INFO - 需要裁剪 0.10秒
2025-07-29 10:42:00,374 - INFO - 裁剪最长场景ID=1685：从2.76秒裁剪至2.66秒
2025-07-29 10:42:00,374 - INFO - 调整后总时长: 4.78秒，与目标时长差异: 0.00秒
2025-07-29 10:42:00,374 - INFO - 方案 #1 调整/填充后最终总时长: 4.78秒
2025-07-29 10:42:00,374 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:42:00,374 - INFO - ========== 当前模式：字幕 #72 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:42:00,374 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:42:00,374 - INFO - ========== 新模式：字幕 #72 共生成 3 套有效场景方案 ==========
2025-07-29 10:42:00,374 - INFO - 
----- 处理字幕 #72 的方案 #1 -----
2025-07-29 10:42:00,374 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-07-29 10:42:00,375 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp95jqkwp5
2025-07-29 10:42:00,375 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1685.mp4 (确认存在: True)
2025-07-29 10:42:00,375 - INFO - 添加场景ID=1685，时长=2.76秒，累计时长=2.76秒
2025-07-29 10:42:00,375 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1686.mp4 (确认存在: True)
2025-07-29 10:42:00,375 - INFO - 添加场景ID=1686，时长=2.08秒，累计时长=4.84秒
2025-07-29 10:42:00,375 - INFO - 准备合并 2 个场景文件，总时长约 4.84秒
2025-07-29 10:42:00,375 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1685.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1686.mp4'

2025-07-29 10:42:00,377 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp95jqkwp5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp95jqkwp5\temp_combined.mp4
2025-07-29 10:42:00,529 - INFO - 合并后的视频时长: 4.89秒，目标音频时长: 4.78秒
2025-07-29 10:42:00,529 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp95jqkwp5\temp_combined.mp4 -ss 0 -to 4.782 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-07-29 10:42:00,892 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:42:00,892 - INFO - 目标音频时长: 4.78秒
2025-07-29 10:42:00,892 - INFO - 实际视频时长: 4.82秒
2025-07-29 10:42:00,892 - INFO - 时长差异: 0.04秒 (0.86%)
2025-07-29 10:42:00,892 - INFO - ==========================================
2025-07-29 10:42:00,892 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:42:00,892 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-07-29 10:42:00,893 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp95jqkwp5
2025-07-29 10:42:00,957 - INFO - 方案 #1 处理完成:
2025-07-29 10:42:00,957 - INFO -   - 音频时长: 4.78秒
2025-07-29 10:42:00,957 - INFO -   - 视频时长: 4.82秒
2025-07-29 10:42:00,957 - INFO -   - 时长差异: 0.04秒 (0.86%)
2025-07-29 10:42:00,957 - INFO - 
----- 处理字幕 #72 的方案 #2 -----
2025-07-29 10:42:00,957 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-07-29 10:42:00,958 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpke8ubb31
2025-07-29 10:42:00,959 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1721.mp4 (确认存在: True)
2025-07-29 10:42:00,959 - INFO - 添加场景ID=1721，时长=2.12秒，累计时长=2.12秒
2025-07-29 10:42:00,959 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1722.mp4 (确认存在: True)
2025-07-29 10:42:00,959 - INFO - 添加场景ID=1722，时长=2.00秒，累计时长=4.12秒
2025-07-29 10:42:00,959 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1723.mp4 (确认存在: True)
2025-07-29 10:42:00,959 - INFO - 添加场景ID=1723，时长=1.56秒，累计时长=5.68秒
2025-07-29 10:42:00,959 - INFO - 准备合并 3 个场景文件，总时长约 5.68秒
2025-07-29 10:42:00,960 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1721.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1722.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1723.mp4'

2025-07-29 10:42:00,960 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpke8ubb31\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpke8ubb31\temp_combined.mp4
2025-07-29 10:42:01,129 - INFO - 合并后的视频时长: 5.75秒，目标音频时长: 4.78秒
2025-07-29 10:42:01,129 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpke8ubb31\temp_combined.mp4 -ss 0 -to 4.782 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-07-29 10:42:01,483 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:42:01,483 - INFO - 目标音频时长: 4.78秒
2025-07-29 10:42:01,483 - INFO - 实际视频时长: 4.82秒
2025-07-29 10:42:01,483 - INFO - 时长差异: 0.04秒 (0.86%)
2025-07-29 10:42:01,483 - INFO - ==========================================
2025-07-29 10:42:01,483 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:42:01,484 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-07-29 10:42:01,484 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpke8ubb31
2025-07-29 10:42:01,547 - INFO - 方案 #2 处理完成:
2025-07-29 10:42:01,547 - INFO -   - 音频时长: 4.78秒
2025-07-29 10:42:01,547 - INFO -   - 视频时长: 4.82秒
2025-07-29 10:42:01,547 - INFO -   - 时长差异: 0.04秒 (0.86%)
2025-07-29 10:42:01,548 - INFO - 
----- 处理字幕 #72 的方案 #3 -----
2025-07-29 10:42:01,548 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\72_3.mp4
2025-07-29 10:42:01,548 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8i_4_d4t
2025-07-29 10:42:01,549 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1685.mp4 (确认存在: True)
2025-07-29 10:42:01,549 - INFO - 添加场景ID=1685，时长=2.76秒，累计时长=2.76秒
2025-07-29 10:42:01,549 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1721.mp4 (确认存在: True)
2025-07-29 10:42:01,549 - INFO - 添加场景ID=1721，时长=2.12秒，累计时长=4.88秒
2025-07-29 10:42:01,549 - INFO - 准备合并 2 个场景文件，总时长约 4.88秒
2025-07-29 10:42:01,549 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1685.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1721.mp4'

2025-07-29 10:42:01,550 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8i_4_d4t\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8i_4_d4t\temp_combined.mp4
2025-07-29 10:42:01,719 - INFO - 合并后的视频时长: 4.93秒，目标音频时长: 4.78秒
2025-07-29 10:42:01,719 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8i_4_d4t\temp_combined.mp4 -ss 0 -to 4.782 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\72_3.mp4
2025-07-29 10:42:02,072 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:42:02,072 - INFO - 目标音频时长: 4.78秒
2025-07-29 10:42:02,072 - INFO - 实际视频时长: 4.82秒
2025-07-29 10:42:02,072 - INFO - 时长差异: 0.04秒 (0.86%)
2025-07-29 10:42:02,072 - INFO - ==========================================
2025-07-29 10:42:02,072 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:42:02,072 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\72_3.mp4
2025-07-29 10:42:02,073 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8i_4_d4t
2025-07-29 10:42:02,134 - INFO - 方案 #3 处理完成:
2025-07-29 10:42:02,134 - INFO -   - 音频时长: 4.78秒
2025-07-29 10:42:02,134 - INFO -   - 视频时长: 4.82秒
2025-07-29 10:42:02,134 - INFO -   - 时长差异: 0.04秒 (0.86%)
2025-07-29 10:42:02,134 - INFO - 
字幕 #72 处理完成，成功生成 3/3 套方案
2025-07-29 10:42:02,134 - INFO - 生成的视频文件:
2025-07-29 10:42:02,134 - INFO -   1. F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-07-29 10:42:02,134 - INFO -   2. F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-07-29 10:42:02,134 - INFO -   3. F:/github/aicut_auto/newcut_ai\72_3.mp4
2025-07-29 10:42:02,134 - INFO - ========== 字幕 #72 处理结束 ==========

