2025-07-29 10:41:16,060 - INFO - ========== 字幕 #46 处理开始 ==========
2025-07-29 10:41:16,060 - INFO - 字幕内容: 他要掌嘴九姨娘，对方却大喊冤枉。
2025-07-29 10:41:16,060 - INFO - 字幕序号: [265, 270]
2025-07-29 10:41:16,061 - INFO - 音频文件详情:
2025-07-29 10:41:16,061 - INFO -   - 路径: output\46.wav
2025-07-29 10:41:16,061 - INFO -   - 时长: 3.19秒
2025-07-29 10:41:16,061 - INFO -   - 验证音频时长: 3.19秒
2025-07-29 10:41:16,061 - INFO - 字幕时间戳信息:
2025-07-29 10:41:16,061 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:16,061 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:16,061 - INFO -   - 根据生成的音频时长(3.19秒)已调整字幕时间戳
2025-07-29 10:41:16,061 - INFO - ========== 新模式：为字幕 #46 生成4套场景方案 ==========
2025-07-29 10:41:16,061 - INFO - 字幕序号列表: [265, 270]
2025-07-29 10:41:16,061 - INFO - 
--- 生成方案 #1：基于字幕序号 #265 ---
2025-07-29 10:41:16,061 - INFO - 开始为单个字幕序号 #265 匹配场景，目标时长: 3.19秒
2025-07-29 10:41:16,061 - INFO - 开始查找字幕序号 [265] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:16,063 - INFO - 找到related_overlap场景: scene_id=398, 字幕#265
2025-07-29 10:41:16,063 - INFO - 找到related_overlap场景: scene_id=399, 字幕#265
2025-07-29 10:41:16,066 - INFO - 字幕 #265 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:16,066 - INFO - 字幕序号 #265 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:16,066 - INFO - 选择第一个overlap场景作为起点: scene_id=398
2025-07-29 10:41:16,066 - INFO - 添加起点场景: scene_id=398, 时长=1.44秒, 累计时长=1.44秒
2025-07-29 10:41:16,066 - INFO - 起点场景时长不足，需要延伸填充 1.75秒
2025-07-29 10:41:16,066 - INFO - 起点场景在原始列表中的索引: 397
2025-07-29 10:41:16,066 - INFO - 延伸添加场景: scene_id=399 (完整时长 0.44秒)
2025-07-29 10:41:16,066 - INFO - 累计时长: 1.88秒
2025-07-29 10:41:16,066 - INFO - 延伸添加场景: scene_id=400 (裁剪至 1.31秒)
2025-07-29 10:41:16,066 - INFO - 累计时长: 3.19秒
2025-07-29 10:41:16,066 - INFO - 字幕序号 #265 场景匹配完成，共选择 3 个场景，总时长: 3.19秒
2025-07-29 10:41:16,066 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:16,066 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:16,066 - INFO - 
--- 生成方案 #2：基于字幕序号 #270 ---
2025-07-29 10:41:16,066 - INFO - 开始为单个字幕序号 #270 匹配场景，目标时长: 3.19秒
2025-07-29 10:41:16,066 - INFO - 开始查找字幕序号 [270] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:16,067 - INFO - 找到related_overlap场景: scene_id=404, 字幕#270
2025-07-29 10:41:16,068 - INFO - 字幕 #270 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:16,068 - INFO - 字幕序号 #270 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:16,068 - INFO - 选择第一个overlap场景作为起点: scene_id=404
2025-07-29 10:41:16,068 - INFO - 添加起点场景: scene_id=404, 时长=1.44秒, 累计时长=1.44秒
2025-07-29 10:41:16,068 - INFO - 起点场景时长不足，需要延伸填充 1.75秒
2025-07-29 10:41:16,068 - INFO - 起点场景在原始列表中的索引: 403
2025-07-29 10:41:16,068 - INFO - 延伸添加场景: scene_id=405 (完整时长 1.36秒)
2025-07-29 10:41:16,068 - INFO - 累计时长: 2.80秒
2025-07-29 10:41:16,068 - INFO - 延伸添加场景: scene_id=406 (裁剪至 0.39秒)
2025-07-29 10:41:16,068 - INFO - 累计时长: 3.19秒
2025-07-29 10:41:16,068 - INFO - 字幕序号 #270 场景匹配完成，共选择 3 个场景，总时长: 3.19秒
2025-07-29 10:41:16,068 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:41:16,068 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:16,068 - INFO - ========== 当前模式：为字幕 #46 生成 1 套场景方案 ==========
2025-07-29 10:41:16,068 - INFO - 开始查找字幕序号 [265, 270] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:16,069 - INFO - 找到related_overlap场景: scene_id=398, 字幕#265
2025-07-29 10:41:16,069 - INFO - 找到related_overlap场景: scene_id=399, 字幕#265
2025-07-29 10:41:16,069 - INFO - 找到related_overlap场景: scene_id=404, 字幕#270
2025-07-29 10:41:16,071 - INFO - 字幕 #265 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:16,071 - INFO - 字幕 #270 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:16,071 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:16,071 - INFO - 开始生成方案 #1
2025-07-29 10:41:16,071 - INFO - 方案 #1: 为字幕#265选择初始化overlap场景id=398
2025-07-29 10:41:16,071 - INFO - 方案 #1: 为字幕#270选择初始化overlap场景id=404
2025-07-29 10:41:16,071 - INFO - 方案 #1: 初始选择后，当前总时长=2.88秒
2025-07-29 10:41:16,071 - INFO - 方案 #1: 额外添加overlap场景id=399, 当前总时长=3.32秒
2025-07-29 10:41:16,071 - INFO - 方案 #1: 额外between选择后，当前总时长=3.32秒
2025-07-29 10:41:16,071 - INFO - 方案 #1: 场景总时长(3.32秒)大于音频时长(3.19秒)，需要裁剪
2025-07-29 10:41:16,071 - INFO - 调整前总时长: 3.32秒, 目标时长: 3.19秒
2025-07-29 10:41:16,072 - INFO - 需要裁剪 0.13秒
2025-07-29 10:41:16,072 - INFO - 裁剪最长场景ID=398：从1.44秒裁剪至1.31秒
2025-07-29 10:41:16,072 - INFO - 调整后总时长: 3.19秒，与目标时长差异: 0.00秒
2025-07-29 10:41:16,072 - INFO - 方案 #1 调整/填充后最终总时长: 3.19秒
2025-07-29 10:41:16,072 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:16,072 - INFO - ========== 当前模式：字幕 #46 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:16,072 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:16,072 - INFO - ========== 新模式：字幕 #46 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:16,072 - INFO - 
----- 处理字幕 #46 的方案 #1 -----
2025-07-29 10:41:16,072 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-29 10:41:16,073 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_zw4fzfr
2025-07-29 10:41:16,073 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\398.mp4 (确认存在: True)
2025-07-29 10:41:16,073 - INFO - 添加场景ID=398，时长=1.44秒，累计时长=1.44秒
2025-07-29 10:41:16,073 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\399.mp4 (确认存在: True)
2025-07-29 10:41:16,073 - INFO - 添加场景ID=399，时长=0.44秒，累计时长=1.88秒
2025-07-29 10:41:16,074 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\400.mp4 (确认存在: True)
2025-07-29 10:41:16,074 - INFO - 添加场景ID=400，时长=2.28秒，累计时长=4.16秒
2025-07-29 10:41:16,074 - INFO - 准备合并 3 个场景文件，总时长约 4.16秒
2025-07-29 10:41:16,074 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/398.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/399.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/400.mp4'

2025-07-29 10:41:16,074 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_zw4fzfr\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_zw4fzfr\temp_combined.mp4
2025-07-29 10:41:16,246 - INFO - 合并后的视频时长: 4.23秒，目标音频时长: 3.19秒
2025-07-29 10:41:16,246 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_zw4fzfr\temp_combined.mp4 -ss 0 -to 3.19 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-29 10:41:16,581 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:16,581 - INFO - 目标音频时长: 3.19秒
2025-07-29 10:41:16,581 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:41:16,581 - INFO - 时长差异: 0.03秒 (1.03%)
2025-07-29 10:41:16,581 - INFO - ==========================================
2025-07-29 10:41:16,581 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:16,581 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-29 10:41:16,582 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_zw4fzfr
2025-07-29 10:41:16,644 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:16,644 - INFO -   - 音频时长: 3.19秒
2025-07-29 10:41:16,644 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:41:16,644 - INFO -   - 时长差异: 0.03秒 (1.03%)
2025-07-29 10:41:16,645 - INFO - 
----- 处理字幕 #46 的方案 #2 -----
2025-07-29 10:41:16,645 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-07-29 10:41:16,645 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmhy1wlxe
2025-07-29 10:41:16,646 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\404.mp4 (确认存在: True)
2025-07-29 10:41:16,646 - INFO - 添加场景ID=404，时长=1.44秒，累计时长=1.44秒
2025-07-29 10:41:16,646 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\405.mp4 (确认存在: True)
2025-07-29 10:41:16,646 - INFO - 添加场景ID=405，时长=1.36秒，累计时长=2.80秒
2025-07-29 10:41:16,646 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\406.mp4 (确认存在: True)
2025-07-29 10:41:16,646 - INFO - 添加场景ID=406，时长=1.44秒，累计时长=4.24秒
2025-07-29 10:41:16,647 - INFO - 准备合并 3 个场景文件，总时长约 4.24秒
2025-07-29 10:41:16,647 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/404.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/405.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/406.mp4'

2025-07-29 10:41:16,647 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmhy1wlxe\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmhy1wlxe\temp_combined.mp4
2025-07-29 10:41:16,825 - INFO - 合并后的视频时长: 4.31秒，目标音频时长: 3.19秒
2025-07-29 10:41:16,825 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmhy1wlxe\temp_combined.mp4 -ss 0 -to 3.19 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-07-29 10:41:17,180 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:17,180 - INFO - 目标音频时长: 3.19秒
2025-07-29 10:41:17,180 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:41:17,180 - INFO - 时长差异: 0.03秒 (1.03%)
2025-07-29 10:41:17,180 - INFO - ==========================================
2025-07-29 10:41:17,180 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:17,180 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-07-29 10:41:17,181 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmhy1wlxe
2025-07-29 10:41:17,232 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:17,232 - INFO -   - 音频时长: 3.19秒
2025-07-29 10:41:17,232 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:41:17,232 - INFO -   - 时长差异: 0.03秒 (1.03%)
2025-07-29 10:41:17,233 - INFO - 
----- 处理字幕 #46 的方案 #3 -----
2025-07-29 10:41:17,233 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-07-29 10:41:17,233 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_87lkxsq
2025-07-29 10:41:17,234 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\398.mp4 (确认存在: True)
2025-07-29 10:41:17,234 - INFO - 添加场景ID=398，时长=1.44秒，累计时长=1.44秒
2025-07-29 10:41:17,234 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\404.mp4 (确认存在: True)
2025-07-29 10:41:17,234 - INFO - 添加场景ID=404，时长=1.44秒，累计时长=2.88秒
2025-07-29 10:41:17,234 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\399.mp4 (确认存在: True)
2025-07-29 10:41:17,234 - INFO - 添加场景ID=399，时长=0.44秒，累计时长=3.32秒
2025-07-29 10:41:17,234 - INFO - 准备合并 3 个场景文件，总时长约 3.32秒
2025-07-29 10:41:17,235 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/398.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/404.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/399.mp4'

2025-07-29 10:41:17,235 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_87lkxsq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_87lkxsq\temp_combined.mp4
2025-07-29 10:41:17,439 - INFO - 合并后的视频时长: 3.39秒，目标音频时长: 3.19秒
2025-07-29 10:41:17,439 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_87lkxsq\temp_combined.mp4 -ss 0 -to 3.19 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-07-29 10:41:17,775 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:17,775 - INFO - 目标音频时长: 3.19秒
2025-07-29 10:41:17,775 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:41:17,775 - INFO - 时长差异: 0.03秒 (1.03%)
2025-07-29 10:41:17,775 - INFO - ==========================================
2025-07-29 10:41:17,775 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:17,775 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-07-29 10:41:17,776 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_87lkxsq
2025-07-29 10:41:17,842 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:17,842 - INFO -   - 音频时长: 3.19秒
2025-07-29 10:41:17,842 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:41:17,842 - INFO -   - 时长差异: 0.03秒 (1.03%)
2025-07-29 10:41:17,843 - INFO - 
字幕 #46 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:17,843 - INFO - 生成的视频文件:
2025-07-29 10:41:17,843 - INFO -   1. F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-29 10:41:17,843 - INFO -   2. F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-07-29 10:41:17,843 - INFO -   3. F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-07-29 10:41:17,843 - INFO - ========== 字幕 #46 处理结束 ==========

