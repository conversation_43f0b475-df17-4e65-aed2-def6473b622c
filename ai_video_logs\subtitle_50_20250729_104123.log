2025-07-29 10:41:23,203 - INFO - ========== 字幕 #50 处理开始 ==========
2025-07-29 10:41:23,203 - INFO - 字幕内容: 哥哥正要处置家贼，九姨娘却称自己怀有身孕。
2025-07-29 10:41:23,203 - INFO - 字幕序号: [286, 290]
2025-07-29 10:41:23,203 - INFO - 音频文件详情:
2025-07-29 10:41:23,203 - INFO -   - 路径: output\50.wav
2025-07-29 10:41:23,203 - INFO -   - 时长: 3.51秒
2025-07-29 10:41:23,204 - INFO -   - 验证音频时长: 3.51秒
2025-07-29 10:41:23,204 - INFO - 字幕时间戳信息:
2025-07-29 10:41:23,204 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:23,204 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:23,204 - INFO -   - 根据生成的音频时长(3.51秒)已调整字幕时间戳
2025-07-29 10:41:23,204 - INFO - ========== 新模式：为字幕 #50 生成4套场景方案 ==========
2025-07-29 10:41:23,204 - INFO - 字幕序号列表: [286, 290]
2025-07-29 10:41:23,204 - INFO - 
--- 生成方案 #1：基于字幕序号 #286 ---
2025-07-29 10:41:23,204 - INFO - 开始为单个字幕序号 #286 匹配场景，目标时长: 3.51秒
2025-07-29 10:41:23,204 - INFO - 开始查找字幕序号 [286] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:23,204 - INFO - 找到related_overlap场景: scene_id=422, 字幕#286
2025-07-29 10:41:23,205 - INFO - 字幕 #286 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:23,205 - INFO - 字幕序号 #286 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:23,205 - INFO - 选择第一个overlap场景作为起点: scene_id=422
2025-07-29 10:41:23,205 - INFO - 添加起点场景: scene_id=422, 时长=1.72秒, 累计时长=1.72秒
2025-07-29 10:41:23,205 - INFO - 起点场景时长不足，需要延伸填充 1.79秒
2025-07-29 10:41:23,205 - INFO - 起点场景在原始列表中的索引: 421
2025-07-29 10:41:23,205 - INFO - 延伸添加场景: scene_id=423 (完整时长 1.40秒)
2025-07-29 10:41:23,205 - INFO - 累计时长: 3.12秒
2025-07-29 10:41:23,205 - INFO - 延伸添加场景: scene_id=424 (裁剪至 0.39秒)
2025-07-29 10:41:23,206 - INFO - 累计时长: 3.51秒
2025-07-29 10:41:23,206 - INFO - 字幕序号 #286 场景匹配完成，共选择 3 个场景，总时长: 3.51秒
2025-07-29 10:41:23,206 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:23,206 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:23,206 - INFO - 
--- 生成方案 #2：基于字幕序号 #290 ---
2025-07-29 10:41:23,206 - INFO - 开始为单个字幕序号 #290 匹配场景，目标时长: 3.51秒
2025-07-29 10:41:23,206 - INFO - 开始查找字幕序号 [290] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:23,206 - INFO - 找到related_overlap场景: scene_id=428, 字幕#290
2025-07-29 10:41:23,206 - INFO - 找到related_between场景: scene_id=430, 字幕#290
2025-07-29 10:41:23,206 - INFO - 找到related_between场景: scene_id=431, 字幕#290
2025-07-29 10:41:23,206 - INFO - 找到related_between场景: scene_id=432, 字幕#290
2025-07-29 10:41:23,206 - INFO - 找到related_between场景: scene_id=433, 字幕#290
2025-07-29 10:41:23,206 - INFO - 字幕 #290 找到 1 个overlap场景, 4 个between场景
2025-07-29 10:41:23,206 - INFO - 字幕序号 #290 找到 1 个可用overlap场景, 4 个可用between场景
2025-07-29 10:41:23,206 - INFO - 选择第一个overlap场景作为起点: scene_id=428
2025-07-29 10:41:23,206 - INFO - 添加起点场景: scene_id=428, 时长=0.64秒, 累计时长=0.64秒
2025-07-29 10:41:23,206 - INFO - 起点场景时长不足，需要延伸填充 2.87秒
2025-07-29 10:41:23,206 - INFO - 起点场景在原始列表中的索引: 427
2025-07-29 10:41:23,206 - INFO - 延伸添加场景: scene_id=429 (完整时长 1.48秒)
2025-07-29 10:41:23,206 - INFO - 累计时长: 2.12秒
2025-07-29 10:41:23,206 - INFO - 延伸添加场景: scene_id=430 (完整时长 1.12秒)
2025-07-29 10:41:23,206 - INFO - 累计时长: 3.24秒
2025-07-29 10:41:23,206 - INFO - 延伸添加场景: scene_id=431 (裁剪至 0.27秒)
2025-07-29 10:41:23,206 - INFO - 累计时长: 3.51秒
2025-07-29 10:41:23,206 - INFO - 字幕序号 #290 场景匹配完成，共选择 4 个场景，总时长: 3.51秒
2025-07-29 10:41:23,206 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 10:41:23,206 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:23,206 - INFO - ========== 当前模式：为字幕 #50 生成 1 套场景方案 ==========
2025-07-29 10:41:23,206 - INFO - 开始查找字幕序号 [286, 290] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:23,207 - INFO - 找到related_overlap场景: scene_id=422, 字幕#286
2025-07-29 10:41:23,207 - INFO - 找到related_overlap场景: scene_id=428, 字幕#290
2025-07-29 10:41:23,207 - INFO - 找到related_between场景: scene_id=430, 字幕#290
2025-07-29 10:41:23,207 - INFO - 找到related_between场景: scene_id=431, 字幕#290
2025-07-29 10:41:23,207 - INFO - 找到related_between场景: scene_id=432, 字幕#290
2025-07-29 10:41:23,207 - INFO - 找到related_between场景: scene_id=433, 字幕#290
2025-07-29 10:41:23,208 - INFO - 字幕 #286 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:23,208 - INFO - 字幕 #290 找到 1 个overlap场景, 4 个between场景
2025-07-29 10:41:23,208 - INFO - 共收集 2 个未使用的overlap场景和 4 个未使用的between场景
2025-07-29 10:41:23,208 - INFO - 开始生成方案 #1
2025-07-29 10:41:23,208 - INFO - 方案 #1: 为字幕#286选择初始化overlap场景id=422
2025-07-29 10:41:23,208 - INFO - 方案 #1: 为字幕#290选择初始化overlap场景id=428
2025-07-29 10:41:23,208 - INFO - 方案 #1: 初始选择后，当前总时长=2.36秒
2025-07-29 10:41:23,208 - INFO - 方案 #1: 额外between选择后，当前总时长=2.36秒
2025-07-29 10:41:23,208 - INFO - 方案 #1: 额外添加between场景id=433, 当前总时长=3.76秒
2025-07-29 10:41:23,208 - INFO - 方案 #1: 场景总时长(3.76秒)大于音频时长(3.51秒)，需要裁剪
2025-07-29 10:41:23,208 - INFO - 调整前总时长: 3.76秒, 目标时长: 3.51秒
2025-07-29 10:41:23,208 - INFO - 需要裁剪 0.25秒
2025-07-29 10:41:23,208 - INFO - 裁剪最长场景ID=422：从1.72秒裁剪至1.47秒
2025-07-29 10:41:23,208 - INFO - 调整后总时长: 3.51秒，与目标时长差异: 0.00秒
2025-07-29 10:41:23,208 - INFO - 方案 #1 调整/填充后最终总时长: 3.51秒
2025-07-29 10:41:23,208 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:23,208 - INFO - ========== 当前模式：字幕 #50 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:23,208 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:23,208 - INFO - ========== 新模式：字幕 #50 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:23,208 - INFO - 
----- 处理字幕 #50 的方案 #1 -----
2025-07-29 10:41:23,208 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-29 10:41:23,208 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpna5zqxgm
2025-07-29 10:41:23,209 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\422.mp4 (确认存在: True)
2025-07-29 10:41:23,209 - INFO - 添加场景ID=422，时长=1.72秒，累计时长=1.72秒
2025-07-29 10:41:23,209 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\423.mp4 (确认存在: True)
2025-07-29 10:41:23,210 - INFO - 添加场景ID=423，时长=1.40秒，累计时长=3.12秒
2025-07-29 10:41:23,210 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\424.mp4 (确认存在: True)
2025-07-29 10:41:23,210 - INFO - 添加场景ID=424，时长=1.08秒，累计时长=4.20秒
2025-07-29 10:41:23,210 - INFO - 准备合并 3 个场景文件，总时长约 4.20秒
2025-07-29 10:41:23,210 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/422.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/423.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/424.mp4'

2025-07-29 10:41:23,211 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpna5zqxgm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpna5zqxgm\temp_combined.mp4
2025-07-29 10:41:23,398 - INFO - 合并后的视频时长: 4.27秒，目标音频时长: 3.51秒
2025-07-29 10:41:23,398 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpna5zqxgm\temp_combined.mp4 -ss 0 -to 3.509 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-29 10:41:23,701 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:23,701 - INFO - 目标音频时长: 3.51秒
2025-07-29 10:41:23,701 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:41:23,701 - INFO - 时长差异: 0.03秒 (0.97%)
2025-07-29 10:41:23,701 - INFO - ==========================================
2025-07-29 10:41:23,701 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:23,701 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-29 10:41:23,703 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpna5zqxgm
2025-07-29 10:41:23,766 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:23,766 - INFO -   - 音频时长: 3.51秒
2025-07-29 10:41:23,766 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:41:23,766 - INFO -   - 时长差异: 0.03秒 (0.97%)
2025-07-29 10:41:23,766 - INFO - 
----- 处理字幕 #50 的方案 #2 -----
2025-07-29 10:41:23,766 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-07-29 10:41:23,766 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp99ehhijq
2025-07-29 10:41:23,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\428.mp4 (确认存在: True)
2025-07-29 10:41:23,767 - INFO - 添加场景ID=428，时长=0.64秒，累计时长=0.64秒
2025-07-29 10:41:23,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\429.mp4 (确认存在: True)
2025-07-29 10:41:23,767 - INFO - 添加场景ID=429，时长=1.48秒，累计时长=2.12秒
2025-07-29 10:41:23,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\430.mp4 (确认存在: True)
2025-07-29 10:41:23,767 - INFO - 添加场景ID=430，时长=1.12秒，累计时长=3.24秒
2025-07-29 10:41:23,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\431.mp4 (确认存在: True)
2025-07-29 10:41:23,767 - INFO - 添加场景ID=431，时长=0.88秒，累计时长=4.12秒
2025-07-29 10:41:23,767 - INFO - 准备合并 4 个场景文件，总时长约 4.12秒
2025-07-29 10:41:23,768 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/428.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/429.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/430.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/431.mp4'

2025-07-29 10:41:23,768 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp99ehhijq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp99ehhijq\temp_combined.mp4
2025-07-29 10:41:23,982 - INFO - 合并后的视频时长: 4.21秒，目标音频时长: 3.51秒
2025-07-29 10:41:23,982 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp99ehhijq\temp_combined.mp4 -ss 0 -to 3.509 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-07-29 10:41:24,306 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:24,306 - INFO - 目标音频时长: 3.51秒
2025-07-29 10:41:24,306 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:41:24,306 - INFO - 时长差异: 0.03秒 (0.97%)
2025-07-29 10:41:24,306 - INFO - ==========================================
2025-07-29 10:41:24,306 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:24,306 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-07-29 10:41:24,306 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp99ehhijq
2025-07-29 10:41:24,360 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:24,360 - INFO -   - 音频时长: 3.51秒
2025-07-29 10:41:24,360 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:41:24,360 - INFO -   - 时长差异: 0.03秒 (0.97%)
2025-07-29 10:41:24,360 - INFO - 
----- 处理字幕 #50 的方案 #3 -----
2025-07-29 10:41:24,360 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-07-29 10:41:24,361 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvehrpk6h
2025-07-29 10:41:24,361 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\422.mp4 (确认存在: True)
2025-07-29 10:41:24,361 - INFO - 添加场景ID=422，时长=1.72秒，累计时长=1.72秒
2025-07-29 10:41:24,361 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\428.mp4 (确认存在: True)
2025-07-29 10:41:24,361 - INFO - 添加场景ID=428，时长=0.64秒，累计时长=2.36秒
2025-07-29 10:41:24,361 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\433.mp4 (确认存在: True)
2025-07-29 10:41:24,361 - INFO - 添加场景ID=433，时长=1.40秒，累计时长=3.76秒
2025-07-29 10:41:24,361 - INFO - 准备合并 3 个场景文件，总时长约 3.76秒
2025-07-29 10:41:24,361 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/422.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/428.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/433.mp4'

2025-07-29 10:41:24,362 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpvehrpk6h\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpvehrpk6h\temp_combined.mp4
2025-07-29 10:41:24,536 - INFO - 合并后的视频时长: 3.83秒，目标音频时长: 3.51秒
2025-07-29 10:41:24,536 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpvehrpk6h\temp_combined.mp4 -ss 0 -to 3.509 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-07-29 10:41:24,892 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:24,893 - INFO - 目标音频时长: 3.51秒
2025-07-29 10:41:24,893 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:41:24,893 - INFO - 时长差异: 0.03秒 (0.97%)
2025-07-29 10:41:24,893 - INFO - ==========================================
2025-07-29 10:41:24,893 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:24,893 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-07-29 10:41:24,894 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvehrpk6h
2025-07-29 10:41:24,958 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:24,958 - INFO -   - 音频时长: 3.51秒
2025-07-29 10:41:24,958 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:41:24,958 - INFO -   - 时长差异: 0.03秒 (0.97%)
2025-07-29 10:41:24,958 - INFO - 
字幕 #50 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:24,958 - INFO - 生成的视频文件:
2025-07-29 10:41:24,958 - INFO -   1. F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-07-29 10:41:24,958 - INFO -   2. F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-07-29 10:41:24,958 - INFO -   3. F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-07-29 10:41:24,959 - INFO - ========== 字幕 #50 处理结束 ==========

