2025-07-29 10:40:26,955 - INFO - ========== 字幕 #9 处理开始 ==========
2025-07-29 10:40:26,955 - INFO - 字幕内容: 祖母催促婚事，她却还为哥哥辩解。
2025-07-29 10:40:26,955 - INFO - 字幕序号: [35, 40]
2025-07-29 10:40:26,955 - INFO - 音频文件详情:
2025-07-29 10:40:26,955 - INFO -   - 路径: output\9.wav
2025-07-29 10:40:26,955 - INFO -   - 时长: 2.62秒
2025-07-29 10:40:26,956 - INFO -   - 验证音频时长: 2.62秒
2025-07-29 10:40:26,956 - INFO - 字幕时间戳信息:
2025-07-29 10:40:26,956 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:26,956 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:26,956 - INFO -   - 根据生成的音频时长(2.62秒)已调整字幕时间戳
2025-07-29 10:40:26,956 - INFO - ========== 新模式：为字幕 #9 生成4套场景方案 ==========
2025-07-29 10:40:26,956 - INFO - 字幕序号列表: [35, 40]
2025-07-29 10:40:26,956 - INFO - 
--- 生成方案 #1：基于字幕序号 #35 ---
2025-07-29 10:40:26,956 - INFO - 开始为单个字幕序号 #35 匹配场景，目标时长: 2.62秒
2025-07-29 10:40:26,956 - INFO - 开始查找字幕序号 [35] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:26,956 - INFO - 找到related_overlap场景: scene_id=52, 字幕#35
2025-07-29 10:40:26,957 - INFO - 字幕 #35 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:26,957 - INFO - 字幕序号 #35 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:26,957 - INFO - 选择第一个overlap场景作为起点: scene_id=52
2025-07-29 10:40:26,957 - INFO - 添加起点场景: scene_id=52, 时长=1.36秒, 累计时长=1.36秒
2025-07-29 10:40:26,957 - INFO - 起点场景时长不足，需要延伸填充 1.27秒
2025-07-29 10:40:26,957 - INFO - 起点场景在原始列表中的索引: 51
2025-07-29 10:40:26,957 - INFO - 延伸添加场景: scene_id=53 (完整时长 1.16秒)
2025-07-29 10:40:26,957 - INFO - 累计时长: 2.52秒
2025-07-29 10:40:26,957 - INFO - 延伸添加场景: scene_id=54 (裁剪至 0.11秒)
2025-07-29 10:40:26,957 - INFO - 累计时长: 2.62秒
2025-07-29 10:40:26,957 - INFO - 字幕序号 #35 场景匹配完成，共选择 3 个场景，总时长: 2.62秒
2025-07-29 10:40:26,957 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:40:26,957 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:40:26,957 - INFO - 
--- 生成方案 #2：基于字幕序号 #40 ---
2025-07-29 10:40:26,957 - INFO - 开始为单个字幕序号 #40 匹配场景，目标时长: 2.62秒
2025-07-29 10:40:26,957 - INFO - 开始查找字幕序号 [40] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:26,957 - INFO - 找到related_overlap场景: scene_id=57, 字幕#40
2025-07-29 10:40:26,959 - INFO - 字幕 #40 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:26,959 - INFO - 字幕序号 #40 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:26,959 - INFO - 选择第一个overlap场景作为起点: scene_id=57
2025-07-29 10:40:26,959 - INFO - 添加起点场景: scene_id=57, 时长=1.76秒, 累计时长=1.76秒
2025-07-29 10:40:26,959 - INFO - 起点场景时长不足，需要延伸填充 0.86秒
2025-07-29 10:40:26,959 - INFO - 起点场景在原始列表中的索引: 56
2025-07-29 10:40:26,959 - INFO - 延伸添加场景: scene_id=58 (完整时长 0.84秒)
2025-07-29 10:40:26,959 - INFO - 累计时长: 2.60秒
2025-07-29 10:40:26,959 - INFO - 延伸添加场景: scene_id=59 (裁剪至 0.02秒)
2025-07-29 10:40:26,959 - INFO - 累计时长: 2.62秒
2025-07-29 10:40:26,959 - INFO - 字幕序号 #40 场景匹配完成，共选择 3 个场景，总时长: 2.62秒
2025-07-29 10:40:26,959 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:26,959 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:26,959 - INFO - ========== 当前模式：为字幕 #9 生成 1 套场景方案 ==========
2025-07-29 10:40:26,959 - INFO - 开始查找字幕序号 [35, 40] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:26,959 - INFO - 找到related_overlap场景: scene_id=52, 字幕#35
2025-07-29 10:40:26,959 - INFO - 找到related_overlap场景: scene_id=57, 字幕#40
2025-07-29 10:40:26,960 - INFO - 字幕 #35 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:26,960 - INFO - 字幕 #40 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:26,960 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:40:26,960 - INFO - 开始生成方案 #1
2025-07-29 10:40:26,961 - INFO - 方案 #1: 为字幕#35选择初始化overlap场景id=52
2025-07-29 10:40:26,961 - INFO - 方案 #1: 为字幕#40选择初始化overlap场景id=57
2025-07-29 10:40:26,961 - INFO - 方案 #1: 初始选择后，当前总时长=3.12秒
2025-07-29 10:40:26,961 - INFO - 方案 #1: 额外between选择后，当前总时长=3.12秒
2025-07-29 10:40:26,961 - INFO - 方案 #1: 场景总时长(3.12秒)大于音频时长(2.62秒)，需要裁剪
2025-07-29 10:40:26,961 - INFO - 调整前总时长: 3.12秒, 目标时长: 2.62秒
2025-07-29 10:40:26,961 - INFO - 需要裁剪 0.49秒
2025-07-29 10:40:26,961 - INFO - 裁剪最长场景ID=57：从1.76秒裁剪至1.27秒
2025-07-29 10:40:26,961 - INFO - 调整后总时长: 2.62秒，与目标时长差异: 0.00秒
2025-07-29 10:40:26,961 - INFO - 方案 #1 调整/填充后最终总时长: 2.62秒
2025-07-29 10:40:26,961 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:26,961 - INFO - ========== 当前模式：字幕 #9 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:26,961 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:26,961 - INFO - ========== 新模式：字幕 #9 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:26,961 - INFO - 
----- 处理字幕 #9 的方案 #1 -----
2025-07-29 10:40:26,961 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-29 10:40:26,961 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpahwk7ova
2025-07-29 10:40:26,962 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\52.mp4 (确认存在: True)
2025-07-29 10:40:26,962 - INFO - 添加场景ID=52，时长=1.36秒，累计时长=1.36秒
2025-07-29 10:40:26,962 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\53.mp4 (确认存在: True)
2025-07-29 10:40:26,962 - INFO - 添加场景ID=53，时长=1.16秒，累计时长=2.52秒
2025-07-29 10:40:26,962 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\54.mp4 (确认存在: True)
2025-07-29 10:40:26,962 - INFO - 添加场景ID=54，时长=1.52秒，累计时长=4.04秒
2025-07-29 10:40:26,962 - INFO - 场景总时长(4.04秒)已达到音频时长(2.62秒)的1.5倍，停止添加场景
2025-07-29 10:40:26,962 - INFO - 准备合并 3 个场景文件，总时长约 4.04秒
2025-07-29 10:40:26,962 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/52.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/53.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/54.mp4'

2025-07-29 10:40:26,962 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpahwk7ova\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpahwk7ova\temp_combined.mp4
2025-07-29 10:40:27,105 - INFO - 合并后的视频时长: 4.11秒，目标音频时长: 2.62秒
2025-07-29 10:40:27,105 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpahwk7ova\temp_combined.mp4 -ss 0 -to 2.625 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-29 10:40:27,341 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:27,341 - INFO - 目标音频时长: 2.62秒
2025-07-29 10:40:27,341 - INFO - 实际视频时长: 2.66秒
2025-07-29 10:40:27,341 - INFO - 时长差异: 0.04秒 (1.45%)
2025-07-29 10:40:27,341 - INFO - ==========================================
2025-07-29 10:40:27,341 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:27,341 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-29 10:40:27,342 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpahwk7ova
2025-07-29 10:40:27,386 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:27,386 - INFO -   - 音频时长: 2.62秒
2025-07-29 10:40:27,386 - INFO -   - 视频时长: 2.66秒
2025-07-29 10:40:27,386 - INFO -   - 时长差异: 0.04秒 (1.45%)
2025-07-29 10:40:27,386 - INFO - 
----- 处理字幕 #9 的方案 #2 -----
2025-07-29 10:40:27,386 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-29 10:40:27,386 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsqv5tvoh
2025-07-29 10:40:27,387 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\57.mp4 (确认存在: True)
2025-07-29 10:40:27,387 - INFO - 添加场景ID=57，时长=1.76秒，累计时长=1.76秒
2025-07-29 10:40:27,387 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\58.mp4 (确认存在: True)
2025-07-29 10:40:27,387 - INFO - 添加场景ID=58，时长=0.84秒，累计时长=2.60秒
2025-07-29 10:40:27,387 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\59.mp4 (确认存在: True)
2025-07-29 10:40:27,387 - INFO - 添加场景ID=59，时长=1.72秒，累计时长=4.32秒
2025-07-29 10:40:27,387 - INFO - 场景总时长(4.32秒)已达到音频时长(2.62秒)的1.5倍，停止添加场景
2025-07-29 10:40:27,387 - INFO - 准备合并 3 个场景文件，总时长约 4.32秒
2025-07-29 10:40:27,387 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/57.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/58.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/59.mp4'

2025-07-29 10:40:27,388 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsqv5tvoh\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsqv5tvoh\temp_combined.mp4
2025-07-29 10:40:27,527 - INFO - 合并后的视频时长: 4.39秒，目标音频时长: 2.62秒
2025-07-29 10:40:27,527 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsqv5tvoh\temp_combined.mp4 -ss 0 -to 2.625 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-29 10:40:27,772 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:27,772 - INFO - 目标音频时长: 2.62秒
2025-07-29 10:40:27,772 - INFO - 实际视频时长: 2.66秒
2025-07-29 10:40:27,772 - INFO - 时长差异: 0.04秒 (1.45%)
2025-07-29 10:40:27,772 - INFO - ==========================================
2025-07-29 10:40:27,772 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:27,772 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-29 10:40:27,773 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsqv5tvoh
2025-07-29 10:40:27,815 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:27,815 - INFO -   - 音频时长: 2.62秒
2025-07-29 10:40:27,815 - INFO -   - 视频时长: 2.66秒
2025-07-29 10:40:27,815 - INFO -   - 时长差异: 0.04秒 (1.45%)
2025-07-29 10:40:27,815 - INFO - 
----- 处理字幕 #9 的方案 #3 -----
2025-07-29 10:40:27,815 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-07-29 10:40:27,816 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0fcz2796
2025-07-29 10:40:27,816 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\52.mp4 (确认存在: True)
2025-07-29 10:40:27,816 - INFO - 添加场景ID=52，时长=1.36秒，累计时长=1.36秒
2025-07-29 10:40:27,816 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\57.mp4 (确认存在: True)
2025-07-29 10:40:27,816 - INFO - 添加场景ID=57，时长=1.76秒，累计时长=3.12秒
2025-07-29 10:40:27,816 - INFO - 准备合并 2 个场景文件，总时长约 3.12秒
2025-07-29 10:40:27,816 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/52.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/57.mp4'

2025-07-29 10:40:27,817 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0fcz2796\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0fcz2796\temp_combined.mp4
2025-07-29 10:40:27,929 - INFO - 合并后的视频时长: 3.17秒，目标音频时长: 2.62秒
2025-07-29 10:40:27,929 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0fcz2796\temp_combined.mp4 -ss 0 -to 2.625 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-07-29 10:40:28,151 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:28,151 - INFO - 目标音频时长: 2.62秒
2025-07-29 10:40:28,151 - INFO - 实际视频时长: 2.66秒
2025-07-29 10:40:28,151 - INFO - 时长差异: 0.04秒 (1.45%)
2025-07-29 10:40:28,151 - INFO - ==========================================
2025-07-29 10:40:28,151 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:28,151 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-07-29 10:40:28,152 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0fcz2796
2025-07-29 10:40:28,201 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:28,201 - INFO -   - 音频时长: 2.62秒
2025-07-29 10:40:28,201 - INFO -   - 视频时长: 2.66秒
2025-07-29 10:40:28,201 - INFO -   - 时长差异: 0.04秒 (1.45%)
2025-07-29 10:40:28,201 - INFO - 
字幕 #9 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:28,201 - INFO - 生成的视频文件:
2025-07-29 10:40:28,201 - INFO -   1. F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-29 10:40:28,201 - INFO -   2. F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-29 10:40:28,201 - INFO -   3. F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-07-29 10:40:28,201 - INFO - ========== 字幕 #9 处理结束 ==========

