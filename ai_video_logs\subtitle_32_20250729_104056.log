2025-07-29 10:40:56,781 - INFO - ========== 字幕 #32 处理开始 ==========
2025-07-29 10:40:56,781 - INFO - 字幕内容: 她在酒楼独饮，被刘青书盯上，送上有问题的酒。
2025-07-29 10:40:56,781 - INFO - 字幕序号: [177, 185]
2025-07-29 10:40:56,781 - INFO - 音频文件详情:
2025-07-29 10:40:56,781 - INFO -   - 路径: output\32.wav
2025-07-29 10:40:56,781 - INFO -   - 时长: 3.89秒
2025-07-29 10:40:56,781 - INFO -   - 验证音频时长: 3.89秒
2025-07-29 10:40:56,781 - INFO - 字幕时间戳信息:
2025-07-29 10:40:56,781 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:56,782 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:56,782 - INFO -   - 根据生成的音频时长(3.89秒)已调整字幕时间戳
2025-07-29 10:40:56,782 - INFO - ========== 新模式：为字幕 #32 生成4套场景方案 ==========
2025-07-29 10:40:56,782 - INFO - 字幕序号列表: [177, 185]
2025-07-29 10:40:56,782 - INFO - 
--- 生成方案 #1：基于字幕序号 #177 ---
2025-07-29 10:40:56,782 - INFO - 开始为单个字幕序号 #177 匹配场景，目标时长: 3.89秒
2025-07-29 10:40:56,782 - INFO - 开始查找字幕序号 [177] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:56,782 - INFO - 找到related_overlap场景: scene_id=254, 字幕#177
2025-07-29 10:40:56,782 - INFO - 找到related_between场景: scene_id=252, 字幕#177
2025-07-29 10:40:56,782 - INFO - 找到related_between场景: scene_id=253, 字幕#177
2025-07-29 10:40:56,783 - INFO - 字幕 #177 找到 1 个overlap场景, 2 个between场景
2025-07-29 10:40:56,783 - INFO - 字幕序号 #177 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 10:40:56,783 - INFO - 选择第一个overlap场景作为起点: scene_id=254
2025-07-29 10:40:56,783 - INFO - 添加起点场景: scene_id=254, 时长=3.48秒, 累计时长=3.48秒
2025-07-29 10:40:56,783 - INFO - 起点场景时长不足，需要延伸填充 0.41秒
2025-07-29 10:40:56,783 - INFO - 起点场景在原始列表中的索引: 253
2025-07-29 10:40:56,783 - INFO - 延伸添加场景: scene_id=255 (裁剪至 0.41秒)
2025-07-29 10:40:56,783 - INFO - 累计时长: 3.89秒
2025-07-29 10:40:56,783 - INFO - 字幕序号 #177 场景匹配完成，共选择 2 个场景，总时长: 3.89秒
2025-07-29 10:40:56,783 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:56,783 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:56,784 - INFO - 
--- 生成方案 #2：基于字幕序号 #185 ---
2025-07-29 10:40:56,784 - INFO - 开始为单个字幕序号 #185 匹配场景，目标时长: 3.89秒
2025-07-29 10:40:56,784 - INFO - 开始查找字幕序号 [185] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:56,784 - INFO - 找到related_overlap场景: scene_id=259, 字幕#185
2025-07-29 10:40:56,784 - INFO - 找到related_overlap场景: scene_id=260, 字幕#185
2025-07-29 10:40:56,785 - INFO - 找到related_between场景: scene_id=261, 字幕#185
2025-07-29 10:40:56,785 - INFO - 找到related_between场景: scene_id=262, 字幕#185
2025-07-29 10:40:56,785 - INFO - 找到related_between场景: scene_id=263, 字幕#185
2025-07-29 10:40:56,785 - INFO - 字幕 #185 找到 2 个overlap场景, 3 个between场景
2025-07-29 10:40:56,785 - INFO - 字幕序号 #185 找到 2 个可用overlap场景, 3 个可用between场景
2025-07-29 10:40:56,785 - INFO - 选择第一个overlap场景作为起点: scene_id=259
2025-07-29 10:40:56,785 - INFO - 添加起点场景: scene_id=259, 时长=1.64秒, 累计时长=1.64秒
2025-07-29 10:40:56,785 - INFO - 起点场景时长不足，需要延伸填充 2.25秒
2025-07-29 10:40:56,785 - INFO - 起点场景在原始列表中的索引: 258
2025-07-29 10:40:56,785 - INFO - 延伸添加场景: scene_id=260 (完整时长 1.24秒)
2025-07-29 10:40:56,785 - INFO - 累计时长: 2.88秒
2025-07-29 10:40:56,785 - INFO - 延伸添加场景: scene_id=261 (裁剪至 1.01秒)
2025-07-29 10:40:56,785 - INFO - 累计时长: 3.89秒
2025-07-29 10:40:56,786 - INFO - 字幕序号 #185 场景匹配完成，共选择 3 个场景，总时长: 3.89秒
2025-07-29 10:40:56,786 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:56,786 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:56,786 - INFO - ========== 当前模式：为字幕 #32 生成 1 套场景方案 ==========
2025-07-29 10:40:56,786 - INFO - 开始查找字幕序号 [177, 185] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:56,786 - INFO - 找到related_overlap场景: scene_id=254, 字幕#177
2025-07-29 10:40:56,786 - INFO - 找到related_overlap场景: scene_id=259, 字幕#185
2025-07-29 10:40:56,786 - INFO - 找到related_overlap场景: scene_id=260, 字幕#185
2025-07-29 10:40:56,787 - INFO - 找到related_between场景: scene_id=252, 字幕#177
2025-07-29 10:40:56,787 - INFO - 找到related_between场景: scene_id=253, 字幕#177
2025-07-29 10:40:56,787 - INFO - 找到related_between场景: scene_id=261, 字幕#185
2025-07-29 10:40:56,787 - INFO - 找到related_between场景: scene_id=262, 字幕#185
2025-07-29 10:40:56,787 - INFO - 找到related_between场景: scene_id=263, 字幕#185
2025-07-29 10:40:56,787 - INFO - 字幕 #177 找到 1 个overlap场景, 2 个between场景
2025-07-29 10:40:56,787 - INFO - 字幕 #185 找到 2 个overlap场景, 3 个between场景
2025-07-29 10:40:56,787 - INFO - 共收集 3 个未使用的overlap场景和 5 个未使用的between场景
2025-07-29 10:40:56,787 - INFO - 开始生成方案 #1
2025-07-29 10:40:56,787 - INFO - 方案 #1: 为字幕#177选择初始化overlap场景id=254
2025-07-29 10:40:56,787 - INFO - 方案 #1: 为字幕#185选择初始化overlap场景id=259
2025-07-29 10:40:56,787 - INFO - 方案 #1: 初始选择后，当前总时长=5.12秒
2025-07-29 10:40:56,787 - INFO - 方案 #1: 额外between选择后，当前总时长=5.12秒
2025-07-29 10:40:56,788 - INFO - 方案 #1: 场景总时长(5.12秒)大于音频时长(3.89秒)，需要裁剪
2025-07-29 10:40:56,788 - INFO - 调整前总时长: 5.12秒, 目标时长: 3.89秒
2025-07-29 10:40:56,788 - INFO - 需要裁剪 1.23秒
2025-07-29 10:40:56,788 - INFO - 裁剪最长场景ID=254：从3.48秒裁剪至2.25秒
2025-07-29 10:40:56,788 - INFO - 调整后总时长: 3.89秒，与目标时长差异: 0.00秒
2025-07-29 10:40:56,788 - INFO - 方案 #1 调整/填充后最终总时长: 3.89秒
2025-07-29 10:40:56,788 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:56,788 - INFO - ========== 当前模式：字幕 #32 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:56,788 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:56,788 - INFO - ========== 新模式：字幕 #32 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:56,788 - INFO - 
----- 处理字幕 #32 的方案 #1 -----
2025-07-29 10:40:56,788 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-29 10:40:56,789 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1y1j1jpg
2025-07-29 10:40:56,789 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\254.mp4 (确认存在: True)
2025-07-29 10:40:56,789 - INFO - 添加场景ID=254，时长=3.48秒，累计时长=3.48秒
2025-07-29 10:40:56,789 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\255.mp4 (确认存在: True)
2025-07-29 10:40:56,789 - INFO - 添加场景ID=255，时长=1.68秒，累计时长=5.16秒
2025-07-29 10:40:56,789 - INFO - 准备合并 2 个场景文件，总时长约 5.16秒
2025-07-29 10:40:56,789 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/254.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/255.mp4'

2025-07-29 10:40:56,790 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1y1j1jpg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1y1j1jpg\temp_combined.mp4
2025-07-29 10:40:56,904 - INFO - 合并后的视频时长: 5.21秒，目标音频时长: 3.89秒
2025-07-29 10:40:56,904 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1y1j1jpg\temp_combined.mp4 -ss 0 -to 3.89 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-29 10:40:57,185 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:57,185 - INFO - 目标音频时长: 3.89秒
2025-07-29 10:40:57,185 - INFO - 实际视频时长: 3.94秒
2025-07-29 10:40:57,185 - INFO - 时长差异: 0.05秒 (1.36%)
2025-07-29 10:40:57,185 - INFO - ==========================================
2025-07-29 10:40:57,185 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:57,185 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-29 10:40:57,186 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1y1j1jpg
2025-07-29 10:40:57,229 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:57,229 - INFO -   - 音频时长: 3.89秒
2025-07-29 10:40:57,229 - INFO -   - 视频时长: 3.94秒
2025-07-29 10:40:57,229 - INFO -   - 时长差异: 0.05秒 (1.36%)
2025-07-29 10:40:57,229 - INFO - 
----- 处理字幕 #32 的方案 #2 -----
2025-07-29 10:40:57,229 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-29 10:40:57,229 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpane6frm7
2025-07-29 10:40:57,230 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\259.mp4 (确认存在: True)
2025-07-29 10:40:57,230 - INFO - 添加场景ID=259，时长=1.64秒，累计时长=1.64秒
2025-07-29 10:40:57,230 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\260.mp4 (确认存在: True)
2025-07-29 10:40:57,230 - INFO - 添加场景ID=260，时长=1.24秒，累计时长=2.88秒
2025-07-29 10:40:57,230 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\261.mp4 (确认存在: True)
2025-07-29 10:40:57,230 - INFO - 添加场景ID=261，时长=1.24秒，累计时长=4.12秒
2025-07-29 10:40:57,230 - INFO - 准备合并 3 个场景文件，总时长约 4.12秒
2025-07-29 10:40:57,230 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/259.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/260.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/261.mp4'

2025-07-29 10:40:57,231 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpane6frm7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpane6frm7\temp_combined.mp4
2025-07-29 10:40:57,380 - INFO - 合并后的视频时长: 4.19秒，目标音频时长: 3.89秒
2025-07-29 10:40:57,381 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpane6frm7\temp_combined.mp4 -ss 0 -to 3.89 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-29 10:40:57,647 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:57,647 - INFO - 目标音频时长: 3.89秒
2025-07-29 10:40:57,647 - INFO - 实际视频时长: 3.94秒
2025-07-29 10:40:57,647 - INFO - 时长差异: 0.05秒 (1.36%)
2025-07-29 10:40:57,647 - INFO - ==========================================
2025-07-29 10:40:57,647 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:57,647 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-29 10:40:57,648 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpane6frm7
2025-07-29 10:40:57,693 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:57,693 - INFO -   - 音频时长: 3.89秒
2025-07-29 10:40:57,693 - INFO -   - 视频时长: 3.94秒
2025-07-29 10:40:57,693 - INFO -   - 时长差异: 0.05秒 (1.36%)
2025-07-29 10:40:57,693 - INFO - 
----- 处理字幕 #32 的方案 #3 -----
2025-07-29 10:40:57,693 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-07-29 10:40:57,693 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3pjwak4s
2025-07-29 10:40:57,694 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\254.mp4 (确认存在: True)
2025-07-29 10:40:57,694 - INFO - 添加场景ID=254，时长=3.48秒，累计时长=3.48秒
2025-07-29 10:40:57,694 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\259.mp4 (确认存在: True)
2025-07-29 10:40:57,694 - INFO - 添加场景ID=259，时长=1.64秒，累计时长=5.12秒
2025-07-29 10:40:57,694 - INFO - 准备合并 2 个场景文件，总时长约 5.12秒
2025-07-29 10:40:57,694 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/254.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/259.mp4'

2025-07-29 10:40:57,694 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3pjwak4s\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3pjwak4s\temp_combined.mp4
2025-07-29 10:40:57,812 - INFO - 合并后的视频时长: 5.17秒，目标音频时长: 3.89秒
2025-07-29 10:40:57,813 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3pjwak4s\temp_combined.mp4 -ss 0 -to 3.89 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-07-29 10:40:58,083 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:58,083 - INFO - 目标音频时长: 3.89秒
2025-07-29 10:40:58,083 - INFO - 实际视频时长: 3.94秒
2025-07-29 10:40:58,083 - INFO - 时长差异: 0.05秒 (1.36%)
2025-07-29 10:40:58,083 - INFO - ==========================================
2025-07-29 10:40:58,083 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:58,083 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-07-29 10:40:58,083 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3pjwak4s
2025-07-29 10:40:58,126 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:58,126 - INFO -   - 音频时长: 3.89秒
2025-07-29 10:40:58,126 - INFO -   - 视频时长: 3.94秒
2025-07-29 10:40:58,126 - INFO -   - 时长差异: 0.05秒 (1.36%)
2025-07-29 10:40:58,126 - INFO - 
字幕 #32 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:58,126 - INFO - 生成的视频文件:
2025-07-29 10:40:58,126 - INFO -   1. F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-29 10:40:58,126 - INFO -   2. F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-07-29 10:40:58,126 - INFO -   3. F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-07-29 10:40:58,126 - INFO - ========== 字幕 #32 处理结束 ==========

