2025-07-29 10:40:45,171 - INFO - ========== 字幕 #23 处理开始 ==========
2025-07-29 10:40:45,171 - INFO - 字幕内容: 她喃喃自语，等来的却不是哥哥，而是摄政王。
2025-07-29 10:40:45,171 - INFO - 字幕序号: [121, 127]
2025-07-29 10:40:45,171 - INFO - 音频文件详情:
2025-07-29 10:40:45,171 - INFO -   - 路径: output\23.wav
2025-07-29 10:40:45,171 - INFO -   - 时长: 3.17秒
2025-07-29 10:40:45,172 - INFO -   - 验证音频时长: 3.17秒
2025-07-29 10:40:45,172 - INFO - 字幕时间戳信息:
2025-07-29 10:40:45,172 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:45,172 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:45,172 - INFO -   - 根据生成的音频时长(3.17秒)已调整字幕时间戳
2025-07-29 10:40:45,172 - INFO - ========== 新模式：为字幕 #23 生成4套场景方案 ==========
2025-07-29 10:40:45,172 - INFO - 字幕序号列表: [121, 127]
2025-07-29 10:40:45,172 - INFO - 
--- 生成方案 #1：基于字幕序号 #121 ---
2025-07-29 10:40:45,172 - INFO - 开始为单个字幕序号 #121 匹配场景，目标时长: 3.17秒
2025-07-29 10:40:45,172 - INFO - 开始查找字幕序号 [121] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:45,172 - INFO - 找到related_overlap场景: scene_id=176, 字幕#121
2025-07-29 10:40:45,172 - INFO - 找到related_overlap场景: scene_id=177, 字幕#121
2025-07-29 10:40:45,173 - INFO - 找到related_between场景: scene_id=174, 字幕#121
2025-07-29 10:40:45,173 - INFO - 找到related_between场景: scene_id=175, 字幕#121
2025-07-29 10:40:45,173 - INFO - 字幕 #121 找到 2 个overlap场景, 2 个between场景
2025-07-29 10:40:45,173 - INFO - 字幕序号 #121 找到 2 个可用overlap场景, 2 个可用between场景
2025-07-29 10:40:45,173 - INFO - 选择第一个overlap场景作为起点: scene_id=176
2025-07-29 10:40:45,173 - INFO - 添加起点场景: scene_id=176, 时长=1.08秒, 累计时长=1.08秒
2025-07-29 10:40:45,173 - INFO - 起点场景时长不足，需要延伸填充 2.09秒
2025-07-29 10:40:45,173 - INFO - 起点场景在原始列表中的索引: 175
2025-07-29 10:40:45,174 - INFO - 延伸添加场景: scene_id=177 (裁剪至 2.09秒)
2025-07-29 10:40:45,174 - INFO - 累计时长: 3.17秒
2025-07-29 10:40:45,174 - INFO - 字幕序号 #121 场景匹配完成，共选择 2 个场景，总时长: 3.17秒
2025-07-29 10:40:45,174 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:45,174 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:45,174 - INFO - 
--- 生成方案 #2：基于字幕序号 #127 ---
2025-07-29 10:40:45,174 - INFO - 开始为单个字幕序号 #127 匹配场景，目标时长: 3.17秒
2025-07-29 10:40:45,174 - INFO - 开始查找字幕序号 [127] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:45,174 - INFO - 找到related_overlap场景: scene_id=181, 字幕#127
2025-07-29 10:40:45,174 - INFO - 找到related_between场景: scene_id=182, 字幕#127
2025-07-29 10:40:45,174 - INFO - 找到related_between场景: scene_id=183, 字幕#127
2025-07-29 10:40:45,174 - INFO - 找到related_between场景: scene_id=184, 字幕#127
2025-07-29 10:40:45,175 - INFO - 字幕 #127 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:40:45,175 - INFO - 字幕序号 #127 找到 1 个可用overlap场景, 3 个可用between场景
2025-07-29 10:40:45,175 - INFO - 选择第一个overlap场景作为起点: scene_id=181
2025-07-29 10:40:45,175 - INFO - 添加起点场景: scene_id=181, 时长=1.48秒, 累计时长=1.48秒
2025-07-29 10:40:45,175 - INFO - 起点场景时长不足，需要延伸填充 1.69秒
2025-07-29 10:40:45,175 - INFO - 起点场景在原始列表中的索引: 180
2025-07-29 10:40:45,175 - INFO - 延伸添加场景: scene_id=182 (完整时长 1.36秒)
2025-07-29 10:40:45,175 - INFO - 累计时长: 2.84秒
2025-07-29 10:40:45,175 - INFO - 延伸添加场景: scene_id=183 (裁剪至 0.33秒)
2025-07-29 10:40:45,175 - INFO - 累计时长: 3.17秒
2025-07-29 10:40:45,175 - INFO - 字幕序号 #127 场景匹配完成，共选择 3 个场景，总时长: 3.17秒
2025-07-29 10:40:45,175 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:45,175 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:45,175 - INFO - ========== 当前模式：为字幕 #23 生成 1 套场景方案 ==========
2025-07-29 10:40:45,175 - INFO - 开始查找字幕序号 [121, 127] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:45,175 - INFO - 找到related_overlap场景: scene_id=176, 字幕#121
2025-07-29 10:40:45,175 - INFO - 找到related_overlap场景: scene_id=177, 字幕#121
2025-07-29 10:40:45,175 - INFO - 找到related_overlap场景: scene_id=181, 字幕#127
2025-07-29 10:40:45,176 - INFO - 找到related_between场景: scene_id=174, 字幕#121
2025-07-29 10:40:45,176 - INFO - 找到related_between场景: scene_id=175, 字幕#121
2025-07-29 10:40:45,176 - INFO - 找到related_between场景: scene_id=182, 字幕#127
2025-07-29 10:40:45,176 - INFO - 找到related_between场景: scene_id=183, 字幕#127
2025-07-29 10:40:45,176 - INFO - 找到related_between场景: scene_id=184, 字幕#127
2025-07-29 10:40:45,176 - INFO - 字幕 #121 找到 2 个overlap场景, 2 个between场景
2025-07-29 10:40:45,176 - INFO - 字幕 #127 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:40:45,176 - INFO - 共收集 3 个未使用的overlap场景和 5 个未使用的between场景
2025-07-29 10:40:45,176 - INFO - 开始生成方案 #1
2025-07-29 10:40:45,176 - INFO - 方案 #1: 为字幕#121选择初始化overlap场景id=176
2025-07-29 10:40:45,176 - INFO - 方案 #1: 为字幕#127选择初始化overlap场景id=181
2025-07-29 10:40:45,176 - INFO - 方案 #1: 初始选择后，当前总时长=2.56秒
2025-07-29 10:40:45,176 - INFO - 方案 #1: 额外添加overlap场景id=177, 当前总时长=9.56秒
2025-07-29 10:40:45,176 - INFO - 方案 #1: 额外between选择后，当前总时长=9.56秒
2025-07-29 10:40:45,176 - INFO - 方案 #1: 场景总时长(9.56秒)大于音频时长(3.17秒)，需要裁剪
2025-07-29 10:40:45,176 - INFO - 调整前总时长: 9.56秒, 目标时长: 3.17秒
2025-07-29 10:40:45,176 - INFO - 需要裁剪 6.39秒
2025-07-29 10:40:45,176 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:40:45,176 - INFO - 裁剪场景ID=177：从7.00秒裁剪至2.10秒
2025-07-29 10:40:45,176 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 1.49秒
2025-07-29 10:40:45,176 - INFO - 移除场景ID=176，时长=1.08秒
2025-07-29 10:40:45,176 - INFO - 移除场景ID=181，时长=1.48秒
2025-07-29 10:40:45,176 - INFO - 调整后总时长: 2.10秒，与目标时长差异: 1.07秒
2025-07-29 10:40:45,176 - INFO - 方案 #1 调整/填充后最终总时长: 2.10秒
2025-07-29 10:40:45,176 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:45,176 - INFO - ========== 当前模式：字幕 #23 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:45,176 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:45,176 - INFO - ========== 新模式：字幕 #23 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:45,176 - INFO - 
----- 处理字幕 #23 的方案 #1 -----
2025-07-29 10:40:45,177 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-29 10:40:45,177 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuqeo9xdh
2025-07-29 10:40:45,177 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\176.mp4 (确认存在: True)
2025-07-29 10:40:45,177 - INFO - 添加场景ID=176，时长=1.08秒，累计时长=1.08秒
2025-07-29 10:40:45,178 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\177.mp4 (确认存在: True)
2025-07-29 10:40:45,178 - INFO - 添加场景ID=177，时长=7.00秒，累计时长=8.08秒
2025-07-29 10:40:45,178 - INFO - 场景总时长(8.08秒)已达到音频时长(3.17秒)的1.5倍，停止添加场景
2025-07-29 10:40:45,178 - INFO - 准备合并 2 个场景文件，总时长约 8.08秒
2025-07-29 10:40:45,178 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/176.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/177.mp4'

2025-07-29 10:40:45,178 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuqeo9xdh\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuqeo9xdh\temp_combined.mp4
2025-07-29 10:40:45,289 - INFO - 合并后的视频时长: 8.13秒，目标音频时长: 3.17秒
2025-07-29 10:40:45,289 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuqeo9xdh\temp_combined.mp4 -ss 0 -to 3.169 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-29 10:40:45,527 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:45,527 - INFO - 目标音频时长: 3.17秒
2025-07-29 10:40:45,527 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:40:45,527 - INFO - 时长差异: 0.05秒 (1.70%)
2025-07-29 10:40:45,527 - INFO - ==========================================
2025-07-29 10:40:45,527 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:45,527 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-29 10:40:45,529 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuqeo9xdh
2025-07-29 10:40:45,570 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:45,570 - INFO -   - 音频时长: 3.17秒
2025-07-29 10:40:45,570 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:40:45,570 - INFO -   - 时长差异: 0.05秒 (1.70%)
2025-07-29 10:40:45,570 - INFO - 
----- 处理字幕 #23 的方案 #2 -----
2025-07-29 10:40:45,570 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-07-29 10:40:45,570 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5hyb4fbu
2025-07-29 10:40:45,571 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\181.mp4 (确认存在: True)
2025-07-29 10:40:45,571 - INFO - 添加场景ID=181，时长=1.48秒，累计时长=1.48秒
2025-07-29 10:40:45,571 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\182.mp4 (确认存在: True)
2025-07-29 10:40:45,571 - INFO - 添加场景ID=182，时长=1.36秒，累计时长=2.84秒
2025-07-29 10:40:45,571 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\183.mp4 (确认存在: True)
2025-07-29 10:40:45,571 - INFO - 添加场景ID=183，时长=2.12秒，累计时长=4.96秒
2025-07-29 10:40:45,571 - INFO - 场景总时长(4.96秒)已达到音频时长(3.17秒)的1.5倍，停止添加场景
2025-07-29 10:40:45,571 - INFO - 准备合并 3 个场景文件，总时长约 4.96秒
2025-07-29 10:40:45,571 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/181.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/182.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/183.mp4'

2025-07-29 10:40:45,571 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5hyb4fbu\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5hyb4fbu\temp_combined.mp4
2025-07-29 10:40:45,717 - INFO - 合并后的视频时长: 5.03秒，目标音频时长: 3.17秒
2025-07-29 10:40:45,717 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5hyb4fbu\temp_combined.mp4 -ss 0 -to 3.169 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-07-29 10:40:45,995 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:45,996 - INFO - 目标音频时长: 3.17秒
2025-07-29 10:40:45,996 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:40:45,996 - INFO - 时长差异: 0.05秒 (1.70%)
2025-07-29 10:40:45,996 - INFO - ==========================================
2025-07-29 10:40:45,996 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:45,996 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-07-29 10:40:45,996 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5hyb4fbu
2025-07-29 10:40:46,038 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:46,038 - INFO -   - 音频时长: 3.17秒
2025-07-29 10:40:46,038 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:40:46,038 - INFO -   - 时长差异: 0.05秒 (1.70%)
2025-07-29 10:40:46,038 - INFO - 
----- 处理字幕 #23 的方案 #3 -----
2025-07-29 10:40:46,038 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\23_3.mp4
2025-07-29 10:40:46,038 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnuu_bl3l
2025-07-29 10:40:46,039 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\177.mp4 (确认存在: True)
2025-07-29 10:40:46,039 - INFO - 添加场景ID=177，时长=7.00秒，累计时长=7.00秒
2025-07-29 10:40:46,039 - INFO - 场景总时长(7.00秒)已达到音频时长(3.17秒)的1.5倍，停止添加场景
2025-07-29 10:40:46,039 - INFO - 准备合并 1 个场景文件，总时长约 7.00秒
2025-07-29 10:40:46,039 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/177.mp4'

2025-07-29 10:40:46,039 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnuu_bl3l\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnuu_bl3l\temp_combined.mp4
2025-07-29 10:40:46,141 - INFO - 合并后的视频时长: 7.02秒，目标音频时长: 3.17秒
2025-07-29 10:40:46,141 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnuu_bl3l\temp_combined.mp4 -ss 0 -to 3.169 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\23_3.mp4
2025-07-29 10:40:46,356 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:46,356 - INFO - 目标音频时长: 3.17秒
2025-07-29 10:40:46,356 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:40:46,356 - INFO - 时长差异: 0.05秒 (1.70%)
2025-07-29 10:40:46,356 - INFO - ==========================================
2025-07-29 10:40:46,356 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:46,356 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\23_3.mp4
2025-07-29 10:40:46,357 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnuu_bl3l
2025-07-29 10:40:46,396 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:46,396 - INFO -   - 音频时长: 3.17秒
2025-07-29 10:40:46,396 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:40:46,396 - INFO -   - 时长差异: 0.05秒 (1.70%)
2025-07-29 10:40:46,396 - INFO - 
字幕 #23 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:46,396 - INFO - 生成的视频文件:
2025-07-29 10:40:46,396 - INFO -   1. F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-29 10:40:46,396 - INFO -   2. F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-07-29 10:40:46,396 - INFO -   3. F:/github/aicut_auto/newcut_ai\23_3.mp4
2025-07-29 10:40:46,396 - INFO - ========== 字幕 #23 处理结束 ==========

