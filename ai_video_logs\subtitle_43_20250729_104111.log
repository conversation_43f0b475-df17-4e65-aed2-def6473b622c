2025-07-29 10:41:11,758 - INFO - ========== 字幕 #43 处理开始 ==========
2025-07-29 10:41:11,758 - INFO - 字幕内容: 哥哥被激怒，命人将他扔进护城河。
2025-07-29 10:41:11,758 - INFO - 字幕序号: [250, 251]
2025-07-29 10:41:11,758 - INFO - 音频文件详情:
2025-07-29 10:41:11,758 - INFO -   - 路径: output\43.wav
2025-07-29 10:41:11,758 - INFO -   - 时长: 2.61秒
2025-07-29 10:41:11,758 - INFO -   - 验证音频时长: 2.61秒
2025-07-29 10:41:11,758 - INFO - 字幕时间戳信息:
2025-07-29 10:41:11,758 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:11,759 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:11,759 - INFO -   - 根据生成的音频时长(2.61秒)已调整字幕时间戳
2025-07-29 10:41:11,759 - INFO - ========== 新模式：为字幕 #43 生成4套场景方案 ==========
2025-07-29 10:41:11,759 - INFO - 字幕序号列表: [250, 251]
2025-07-29 10:41:11,759 - INFO - 
--- 生成方案 #1：基于字幕序号 #250 ---
2025-07-29 10:41:11,759 - INFO - 开始为单个字幕序号 #250 匹配场景，目标时长: 2.61秒
2025-07-29 10:41:11,759 - INFO - 开始查找字幕序号 [250] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:11,759 - INFO - 找到related_overlap场景: scene_id=370, 字幕#250
2025-07-29 10:41:11,759 - INFO - 找到related_overlap场景: scene_id=371, 字幕#250
2025-07-29 10:41:11,759 - INFO - 找到related_between场景: scene_id=372, 字幕#250
2025-07-29 10:41:11,760 - INFO - 找到related_between场景: scene_id=373, 字幕#250
2025-07-29 10:41:11,760 - INFO - 找到related_between场景: scene_id=374, 字幕#250
2025-07-29 10:41:11,760 - INFO - 找到related_between场景: scene_id=375, 字幕#250
2025-07-29 10:41:11,760 - INFO - 找到related_between场景: scene_id=376, 字幕#250
2025-07-29 10:41:11,760 - INFO - 找到related_between场景: scene_id=377, 字幕#250
2025-07-29 10:41:11,760 - INFO - 找到related_between场景: scene_id=378, 字幕#250
2025-07-29 10:41:11,760 - INFO - 找到related_between场景: scene_id=379, 字幕#250
2025-07-29 10:41:11,760 - INFO - 找到related_between场景: scene_id=380, 字幕#250
2025-07-29 10:41:11,760 - INFO - 找到related_between场景: scene_id=381, 字幕#250
2025-07-29 10:41:11,760 - INFO - 字幕 #250 找到 2 个overlap场景, 10 个between场景
2025-07-29 10:41:11,760 - INFO - 字幕序号 #250 找到 2 个可用overlap场景, 10 个可用between场景
2025-07-29 10:41:11,760 - INFO - 选择第一个overlap场景作为起点: scene_id=370
2025-07-29 10:41:11,760 - INFO - 添加起点场景: scene_id=370, 时长=1.60秒, 累计时长=1.60秒
2025-07-29 10:41:11,760 - INFO - 起点场景时长不足，需要延伸填充 1.01秒
2025-07-29 10:41:11,760 - INFO - 起点场景在原始列表中的索引: 369
2025-07-29 10:41:11,760 - INFO - 延伸添加场景: scene_id=371 (裁剪至 1.01秒)
2025-07-29 10:41:11,760 - INFO - 累计时长: 2.61秒
2025-07-29 10:41:11,760 - INFO - 字幕序号 #250 场景匹配完成，共选择 2 个场景，总时长: 2.61秒
2025-07-29 10:41:11,760 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:11,760 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:11,760 - INFO - 
--- 生成方案 #2：基于字幕序号 #251 ---
2025-07-29 10:41:11,760 - INFO - 开始为单个字幕序号 #251 匹配场景，目标时长: 2.61秒
2025-07-29 10:41:11,760 - INFO - 开始查找字幕序号 [251] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:11,760 - INFO - 找到related_overlap场景: scene_id=382, 字幕#251
2025-07-29 10:41:11,761 - INFO - 找到related_between场景: scene_id=372, 字幕#251
2025-07-29 10:41:11,761 - INFO - 找到related_between场景: scene_id=373, 字幕#251
2025-07-29 10:41:11,761 - INFO - 找到related_between场景: scene_id=374, 字幕#251
2025-07-29 10:41:11,761 - INFO - 找到related_between场景: scene_id=375, 字幕#251
2025-07-29 10:41:11,761 - INFO - 找到related_between场景: scene_id=376, 字幕#251
2025-07-29 10:41:11,761 - INFO - 找到related_between场景: scene_id=377, 字幕#251
2025-07-29 10:41:11,761 - INFO - 找到related_between场景: scene_id=378, 字幕#251
2025-07-29 10:41:11,761 - INFO - 找到related_between场景: scene_id=379, 字幕#251
2025-07-29 10:41:11,761 - INFO - 找到related_between场景: scene_id=380, 字幕#251
2025-07-29 10:41:11,761 - INFO - 找到related_between场景: scene_id=381, 字幕#251
2025-07-29 10:41:11,761 - INFO - 找到related_between场景: scene_id=383, 字幕#251
2025-07-29 10:41:11,762 - INFO - 字幕 #251 找到 1 个overlap场景, 11 个between场景
2025-07-29 10:41:11,762 - INFO - 字幕序号 #251 找到 1 个可用overlap场景, 11 个可用between场景
2025-07-29 10:41:11,762 - INFO - 选择第一个overlap场景作为起点: scene_id=382
2025-07-29 10:41:11,762 - INFO - 添加起点场景: scene_id=382, 时长=2.44秒, 累计时长=2.44秒
2025-07-29 10:41:11,762 - INFO - 起点场景时长不足，需要延伸填充 0.17秒
2025-07-29 10:41:11,762 - INFO - 起点场景在原始列表中的索引: 381
2025-07-29 10:41:11,762 - INFO - 延伸添加场景: scene_id=383 (裁剪至 0.17秒)
2025-07-29 10:41:11,762 - INFO - 累计时长: 2.61秒
2025-07-29 10:41:11,762 - INFO - 字幕序号 #251 场景匹配完成，共选择 2 个场景，总时长: 2.61秒
2025-07-29 10:41:11,762 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:11,762 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:11,762 - INFO - ========== 当前模式：为字幕 #43 生成 1 套场景方案 ==========
2025-07-29 10:41:11,762 - INFO - 开始查找字幕序号 [250, 251] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:11,762 - INFO - 找到related_overlap场景: scene_id=370, 字幕#250
2025-07-29 10:41:11,762 - INFO - 找到related_overlap场景: scene_id=371, 字幕#250
2025-07-29 10:41:11,762 - INFO - 找到related_overlap场景: scene_id=382, 字幕#251
2025-07-29 10:41:11,763 - INFO - 找到related_between场景: scene_id=372, 字幕#250
2025-07-29 10:41:11,763 - INFO - 找到related_between场景: scene_id=373, 字幕#250
2025-07-29 10:41:11,763 - INFO - 找到related_between场景: scene_id=374, 字幕#250
2025-07-29 10:41:11,763 - INFO - 找到related_between场景: scene_id=375, 字幕#250
2025-07-29 10:41:11,763 - INFO - 找到related_between场景: scene_id=376, 字幕#250
2025-07-29 10:41:11,763 - INFO - 找到related_between场景: scene_id=377, 字幕#250
2025-07-29 10:41:11,763 - INFO - 找到related_between场景: scene_id=378, 字幕#250
2025-07-29 10:41:11,763 - INFO - 找到related_between场景: scene_id=379, 字幕#250
2025-07-29 10:41:11,763 - INFO - 找到related_between场景: scene_id=380, 字幕#250
2025-07-29 10:41:11,763 - INFO - 找到related_between场景: scene_id=381, 字幕#250
2025-07-29 10:41:11,763 - INFO - 找到related_between场景: scene_id=383, 字幕#251
2025-07-29 10:41:11,763 - INFO - 字幕 #250 找到 2 个overlap场景, 10 个between场景
2025-07-29 10:41:11,763 - INFO - 字幕 #251 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:11,763 - INFO - 共收集 3 个未使用的overlap场景和 11 个未使用的between场景
2025-07-29 10:41:11,763 - INFO - 开始生成方案 #1
2025-07-29 10:41:11,763 - INFO - 方案 #1: 为字幕#250选择初始化overlap场景id=370
2025-07-29 10:41:11,763 - INFO - 方案 #1: 为字幕#251选择初始化overlap场景id=382
2025-07-29 10:41:11,763 - INFO - 方案 #1: 初始选择后，当前总时长=4.04秒
2025-07-29 10:41:11,763 - INFO - 方案 #1: 额外between选择后，当前总时长=4.04秒
2025-07-29 10:41:11,763 - INFO - 方案 #1: 场景总时长(4.04秒)大于音频时长(2.61秒)，需要裁剪
2025-07-29 10:41:11,763 - INFO - 调整前总时长: 4.04秒, 目标时长: 2.61秒
2025-07-29 10:41:11,763 - INFO - 需要裁剪 1.43秒
2025-07-29 10:41:11,763 - INFO - 裁剪最长场景ID=382：从2.44秒裁剪至1.01秒
2025-07-29 10:41:11,763 - INFO - 调整后总时长: 2.61秒，与目标时长差异: 0.00秒
2025-07-29 10:41:11,763 - INFO - 方案 #1 调整/填充后最终总时长: 2.61秒
2025-07-29 10:41:11,763 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:11,763 - INFO - ========== 当前模式：字幕 #43 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:11,763 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:11,763 - INFO - ========== 新模式：字幕 #43 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:11,763 - INFO - 
----- 处理字幕 #43 的方案 #1 -----
2025-07-29 10:41:11,763 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-29 10:41:11,764 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5ym95qsg
2025-07-29 10:41:11,764 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\370.mp4 (确认存在: True)
2025-07-29 10:41:11,764 - INFO - 添加场景ID=370，时长=1.60秒，累计时长=1.60秒
2025-07-29 10:41:11,764 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\371.mp4 (确认存在: True)
2025-07-29 10:41:11,764 - INFO - 添加场景ID=371，时长=1.44秒，累计时长=3.04秒
2025-07-29 10:41:11,765 - INFO - 准备合并 2 个场景文件，总时长约 3.04秒
2025-07-29 10:41:11,765 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/370.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/371.mp4'

2025-07-29 10:41:11,765 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5ym95qsg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5ym95qsg\temp_combined.mp4
2025-07-29 10:41:11,892 - INFO - 合并后的视频时长: 3.09秒，目标音频时长: 2.61秒
2025-07-29 10:41:11,892 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5ym95qsg\temp_combined.mp4 -ss 0 -to 2.606 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-29 10:41:12,120 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:12,120 - INFO - 目标音频时长: 2.61秒
2025-07-29 10:41:12,120 - INFO - 实际视频时长: 2.66秒
2025-07-29 10:41:12,120 - INFO - 时长差异: 0.06秒 (2.19%)
2025-07-29 10:41:12,120 - INFO - ==========================================
2025-07-29 10:41:12,120 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:12,120 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-29 10:41:12,120 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5ym95qsg
2025-07-29 10:41:12,163 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:12,163 - INFO -   - 音频时长: 2.61秒
2025-07-29 10:41:12,163 - INFO -   - 视频时长: 2.66秒
2025-07-29 10:41:12,163 - INFO -   - 时长差异: 0.06秒 (2.19%)
2025-07-29 10:41:12,163 - INFO - 
----- 处理字幕 #43 的方案 #2 -----
2025-07-29 10:41:12,163 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-29 10:41:12,163 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcoqhskb_
2025-07-29 10:41:12,164 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\382.mp4 (确认存在: True)
2025-07-29 10:41:12,164 - INFO - 添加场景ID=382，时长=2.44秒，累计时长=2.44秒
2025-07-29 10:41:12,164 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\383.mp4 (确认存在: True)
2025-07-29 10:41:12,164 - INFO - 添加场景ID=383，时长=2.56秒，累计时长=5.00秒
2025-07-29 10:41:12,164 - INFO - 场景总时长(5.00秒)已达到音频时长(2.61秒)的1.5倍，停止添加场景
2025-07-29 10:41:12,164 - INFO - 准备合并 2 个场景文件，总时长约 5.00秒
2025-07-29 10:41:12,164 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/382.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/383.mp4'

2025-07-29 10:41:12,164 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcoqhskb_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcoqhskb_\temp_combined.mp4
2025-07-29 10:41:12,288 - INFO - 合并后的视频时长: 5.05秒，目标音频时长: 2.61秒
2025-07-29 10:41:12,288 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcoqhskb_\temp_combined.mp4 -ss 0 -to 2.606 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-29 10:41:12,530 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:12,531 - INFO - 目标音频时长: 2.61秒
2025-07-29 10:41:12,531 - INFO - 实际视频时长: 2.66秒
2025-07-29 10:41:12,531 - INFO - 时长差异: 0.06秒 (2.19%)
2025-07-29 10:41:12,531 - INFO - ==========================================
2025-07-29 10:41:12,531 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:12,531 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-29 10:41:12,531 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcoqhskb_
2025-07-29 10:41:12,576 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:12,576 - INFO -   - 音频时长: 2.61秒
2025-07-29 10:41:12,576 - INFO -   - 视频时长: 2.66秒
2025-07-29 10:41:12,576 - INFO -   - 时长差异: 0.06秒 (2.19%)
2025-07-29 10:41:12,576 - INFO - 
----- 处理字幕 #43 的方案 #3 -----
2025-07-29 10:41:12,576 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-07-29 10:41:12,578 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpn9p8d8rd
2025-07-29 10:41:12,578 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\370.mp4 (确认存在: True)
2025-07-29 10:41:12,578 - INFO - 添加场景ID=370，时长=1.60秒，累计时长=1.60秒
2025-07-29 10:41:12,578 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\382.mp4 (确认存在: True)
2025-07-29 10:41:12,578 - INFO - 添加场景ID=382，时长=2.44秒，累计时长=4.04秒
2025-07-29 10:41:12,578 - INFO - 场景总时长(4.04秒)已达到音频时长(2.61秒)的1.5倍，停止添加场景
2025-07-29 10:41:12,578 - INFO - 准备合并 2 个场景文件，总时长约 4.04秒
2025-07-29 10:41:12,578 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/370.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/382.mp4'

2025-07-29 10:41:12,578 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpn9p8d8rd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpn9p8d8rd\temp_combined.mp4
2025-07-29 10:41:12,702 - INFO - 合并后的视频时长: 4.09秒，目标音频时长: 2.61秒
2025-07-29 10:41:12,702 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpn9p8d8rd\temp_combined.mp4 -ss 0 -to 2.606 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-07-29 10:41:12,934 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:12,934 - INFO - 目标音频时长: 2.61秒
2025-07-29 10:41:12,934 - INFO - 实际视频时长: 2.66秒
2025-07-29 10:41:12,934 - INFO - 时长差异: 0.06秒 (2.19%)
2025-07-29 10:41:12,934 - INFO - ==========================================
2025-07-29 10:41:12,934 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:12,934 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-07-29 10:41:12,934 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpn9p8d8rd
2025-07-29 10:41:12,983 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:12,983 - INFO -   - 音频时长: 2.61秒
2025-07-29 10:41:12,983 - INFO -   - 视频时长: 2.66秒
2025-07-29 10:41:12,983 - INFO -   - 时长差异: 0.06秒 (2.19%)
2025-07-29 10:41:12,983 - INFO - 
字幕 #43 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:12,983 - INFO - 生成的视频文件:
2025-07-29 10:41:12,983 - INFO -   1. F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-29 10:41:12,983 - INFO -   2. F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-07-29 10:41:12,983 - INFO -   3. F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-07-29 10:41:12,983 - INFO - ========== 字幕 #43 处理结束 ==========

