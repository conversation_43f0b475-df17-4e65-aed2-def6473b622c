2025-07-29 10:41:55,627 - INFO - ========== 字幕 #69 处理开始 ==========
2025-07-29 10:41:55,628 - INFO - 字幕内容: 而保护她的办法，就是嫁给摄政王，一桩有名无实的婚事。
2025-07-29 10:41:55,628 - INFO - 字幕序号: [1296, 1309]
2025-07-29 10:41:55,628 - INFO - 音频文件详情:
2025-07-29 10:41:55,628 - INFO -   - 路径: output\69.wav
2025-07-29 10:41:55,628 - INFO -   - 时长: 3.03秒
2025-07-29 10:41:55,628 - INFO -   - 验证音频时长: 3.03秒
2025-07-29 10:41:55,629 - INFO - 字幕时间戳信息:
2025-07-29 10:41:55,629 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:55,629 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:55,629 - INFO -   - 根据生成的音频时长(3.03秒)已调整字幕时间戳
2025-07-29 10:41:55,629 - INFO - ========== 新模式：为字幕 #69 生成4套场景方案 ==========
2025-07-29 10:41:55,629 - INFO - 字幕序号列表: [1296, 1309]
2025-07-29 10:41:55,629 - INFO - 
--- 生成方案 #1：基于字幕序号 #1296 ---
2025-07-29 10:41:55,629 - INFO - 开始为单个字幕序号 #1296 匹配场景，目标时长: 3.03秒
2025-07-29 10:41:55,629 - INFO - 开始查找字幕序号 [1296] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:55,630 - INFO - 找到related_overlap场景: scene_id=1646, 字幕#1296
2025-07-29 10:41:55,630 - INFO - 找到related_overlap场景: scene_id=1647, 字幕#1296
2025-07-29 10:41:55,632 - INFO - 字幕 #1296 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:55,632 - INFO - 字幕序号 #1296 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:55,633 - INFO - 选择第一个overlap场景作为起点: scene_id=1646
2025-07-29 10:41:55,633 - INFO - 添加起点场景: scene_id=1646, 时长=1.36秒, 累计时长=1.36秒
2025-07-29 10:41:55,633 - INFO - 起点场景时长不足，需要延伸填充 1.67秒
2025-07-29 10:41:55,633 - INFO - 起点场景在原始列表中的索引: 1645
2025-07-29 10:41:55,633 - INFO - 延伸添加场景: scene_id=1647 (裁剪至 1.67秒)
2025-07-29 10:41:55,633 - INFO - 累计时长: 3.03秒
2025-07-29 10:41:55,633 - INFO - 字幕序号 #1296 场景匹配完成，共选择 2 个场景，总时长: 3.03秒
2025-07-29 10:41:55,633 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:55,633 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:55,633 - INFO - 
--- 生成方案 #2：基于字幕序号 #1309 ---
2025-07-29 10:41:55,633 - INFO - 开始为单个字幕序号 #1309 匹配场景，目标时长: 3.03秒
2025-07-29 10:41:55,633 - INFO - 开始查找字幕序号 [1309] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:55,634 - INFO - 找到related_overlap场景: scene_id=1657, 字幕#1309
2025-07-29 10:41:55,635 - INFO - 字幕 #1309 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:55,635 - INFO - 字幕序号 #1309 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:55,635 - INFO - 选择第一个overlap场景作为起点: scene_id=1657
2025-07-29 10:41:55,635 - INFO - 添加起点场景: scene_id=1657, 时长=1.68秒, 累计时长=1.68秒
2025-07-29 10:41:55,635 - INFO - 起点场景时长不足，需要延伸填充 1.35秒
2025-07-29 10:41:55,635 - INFO - 起点场景在原始列表中的索引: 1656
2025-07-29 10:41:55,635 - INFO - 延伸添加场景: scene_id=1658 (裁剪至 1.35秒)
2025-07-29 10:41:55,635 - INFO - 累计时长: 3.03秒
2025-07-29 10:41:55,635 - INFO - 字幕序号 #1309 场景匹配完成，共选择 2 个场景，总时长: 3.03秒
2025-07-29 10:41:55,635 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:55,635 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:55,635 - INFO - ========== 当前模式：为字幕 #69 生成 1 套场景方案 ==========
2025-07-29 10:41:55,635 - INFO - 开始查找字幕序号 [1296, 1309] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:55,637 - INFO - 找到related_overlap场景: scene_id=1646, 字幕#1296
2025-07-29 10:41:55,637 - INFO - 找到related_overlap场景: scene_id=1647, 字幕#1296
2025-07-29 10:41:55,637 - INFO - 找到related_overlap场景: scene_id=1657, 字幕#1309
2025-07-29 10:41:55,638 - INFO - 字幕 #1296 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:55,638 - INFO - 字幕 #1309 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:55,639 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:55,639 - INFO - 开始生成方案 #1
2025-07-29 10:41:55,639 - INFO - 方案 #1: 为字幕#1296选择初始化overlap场景id=1646
2025-07-29 10:41:55,639 - INFO - 方案 #1: 为字幕#1309选择初始化overlap场景id=1657
2025-07-29 10:41:55,639 - INFO - 方案 #1: 初始选择后，当前总时长=3.04秒
2025-07-29 10:41:55,639 - INFO - 方案 #1: 额外between选择后，当前总时长=3.04秒
2025-07-29 10:41:55,639 - INFO - 方案 #1: 场景总时长(3.04秒)大于音频时长(3.03秒)，需要裁剪
2025-07-29 10:41:55,639 - INFO - 调整前总时长: 3.04秒, 目标时长: 3.03秒
2025-07-29 10:41:55,639 - INFO - 场景时长已经匹配目标时长，无需调整
2025-07-29 10:41:55,639 - INFO - 方案 #1 调整/填充后最终总时长: 3.04秒
2025-07-29 10:41:55,639 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:55,639 - INFO - ========== 当前模式：字幕 #69 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:55,639 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:55,639 - INFO - ========== 新模式：字幕 #69 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:55,639 - INFO - 
----- 处理字幕 #69 的方案 #1 -----
2025-07-29 10:41:55,639 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-07-29 10:41:55,640 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu706am0t
2025-07-29 10:41:55,641 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1646.mp4 (确认存在: True)
2025-07-29 10:41:55,641 - INFO - 添加场景ID=1646，时长=1.36秒，累计时长=1.36秒
2025-07-29 10:41:55,641 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1647.mp4 (确认存在: True)
2025-07-29 10:41:55,641 - INFO - 添加场景ID=1647，时长=2.56秒，累计时长=3.92秒
2025-07-29 10:41:55,642 - INFO - 准备合并 2 个场景文件，总时长约 3.92秒
2025-07-29 10:41:55,642 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1646.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1647.mp4'

2025-07-29 10:41:55,642 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpu706am0t\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpu706am0t\temp_combined.mp4
2025-07-29 10:41:55,809 - INFO - 合并后的视频时长: 3.97秒，目标音频时长: 3.03秒
2025-07-29 10:41:55,809 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpu706am0t\temp_combined.mp4 -ss 0 -to 3.03 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-07-29 10:41:56,085 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:56,085 - INFO - 目标音频时长: 3.03秒
2025-07-29 10:41:56,085 - INFO - 实际视频时长: 3.06秒
2025-07-29 10:41:56,085 - INFO - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:41:56,085 - INFO - ==========================================
2025-07-29 10:41:56,085 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:56,086 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-07-29 10:41:56,086 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu706am0t
2025-07-29 10:41:56,137 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:56,137 - INFO -   - 音频时长: 3.03秒
2025-07-29 10:41:56,137 - INFO -   - 视频时长: 3.06秒
2025-07-29 10:41:56,137 - INFO -   - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:41:56,137 - INFO - 
----- 处理字幕 #69 的方案 #2 -----
2025-07-29 10:41:56,137 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-07-29 10:41:56,138 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6jf_7cwo
2025-07-29 10:41:56,138 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1657.mp4 (确认存在: True)
2025-07-29 10:41:56,138 - INFO - 添加场景ID=1657，时长=1.68秒，累计时长=1.68秒
2025-07-29 10:41:56,138 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1658.mp4 (确认存在: True)
2025-07-29 10:41:56,138 - INFO - 添加场景ID=1658，时长=3.16秒，累计时长=4.84秒
2025-07-29 10:41:56,138 - INFO - 场景总时长(4.84秒)已达到音频时长(3.03秒)的1.5倍，停止添加场景
2025-07-29 10:41:56,138 - INFO - 准备合并 2 个场景文件，总时长约 4.84秒
2025-07-29 10:41:56,138 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1657.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1658.mp4'

2025-07-29 10:41:56,138 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6jf_7cwo\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6jf_7cwo\temp_combined.mp4
2025-07-29 10:41:56,291 - INFO - 合并后的视频时长: 4.89秒，目标音频时长: 3.03秒
2025-07-29 10:41:56,291 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6jf_7cwo\temp_combined.mp4 -ss 0 -to 3.03 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-07-29 10:41:56,605 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:56,605 - INFO - 目标音频时长: 3.03秒
2025-07-29 10:41:56,605 - INFO - 实际视频时长: 3.06秒
2025-07-29 10:41:56,605 - INFO - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:41:56,605 - INFO - ==========================================
2025-07-29 10:41:56,605 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:56,605 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-07-29 10:41:56,606 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6jf_7cwo
2025-07-29 10:41:56,671 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:56,671 - INFO -   - 音频时长: 3.03秒
2025-07-29 10:41:56,671 - INFO -   - 视频时长: 3.06秒
2025-07-29 10:41:56,671 - INFO -   - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:41:56,672 - INFO - 
----- 处理字幕 #69 的方案 #3 -----
2025-07-29 10:41:56,672 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-07-29 10:41:56,672 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpna2wfk8z
2025-07-29 10:41:56,673 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1646.mp4 (确认存在: True)
2025-07-29 10:41:56,673 - INFO - 添加场景ID=1646，时长=1.36秒，累计时长=1.36秒
2025-07-29 10:41:56,673 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1657.mp4 (确认存在: True)
2025-07-29 10:41:56,673 - INFO - 添加场景ID=1657，时长=1.68秒，累计时长=3.04秒
2025-07-29 10:41:56,673 - INFO - 准备合并 2 个场景文件，总时长约 3.04秒
2025-07-29 10:41:56,674 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1646.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1657.mp4'

2025-07-29 10:41:56,674 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpna2wfk8z\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpna2wfk8z\temp_combined.mp4
2025-07-29 10:41:56,826 - INFO - 合并后的视频时长: 3.09秒，目标音频时长: 3.03秒
2025-07-29 10:41:56,826 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpna2wfk8z\temp_combined.mp4 -ss 0 -to 3.03 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-07-29 10:41:57,149 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:57,149 - INFO - 目标音频时长: 3.03秒
2025-07-29 10:41:57,149 - INFO - 实际视频时长: 3.06秒
2025-07-29 10:41:57,149 - INFO - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:41:57,149 - INFO - ==========================================
2025-07-29 10:41:57,149 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:57,149 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-07-29 10:41:57,150 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpna2wfk8z
2025-07-29 10:41:57,208 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:57,208 - INFO -   - 音频时长: 3.03秒
2025-07-29 10:41:57,208 - INFO -   - 视频时长: 3.06秒
2025-07-29 10:41:57,208 - INFO -   - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:41:57,208 - INFO - 
字幕 #69 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:57,208 - INFO - 生成的视频文件:
2025-07-29 10:41:57,208 - INFO -   1. F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-07-29 10:41:57,208 - INFO -   2. F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-07-29 10:41:57,208 - INFO -   3. F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-07-29 10:41:57,208 - INFO - ========== 字幕 #69 处理结束 ==========

