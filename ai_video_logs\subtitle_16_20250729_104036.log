2025-07-29 10:40:36,374 - INFO - ========== 字幕 #16 处理开始 ==========
2025-07-29 10:40:36,374 - INFO - 字幕内容: 她京城第一美人的容貌，引来全场瞩目。
2025-07-29 10:40:36,374 - INFO - 字幕序号: [73, 78]
2025-07-29 10:40:36,374 - INFO - 音频文件详情:
2025-07-29 10:40:36,374 - INFO -   - 路径: output\16.wav
2025-07-29 10:40:36,374 - INFO -   - 时长: 2.75秒
2025-07-29 10:40:36,374 - INFO -   - 验证音频时长: 2.75秒
2025-07-29 10:40:36,374 - INFO - 字幕时间戳信息:
2025-07-29 10:40:36,374 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:36,374 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:36,375 - INFO -   - 根据生成的音频时长(2.75秒)已调整字幕时间戳
2025-07-29 10:40:36,375 - INFO - ========== 新模式：为字幕 #16 生成4套场景方案 ==========
2025-07-29 10:40:36,375 - INFO - 字幕序号列表: [73, 78]
2025-07-29 10:40:36,375 - INFO - 
--- 生成方案 #1：基于字幕序号 #73 ---
2025-07-29 10:40:36,375 - INFO - 开始为单个字幕序号 #73 匹配场景，目标时长: 2.75秒
2025-07-29 10:40:36,375 - INFO - 开始查找字幕序号 [73] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:36,375 - INFO - 找到related_overlap场景: scene_id=102, 字幕#73
2025-07-29 10:40:36,375 - INFO - 找到related_overlap场景: scene_id=104, 字幕#73
2025-07-29 10:40:36,375 - INFO - 找到related_between场景: scene_id=101, 字幕#73
2025-07-29 10:40:36,376 - INFO - 字幕 #73 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:36,376 - INFO - 字幕序号 #73 找到 2 个可用overlap场景, 1 个可用between场景
2025-07-29 10:40:36,376 - INFO - 选择第一个overlap场景作为起点: scene_id=102
2025-07-29 10:40:36,376 - INFO - 添加起点场景: scene_id=102, 时长=1.56秒, 累计时长=1.56秒
2025-07-29 10:40:36,376 - INFO - 起点场景时长不足，需要延伸填充 1.19秒
2025-07-29 10:40:36,376 - INFO - 起点场景在原始列表中的索引: 101
2025-07-29 10:40:36,376 - INFO - 延伸添加场景: scene_id=103 (裁剪至 1.19秒)
2025-07-29 10:40:36,376 - INFO - 累计时长: 2.75秒
2025-07-29 10:40:36,376 - INFO - 字幕序号 #73 场景匹配完成，共选择 2 个场景，总时长: 2.75秒
2025-07-29 10:40:36,376 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:36,376 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:36,376 - INFO - 
--- 生成方案 #2：基于字幕序号 #78 ---
2025-07-29 10:40:36,376 - INFO - 开始为单个字幕序号 #78 匹配场景，目标时长: 2.75秒
2025-07-29 10:40:36,376 - INFO - 开始查找字幕序号 [78] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:36,376 - INFO - 找到related_overlap场景: scene_id=106, 字幕#78
2025-07-29 10:40:36,377 - INFO - 找到related_between场景: scene_id=107, 字幕#78
2025-07-29 10:40:36,377 - INFO - 找到related_between场景: scene_id=108, 字幕#78
2025-07-29 10:40:36,377 - INFO - 字幕 #78 找到 1 个overlap场景, 2 个between场景
2025-07-29 10:40:36,377 - INFO - 字幕序号 #78 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 10:40:36,378 - INFO - 选择第一个overlap场景作为起点: scene_id=106
2025-07-29 10:40:36,378 - INFO - 添加起点场景: scene_id=106, 时长=2.88秒, 累计时长=2.88秒
2025-07-29 10:40:36,378 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:40:36,378 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:40:36,378 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:36,378 - INFO - ========== 当前模式：为字幕 #16 生成 1 套场景方案 ==========
2025-07-29 10:40:36,378 - INFO - 开始查找字幕序号 [73, 78] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:36,378 - INFO - 找到related_overlap场景: scene_id=102, 字幕#73
2025-07-29 10:40:36,378 - INFO - 找到related_overlap场景: scene_id=104, 字幕#73
2025-07-29 10:40:36,378 - INFO - 找到related_overlap场景: scene_id=106, 字幕#78
2025-07-29 10:40:36,378 - INFO - 找到related_between场景: scene_id=101, 字幕#73
2025-07-29 10:40:36,378 - INFO - 找到related_between场景: scene_id=107, 字幕#78
2025-07-29 10:40:36,378 - INFO - 找到related_between场景: scene_id=108, 字幕#78
2025-07-29 10:40:36,379 - INFO - 字幕 #73 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:36,379 - INFO - 字幕 #78 找到 1 个overlap场景, 2 个between场景
2025-07-29 10:40:36,379 - INFO - 共收集 3 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 10:40:36,379 - INFO - 开始生成方案 #1
2025-07-29 10:40:36,379 - INFO - 方案 #1: 为字幕#73选择初始化overlap场景id=104
2025-07-29 10:40:36,379 - INFO - 方案 #1: 为字幕#78选择初始化overlap场景id=106
2025-07-29 10:40:36,379 - INFO - 方案 #1: 初始选择后，当前总时长=5.24秒
2025-07-29 10:40:36,379 - INFO - 方案 #1: 额外between选择后，当前总时长=5.24秒
2025-07-29 10:40:36,379 - INFO - 方案 #1: 场景总时长(5.24秒)大于音频时长(2.75秒)，需要裁剪
2025-07-29 10:40:36,379 - INFO - 调整前总时长: 5.24秒, 目标时长: 2.75秒
2025-07-29 10:40:36,379 - INFO - 需要裁剪 2.48秒
2025-07-29 10:40:36,379 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:40:36,379 - INFO - 裁剪场景ID=106：从2.88秒裁剪至1.00秒
2025-07-29 10:40:36,379 - INFO - 裁剪场景ID=104：从2.36秒裁剪至1.75秒
2025-07-29 10:40:36,379 - INFO - 调整后总时长: 2.75秒，与目标时长差异: 0.00秒
2025-07-29 10:40:36,379 - INFO - 方案 #1 调整/填充后最终总时长: 2.75秒
2025-07-29 10:40:36,379 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:36,379 - INFO - ========== 当前模式：字幕 #16 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:36,379 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:36,379 - INFO - ========== 新模式：字幕 #16 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:36,379 - INFO - 
----- 处理字幕 #16 的方案 #1 -----
2025-07-29 10:40:36,379 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-29 10:40:36,380 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphhwh9ja8
2025-07-29 10:40:36,380 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\102.mp4 (确认存在: True)
2025-07-29 10:40:36,380 - INFO - 添加场景ID=102，时长=1.56秒，累计时长=1.56秒
2025-07-29 10:40:36,380 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\103.mp4 (确认存在: True)
2025-07-29 10:40:36,380 - INFO - 添加场景ID=103，时长=1.92秒，累计时长=3.48秒
2025-07-29 10:40:36,380 - INFO - 准备合并 2 个场景文件，总时长约 3.48秒
2025-07-29 10:40:36,380 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/102.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/103.mp4'

2025-07-29 10:40:36,380 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphhwh9ja8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphhwh9ja8\temp_combined.mp4
2025-07-29 10:40:36,497 - INFO - 合并后的视频时长: 3.53秒，目标音频时长: 2.75秒
2025-07-29 10:40:36,497 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphhwh9ja8\temp_combined.mp4 -ss 0 -to 2.753 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-29 10:40:36,742 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:36,742 - INFO - 目标音频时长: 2.75秒
2025-07-29 10:40:36,742 - INFO - 实际视频时长: 2.78秒
2025-07-29 10:40:36,742 - INFO - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:40:36,742 - INFO - ==========================================
2025-07-29 10:40:36,742 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:36,742 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-29 10:40:36,743 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphhwh9ja8
2025-07-29 10:40:36,789 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:36,789 - INFO -   - 音频时长: 2.75秒
2025-07-29 10:40:36,789 - INFO -   - 视频时长: 2.78秒
2025-07-29 10:40:36,789 - INFO -   - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:40:36,789 - INFO - 
----- 处理字幕 #16 的方案 #2 -----
2025-07-29 10:40:36,789 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-29 10:40:36,789 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpg39g1y_f
2025-07-29 10:40:36,790 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\106.mp4 (确认存在: True)
2025-07-29 10:40:36,790 - INFO - 添加场景ID=106，时长=2.88秒，累计时长=2.88秒
2025-07-29 10:40:36,790 - INFO - 准备合并 1 个场景文件，总时长约 2.88秒
2025-07-29 10:40:36,790 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/106.mp4'

2025-07-29 10:40:36,790 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpg39g1y_f\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpg39g1y_f\temp_combined.mp4
2025-07-29 10:40:36,922 - INFO - 合并后的视频时长: 2.90秒，目标音频时长: 2.75秒
2025-07-29 10:40:36,922 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpg39g1y_f\temp_combined.mp4 -ss 0 -to 2.753 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-29 10:40:37,174 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:37,174 - INFO - 目标音频时长: 2.75秒
2025-07-29 10:40:37,174 - INFO - 实际视频时长: 2.78秒
2025-07-29 10:40:37,174 - INFO - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:40:37,174 - INFO - ==========================================
2025-07-29 10:40:37,174 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:37,174 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-29 10:40:37,175 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpg39g1y_f
2025-07-29 10:40:37,220 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:37,220 - INFO -   - 音频时长: 2.75秒
2025-07-29 10:40:37,220 - INFO -   - 视频时长: 2.78秒
2025-07-29 10:40:37,220 - INFO -   - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:40:37,220 - INFO - 
----- 处理字幕 #16 的方案 #3 -----
2025-07-29 10:40:37,220 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-07-29 10:40:37,220 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp65qtzmqr
2025-07-29 10:40:37,221 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\104.mp4 (确认存在: True)
2025-07-29 10:40:37,221 - INFO - 添加场景ID=104，时长=2.36秒，累计时长=2.36秒
2025-07-29 10:40:37,221 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\106.mp4 (确认存在: True)
2025-07-29 10:40:37,221 - INFO - 添加场景ID=106，时长=2.88秒，累计时长=5.24秒
2025-07-29 10:40:37,221 - INFO - 场景总时长(5.24秒)已达到音频时长(2.75秒)的1.5倍，停止添加场景
2025-07-29 10:40:37,221 - INFO - 准备合并 2 个场景文件，总时长约 5.24秒
2025-07-29 10:40:37,221 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/104.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/106.mp4'

2025-07-29 10:40:37,221 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp65qtzmqr\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp65qtzmqr\temp_combined.mp4
2025-07-29 10:40:37,354 - INFO - 合并后的视频时长: 5.29秒，目标音频时长: 2.75秒
2025-07-29 10:40:37,354 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp65qtzmqr\temp_combined.mp4 -ss 0 -to 2.753 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-07-29 10:40:37,585 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:37,585 - INFO - 目标音频时长: 2.75秒
2025-07-29 10:40:37,585 - INFO - 实际视频时长: 2.78秒
2025-07-29 10:40:37,585 - INFO - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:40:37,585 - INFO - ==========================================
2025-07-29 10:40:37,585 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:37,585 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-07-29 10:40:37,586 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp65qtzmqr
2025-07-29 10:40:37,630 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:37,630 - INFO -   - 音频时长: 2.75秒
2025-07-29 10:40:37,630 - INFO -   - 视频时长: 2.78秒
2025-07-29 10:40:37,630 - INFO -   - 时长差异: 0.03秒 (1.09%)
2025-07-29 10:40:37,630 - INFO - 
字幕 #16 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:37,630 - INFO - 生成的视频文件:
2025-07-29 10:40:37,630 - INFO -   1. F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-29 10:40:37,630 - INFO -   2. F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-29 10:40:37,630 - INFO -   3. F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-07-29 10:40:37,631 - INFO - ========== 字幕 #16 处理结束 ==========

