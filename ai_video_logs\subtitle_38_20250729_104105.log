2025-07-29 10:41:05,038 - INFO - ========== 字幕 #38 处理开始 ==========
2025-07-29 10:41:05,038 - INFO - 字幕内容: 药效发作，她神志不清，只能无助地向他求救。
2025-07-29 10:41:05,038 - INFO - 字幕序号: [220, 223]
2025-07-29 10:41:05,039 - INFO - 音频文件详情:
2025-07-29 10:41:05,039 - INFO -   - 路径: output\38.wav
2025-07-29 10:41:05,039 - INFO -   - 时长: 3.58秒
2025-07-29 10:41:05,039 - INFO -   - 验证音频时长: 3.58秒
2025-07-29 10:41:05,039 - INFO - 字幕时间戳信息:
2025-07-29 10:41:05,039 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:05,039 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:05,039 - INFO -   - 根据生成的音频时长(3.58秒)已调整字幕时间戳
2025-07-29 10:41:05,039 - INFO - ========== 新模式：为字幕 #38 生成4套场景方案 ==========
2025-07-29 10:41:05,039 - INFO - 字幕序号列表: [220, 223]
2025-07-29 10:41:05,039 - INFO - 
--- 生成方案 #1：基于字幕序号 #220 ---
2025-07-29 10:41:05,039 - INFO - 开始为单个字幕序号 #220 匹配场景，目标时长: 3.58秒
2025-07-29 10:41:05,039 - INFO - 开始查找字幕序号 [220] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:05,039 - INFO - 找到related_overlap场景: scene_id=309, 字幕#220
2025-07-29 10:41:05,039 - INFO - 找到related_overlap场景: scene_id=310, 字幕#220
2025-07-29 10:41:05,040 - INFO - 找到related_between场景: scene_id=311, 字幕#220
2025-07-29 10:41:05,040 - INFO - 找到related_between场景: scene_id=312, 字幕#220
2025-07-29 10:41:05,041 - INFO - 字幕 #220 找到 2 个overlap场景, 2 个between场景
2025-07-29 10:41:05,041 - INFO - 字幕序号 #220 找到 2 个可用overlap场景, 2 个可用between场景
2025-07-29 10:41:05,041 - INFO - 选择第一个overlap场景作为起点: scene_id=309
2025-07-29 10:41:05,041 - INFO - 添加起点场景: scene_id=309, 时长=2.56秒, 累计时长=2.56秒
2025-07-29 10:41:05,041 - INFO - 起点场景时长不足，需要延伸填充 1.02秒
2025-07-29 10:41:05,041 - INFO - 起点场景在原始列表中的索引: 308
2025-07-29 10:41:05,041 - INFO - 延伸添加场景: scene_id=310 (裁剪至 1.02秒)
2025-07-29 10:41:05,041 - INFO - 累计时长: 3.58秒
2025-07-29 10:41:05,041 - INFO - 字幕序号 #220 场景匹配完成，共选择 2 个场景，总时长: 3.58秒
2025-07-29 10:41:05,041 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:05,041 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:05,041 - INFO - 
--- 生成方案 #2：基于字幕序号 #223 ---
2025-07-29 10:41:05,041 - INFO - 开始为单个字幕序号 #223 匹配场景，目标时长: 3.58秒
2025-07-29 10:41:05,041 - INFO - 开始查找字幕序号 [223] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:05,041 - INFO - 找到related_overlap场景: scene_id=317, 字幕#223
2025-07-29 10:41:05,042 - INFO - 找到related_between场景: scene_id=318, 字幕#223
2025-07-29 10:41:05,042 - INFO - 找到related_between场景: scene_id=319, 字幕#223
2025-07-29 10:41:05,042 - INFO - 找到related_between场景: scene_id=320, 字幕#223
2025-07-29 10:41:05,042 - INFO - 找到related_between场景: scene_id=321, 字幕#223
2025-07-29 10:41:05,042 - INFO - 找到related_between场景: scene_id=322, 字幕#223
2025-07-29 10:41:05,042 - INFO - 找到related_between场景: scene_id=323, 字幕#223
2025-07-29 10:41:05,042 - INFO - 找到related_between场景: scene_id=324, 字幕#223
2025-07-29 10:41:05,042 - INFO - 找到related_between场景: scene_id=325, 字幕#223
2025-07-29 10:41:05,042 - INFO - 找到related_between场景: scene_id=326, 字幕#223
2025-07-29 10:41:05,042 - INFO - 字幕 #223 找到 1 个overlap场景, 9 个between场景
2025-07-29 10:41:05,042 - INFO - 字幕序号 #223 找到 1 个可用overlap场景, 9 个可用between场景
2025-07-29 10:41:05,042 - INFO - 选择第一个overlap场景作为起点: scene_id=317
2025-07-29 10:41:05,042 - INFO - 添加起点场景: scene_id=317, 时长=1.60秒, 累计时长=1.60秒
2025-07-29 10:41:05,042 - INFO - 起点场景时长不足，需要延伸填充 1.98秒
2025-07-29 10:41:05,042 - INFO - 起点场景在原始列表中的索引: 316
2025-07-29 10:41:05,042 - INFO - 延伸添加场景: scene_id=318 (裁剪至 1.98秒)
2025-07-29 10:41:05,042 - INFO - 累计时长: 3.58秒
2025-07-29 10:41:05,042 - INFO - 字幕序号 #223 场景匹配完成，共选择 2 个场景，总时长: 3.58秒
2025-07-29 10:41:05,042 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:05,042 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:05,042 - INFO - ========== 当前模式：为字幕 #38 生成 1 套场景方案 ==========
2025-07-29 10:41:05,042 - INFO - 开始查找字幕序号 [220, 223] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:05,043 - INFO - 找到related_overlap场景: scene_id=309, 字幕#220
2025-07-29 10:41:05,043 - INFO - 找到related_overlap场景: scene_id=310, 字幕#220
2025-07-29 10:41:05,043 - INFO - 找到related_overlap场景: scene_id=317, 字幕#223
2025-07-29 10:41:05,043 - INFO - 找到related_between场景: scene_id=311, 字幕#220
2025-07-29 10:41:05,043 - INFO - 找到related_between场景: scene_id=312, 字幕#220
2025-07-29 10:41:05,043 - INFO - 找到related_between场景: scene_id=318, 字幕#223
2025-07-29 10:41:05,043 - INFO - 找到related_between场景: scene_id=319, 字幕#223
2025-07-29 10:41:05,043 - INFO - 找到related_between场景: scene_id=320, 字幕#223
2025-07-29 10:41:05,043 - INFO - 找到related_between场景: scene_id=321, 字幕#223
2025-07-29 10:41:05,043 - INFO - 找到related_between场景: scene_id=322, 字幕#223
2025-07-29 10:41:05,043 - INFO - 找到related_between场景: scene_id=323, 字幕#223
2025-07-29 10:41:05,043 - INFO - 找到related_between场景: scene_id=324, 字幕#223
2025-07-29 10:41:05,043 - INFO - 找到related_between场景: scene_id=325, 字幕#223
2025-07-29 10:41:05,043 - INFO - 找到related_between场景: scene_id=326, 字幕#223
2025-07-29 10:41:05,044 - INFO - 字幕 #220 找到 2 个overlap场景, 2 个between场景
2025-07-29 10:41:05,044 - INFO - 字幕 #223 找到 1 个overlap场景, 9 个between场景
2025-07-29 10:41:05,044 - INFO - 共收集 3 个未使用的overlap场景和 11 个未使用的between场景
2025-07-29 10:41:05,044 - INFO - 开始生成方案 #1
2025-07-29 10:41:05,044 - INFO - 方案 #1: 为字幕#220选择初始化overlap场景id=309
2025-07-29 10:41:05,044 - INFO - 方案 #1: 为字幕#223选择初始化overlap场景id=317
2025-07-29 10:41:05,044 - INFO - 方案 #1: 初始选择后，当前总时长=4.16秒
2025-07-29 10:41:05,044 - INFO - 方案 #1: 额外between选择后，当前总时长=4.16秒
2025-07-29 10:41:05,044 - INFO - 方案 #1: 场景总时长(4.16秒)大于音频时长(3.58秒)，需要裁剪
2025-07-29 10:41:05,044 - INFO - 调整前总时长: 4.16秒, 目标时长: 3.58秒
2025-07-29 10:41:05,044 - INFO - 需要裁剪 0.58秒
2025-07-29 10:41:05,044 - INFO - 裁剪最长场景ID=309：从2.56秒裁剪至1.98秒
2025-07-29 10:41:05,044 - INFO - 调整后总时长: 3.58秒，与目标时长差异: 0.00秒
2025-07-29 10:41:05,044 - INFO - 方案 #1 调整/填充后最终总时长: 3.58秒
2025-07-29 10:41:05,044 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:05,044 - INFO - ========== 当前模式：字幕 #38 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:05,044 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:05,044 - INFO - ========== 新模式：字幕 #38 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:05,044 - INFO - 
----- 处理字幕 #38 的方案 #1 -----
2025-07-29 10:41:05,044 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-29 10:41:05,045 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_km13kxz
2025-07-29 10:41:05,045 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\309.mp4 (确认存在: True)
2025-07-29 10:41:05,045 - INFO - 添加场景ID=309，时长=2.56秒，累计时长=2.56秒
2025-07-29 10:41:05,045 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\310.mp4 (确认存在: True)
2025-07-29 10:41:05,045 - INFO - 添加场景ID=310，时长=3.88秒，累计时长=6.44秒
2025-07-29 10:41:05,045 - INFO - 场景总时长(6.44秒)已达到音频时长(3.58秒)的1.5倍，停止添加场景
2025-07-29 10:41:05,045 - INFO - 准备合并 2 个场景文件，总时长约 6.44秒
2025-07-29 10:41:05,045 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/309.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/310.mp4'

2025-07-29 10:41:05,045 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_km13kxz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_km13kxz\temp_combined.mp4
2025-07-29 10:41:05,176 - INFO - 合并后的视频时长: 6.49秒，目标音频时长: 3.58秒
2025-07-29 10:41:05,176 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_km13kxz\temp_combined.mp4 -ss 0 -to 3.582 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-29 10:41:05,427 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:05,427 - INFO - 目标音频时长: 3.58秒
2025-07-29 10:41:05,427 - INFO - 实际视频时长: 3.62秒
2025-07-29 10:41:05,427 - INFO - 时长差异: 0.04秒 (1.14%)
2025-07-29 10:41:05,427 - INFO - ==========================================
2025-07-29 10:41:05,427 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:05,427 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-29 10:41:05,428 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_km13kxz
2025-07-29 10:41:05,470 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:05,470 - INFO -   - 音频时长: 3.58秒
2025-07-29 10:41:05,470 - INFO -   - 视频时长: 3.62秒
2025-07-29 10:41:05,470 - INFO -   - 时长差异: 0.04秒 (1.14%)
2025-07-29 10:41:05,470 - INFO - 
----- 处理字幕 #38 的方案 #2 -----
2025-07-29 10:41:05,470 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\38_2.mp4
2025-07-29 10:41:05,471 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7ccgkw0s
2025-07-29 10:41:05,471 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\317.mp4 (确认存在: True)
2025-07-29 10:41:05,472 - INFO - 添加场景ID=317，时长=1.60秒，累计时长=1.60秒
2025-07-29 10:41:05,472 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\318.mp4 (确认存在: True)
2025-07-29 10:41:05,472 - INFO - 添加场景ID=318，时长=2.60秒，累计时长=4.20秒
2025-07-29 10:41:05,472 - INFO - 准备合并 2 个场景文件，总时长约 4.20秒
2025-07-29 10:41:05,472 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/317.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/318.mp4'

2025-07-29 10:41:05,472 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7ccgkw0s\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7ccgkw0s\temp_combined.mp4
2025-07-29 10:41:05,600 - INFO - 合并后的视频时长: 4.25秒，目标音频时长: 3.58秒
2025-07-29 10:41:05,600 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7ccgkw0s\temp_combined.mp4 -ss 0 -to 3.582 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\38_2.mp4
2025-07-29 10:41:05,858 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:05,858 - INFO - 目标音频时长: 3.58秒
2025-07-29 10:41:05,859 - INFO - 实际视频时长: 3.62秒
2025-07-29 10:41:05,859 - INFO - 时长差异: 0.04秒 (1.14%)
2025-07-29 10:41:05,859 - INFO - ==========================================
2025-07-29 10:41:05,859 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:05,859 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\38_2.mp4
2025-07-29 10:41:05,859 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7ccgkw0s
2025-07-29 10:41:05,913 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:05,913 - INFO -   - 音频时长: 3.58秒
2025-07-29 10:41:05,913 - INFO -   - 视频时长: 3.62秒
2025-07-29 10:41:05,913 - INFO -   - 时长差异: 0.04秒 (1.14%)
2025-07-29 10:41:05,913 - INFO - 
----- 处理字幕 #38 的方案 #3 -----
2025-07-29 10:41:05,913 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\38_3.mp4
2025-07-29 10:41:05,913 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5wy91rpj
2025-07-29 10:41:05,913 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\309.mp4 (确认存在: True)
2025-07-29 10:41:05,914 - INFO - 添加场景ID=309，时长=2.56秒，累计时长=2.56秒
2025-07-29 10:41:05,914 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\317.mp4 (确认存在: True)
2025-07-29 10:41:05,914 - INFO - 添加场景ID=317，时长=1.60秒，累计时长=4.16秒
2025-07-29 10:41:05,914 - INFO - 准备合并 2 个场景文件，总时长约 4.16秒
2025-07-29 10:41:05,914 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/309.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/317.mp4'

2025-07-29 10:41:05,914 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5wy91rpj\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5wy91rpj\temp_combined.mp4
2025-07-29 10:41:06,036 - INFO - 合并后的视频时长: 4.21秒，目标音频时长: 3.58秒
2025-07-29 10:41:06,036 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5wy91rpj\temp_combined.mp4 -ss 0 -to 3.582 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\38_3.mp4
