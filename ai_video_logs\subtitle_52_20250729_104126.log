2025-07-29 10:41:26,800 - INFO - ========== 字幕 #52 处理开始 ==========
2025-07-29 10:41:26,800 - INFO - 字幕内容: 哥哥盼子多年，以此为由，请求祖母饶恕姨娘。
2025-07-29 10:41:26,800 - INFO - 字幕序号: [296, 302]
2025-07-29 10:41:26,801 - INFO - 音频文件详情:
2025-07-29 10:41:26,801 - INFO -   - 路径: output\52.wav
2025-07-29 10:41:26,801 - INFO -   - 时长: 5.20秒
2025-07-29 10:41:26,801 - INFO -   - 验证音频时长: 5.20秒
2025-07-29 10:41:26,802 - INFO - 字幕时间戳信息:
2025-07-29 10:41:26,802 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:26,802 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:26,802 - INFO -   - 根据生成的音频时长(5.20秒)已调整字幕时间戳
2025-07-29 10:41:26,802 - INFO - ========== 新模式：为字幕 #52 生成4套场景方案 ==========
2025-07-29 10:41:26,802 - INFO - 字幕序号列表: [296, 302]
2025-07-29 10:41:26,802 - INFO - 
--- 生成方案 #1：基于字幕序号 #296 ---
2025-07-29 10:41:26,802 - INFO - 开始为单个字幕序号 #296 匹配场景，目标时长: 5.20秒
2025-07-29 10:41:26,802 - INFO - 开始查找字幕序号 [296] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:26,802 - INFO - 找到related_overlap场景: scene_id=438, 字幕#296
2025-07-29 10:41:26,805 - INFO - 找到related_between场景: scene_id=437, 字幕#296
2025-07-29 10:41:26,806 - INFO - 字幕 #296 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:26,806 - INFO - 字幕序号 #296 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 10:41:26,806 - INFO - 选择第一个overlap场景作为起点: scene_id=438
2025-07-29 10:41:26,806 - INFO - 添加起点场景: scene_id=438, 时长=2.40秒, 累计时长=2.40秒
2025-07-29 10:41:26,806 - INFO - 起点场景时长不足，需要延伸填充 2.80秒
2025-07-29 10:41:26,806 - INFO - 起点场景在原始列表中的索引: 437
2025-07-29 10:41:26,806 - INFO - 延伸添加场景: scene_id=439 (完整时长 2.20秒)
2025-07-29 10:41:26,806 - INFO - 累计时长: 4.60秒
2025-07-29 10:41:26,806 - INFO - 延伸添加场景: scene_id=440 (裁剪至 0.60秒)
2025-07-29 10:41:26,806 - INFO - 累计时长: 5.20秒
2025-07-29 10:41:26,807 - INFO - 字幕序号 #296 场景匹配完成，共选择 3 个场景，总时长: 5.20秒
2025-07-29 10:41:26,807 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:26,807 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:26,807 - INFO - 
--- 生成方案 #2：基于字幕序号 #302 ---
2025-07-29 10:41:26,807 - INFO - 开始为单个字幕序号 #302 匹配场景，目标时长: 5.20秒
2025-07-29 10:41:26,807 - INFO - 开始查找字幕序号 [302] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:26,807 - INFO - 找到related_overlap场景: scene_id=444, 字幕#302
2025-07-29 10:41:26,809 - INFO - 找到related_between场景: scene_id=443, 字幕#302
2025-07-29 10:41:26,810 - INFO - 字幕 #302 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:26,810 - INFO - 字幕序号 #302 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 10:41:26,810 - INFO - 选择第一个overlap场景作为起点: scene_id=444
2025-07-29 10:41:26,810 - INFO - 添加起点场景: scene_id=444, 时长=4.92秒, 累计时长=4.92秒
2025-07-29 10:41:26,810 - INFO - 起点场景时长不足，需要延伸填充 0.28秒
2025-07-29 10:41:26,810 - INFO - 起点场景在原始列表中的索引: 443
2025-07-29 10:41:26,810 - INFO - 延伸添加场景: scene_id=445 (裁剪至 0.28秒)
2025-07-29 10:41:26,810 - INFO - 累计时长: 5.20秒
2025-07-29 10:41:26,810 - INFO - 字幕序号 #302 场景匹配完成，共选择 2 个场景，总时长: 5.20秒
2025-07-29 10:41:26,810 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:26,810 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:26,810 - INFO - ========== 当前模式：为字幕 #52 生成 1 套场景方案 ==========
2025-07-29 10:41:26,810 - INFO - 开始查找字幕序号 [296, 302] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:26,810 - INFO - 找到related_overlap场景: scene_id=438, 字幕#296
2025-07-29 10:41:26,811 - INFO - 找到related_overlap场景: scene_id=444, 字幕#302
2025-07-29 10:41:26,812 - INFO - 找到related_between场景: scene_id=437, 字幕#296
2025-07-29 10:41:26,812 - INFO - 找到related_between场景: scene_id=443, 字幕#302
2025-07-29 10:41:26,812 - INFO - 字幕 #296 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:26,812 - INFO - 字幕 #302 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:26,812 - INFO - 共收集 2 个未使用的overlap场景和 2 个未使用的between场景
2025-07-29 10:41:26,812 - INFO - 开始生成方案 #1
2025-07-29 10:41:26,812 - INFO - 方案 #1: 为字幕#296选择初始化overlap场景id=438
2025-07-29 10:41:26,814 - INFO - 方案 #1: 为字幕#302选择初始化overlap场景id=444
2025-07-29 10:41:26,814 - INFO - 方案 #1: 初始选择后，当前总时长=7.32秒
2025-07-29 10:41:26,814 - INFO - 方案 #1: 额外between选择后，当前总时长=7.32秒
2025-07-29 10:41:26,814 - INFO - 方案 #1: 场景总时长(7.32秒)大于音频时长(5.20秒)，需要裁剪
2025-07-29 10:41:26,814 - INFO - 调整前总时长: 7.32秒, 目标时长: 5.20秒
2025-07-29 10:41:26,814 - INFO - 需要裁剪 2.12秒
2025-07-29 10:41:26,814 - INFO - 裁剪最长场景ID=444：从4.92秒裁剪至2.80秒
2025-07-29 10:41:26,814 - INFO - 调整后总时长: 5.20秒，与目标时长差异: 0.00秒
2025-07-29 10:41:26,814 - INFO - 方案 #1 调整/填充后最终总时长: 5.20秒
2025-07-29 10:41:26,814 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:26,814 - INFO - ========== 当前模式：字幕 #52 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:26,814 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:26,814 - INFO - ========== 新模式：字幕 #52 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:26,814 - INFO - 
----- 处理字幕 #52 的方案 #1 -----
2025-07-29 10:41:26,814 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-07-29 10:41:26,815 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpug29x5je
2025-07-29 10:41:26,816 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\438.mp4 (确认存在: True)
2025-07-29 10:41:26,816 - INFO - 添加场景ID=438，时长=2.40秒，累计时长=2.40秒
2025-07-29 10:41:26,816 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\439.mp4 (确认存在: True)
2025-07-29 10:41:26,816 - INFO - 添加场景ID=439，时长=2.20秒，累计时长=4.60秒
2025-07-29 10:41:26,816 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\440.mp4 (确认存在: True)
2025-07-29 10:41:26,816 - INFO - 添加场景ID=440，时长=1.48秒，累计时长=6.08秒
2025-07-29 10:41:26,816 - INFO - 准备合并 3 个场景文件，总时长约 6.08秒
2025-07-29 10:41:26,816 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/438.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/439.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/440.mp4'

2025-07-29 10:41:26,816 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpug29x5je\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpug29x5je\temp_combined.mp4
2025-07-29 10:41:26,981 - INFO - 合并后的视频时长: 6.15秒，目标音频时长: 5.20秒
2025-07-29 10:41:26,981 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpug29x5je\temp_combined.mp4 -ss 0 -to 5.196 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-07-29 10:41:27,387 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:27,388 - INFO - 目标音频时长: 5.20秒
2025-07-29 10:41:27,388 - INFO - 实际视频时长: 5.22秒
2025-07-29 10:41:27,388 - INFO - 时长差异: 0.03秒 (0.52%)
2025-07-29 10:41:27,388 - INFO - ==========================================
2025-07-29 10:41:27,388 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:27,388 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-07-29 10:41:27,389 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpug29x5je
2025-07-29 10:41:27,451 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:27,451 - INFO -   - 音频时长: 5.20秒
2025-07-29 10:41:27,451 - INFO -   - 视频时长: 5.22秒
2025-07-29 10:41:27,451 - INFO -   - 时长差异: 0.03秒 (0.52%)
2025-07-29 10:41:27,452 - INFO - 
----- 处理字幕 #52 的方案 #2 -----
2025-07-29 10:41:27,452 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-07-29 10:41:27,452 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpj0llly69
2025-07-29 10:41:27,453 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\444.mp4 (确认存在: True)
2025-07-29 10:41:27,453 - INFO - 添加场景ID=444，时长=4.92秒，累计时长=4.92秒
2025-07-29 10:41:27,453 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\445.mp4 (确认存在: True)
2025-07-29 10:41:27,453 - INFO - 添加场景ID=445，时长=2.20秒，累计时长=7.12秒
2025-07-29 10:41:27,453 - INFO - 准备合并 2 个场景文件，总时长约 7.12秒
2025-07-29 10:41:27,453 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/444.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/445.mp4'

2025-07-29 10:41:27,454 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpj0llly69\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpj0llly69\temp_combined.mp4
2025-07-29 10:41:27,615 - INFO - 合并后的视频时长: 7.17秒，目标音频时长: 5.20秒
2025-07-29 10:41:27,615 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpj0llly69\temp_combined.mp4 -ss 0 -to 5.196 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-07-29 10:41:28,063 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:28,063 - INFO - 目标音频时长: 5.20秒
2025-07-29 10:41:28,063 - INFO - 实际视频时长: 5.22秒
2025-07-29 10:41:28,063 - INFO - 时长差异: 0.03秒 (0.52%)
2025-07-29 10:41:28,063 - INFO - ==========================================
2025-07-29 10:41:28,063 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:28,064 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-07-29 10:41:28,065 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpj0llly69
2025-07-29 10:41:28,135 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:28,135 - INFO -   - 音频时长: 5.20秒
2025-07-29 10:41:28,135 - INFO -   - 视频时长: 5.22秒
2025-07-29 10:41:28,136 - INFO -   - 时长差异: 0.03秒 (0.52%)
2025-07-29 10:41:28,136 - INFO - 
----- 处理字幕 #52 的方案 #3 -----
2025-07-29 10:41:28,136 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\52_3.mp4
2025-07-29 10:41:28,136 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_nt868sk
2025-07-29 10:41:28,137 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\438.mp4 (确认存在: True)
2025-07-29 10:41:28,137 - INFO - 添加场景ID=438，时长=2.40秒，累计时长=2.40秒
2025-07-29 10:41:28,137 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\444.mp4 (确认存在: True)
2025-07-29 10:41:28,137 - INFO - 添加场景ID=444，时长=4.92秒，累计时长=7.32秒
2025-07-29 10:41:28,137 - INFO - 准备合并 2 个场景文件，总时长约 7.32秒
2025-07-29 10:41:28,137 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/438.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/444.mp4'

2025-07-29 10:41:28,138 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_nt868sk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_nt868sk\temp_combined.mp4
2025-07-29 10:41:28,316 - INFO - 合并后的视频时长: 7.37秒，目标音频时长: 5.20秒
2025-07-29 10:41:28,316 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_nt868sk\temp_combined.mp4 -ss 0 -to 5.196 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\52_3.mp4
2025-07-29 10:41:28,738 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:28,738 - INFO - 目标音频时长: 5.20秒
2025-07-29 10:41:28,738 - INFO - 实际视频时长: 5.22秒
2025-07-29 10:41:28,738 - INFO - 时长差异: 0.03秒 (0.52%)
2025-07-29 10:41:28,738 - INFO - ==========================================
2025-07-29 10:41:28,738 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:28,738 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\52_3.mp4
2025-07-29 10:41:28,740 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_nt868sk
2025-07-29 10:41:28,807 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:28,807 - INFO -   - 音频时长: 5.20秒
2025-07-29 10:41:28,807 - INFO -   - 视频时长: 5.22秒
2025-07-29 10:41:28,807 - INFO -   - 时长差异: 0.03秒 (0.52%)
2025-07-29 10:41:28,808 - INFO - 
字幕 #52 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:28,808 - INFO - 生成的视频文件:
2025-07-29 10:41:28,808 - INFO -   1. F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-07-29 10:41:28,808 - INFO -   2. F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-07-29 10:41:28,808 - INFO -   3. F:/github/aicut_auto/newcut_ai\52_3.mp4
2025-07-29 10:41:28,808 - INFO - ========== 字幕 #52 处理结束 ==========

