2025-07-29 10:40:25,634 - INFO - ========== 字幕 #8 处理开始 ==========
2025-07-29 10:40:25,634 - INFO - 字幕内容: 哥哥说她年纪小，被祖母斥责他放浪形骸。
2025-07-29 10:40:25,634 - INFO - 字幕序号: [30, 34]
2025-07-29 10:40:25,634 - INFO - 音频文件详情:
2025-07-29 10:40:25,634 - INFO -   - 路径: output\8.wav
2025-07-29 10:40:25,634 - INFO -   - 时长: 3.13秒
2025-07-29 10:40:25,634 - INFO -   - 验证音频时长: 3.13秒
2025-07-29 10:40:25,634 - INFO - 字幕时间戳信息:
2025-07-29 10:40:25,634 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:25,634 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:25,634 - INFO -   - 根据生成的音频时长(3.13秒)已调整字幕时间戳
2025-07-29 10:40:25,635 - INFO - ========== 新模式：为字幕 #8 生成4套场景方案 ==========
2025-07-29 10:40:25,635 - INFO - 字幕序号列表: [30, 34]
2025-07-29 10:40:25,635 - INFO - 
--- 生成方案 #1：基于字幕序号 #30 ---
2025-07-29 10:40:25,635 - INFO - 开始为单个字幕序号 #30 匹配场景，目标时长: 3.13秒
2025-07-29 10:40:25,635 - INFO - 开始查找字幕序号 [30] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:25,635 - INFO - 找到related_overlap场景: scene_id=47, 字幕#30
2025-07-29 10:40:25,635 - INFO - 找到related_overlap场景: scene_id=48, 字幕#30
2025-07-29 10:40:25,635 - INFO - 找到related_between场景: scene_id=44, 字幕#30
2025-07-29 10:40:25,635 - INFO - 找到related_between场景: scene_id=45, 字幕#30
2025-07-29 10:40:25,635 - INFO - 找到related_between场景: scene_id=46, 字幕#30
2025-07-29 10:40:25,636 - INFO - 字幕 #30 找到 2 个overlap场景, 3 个between场景
2025-07-29 10:40:25,636 - INFO - 字幕序号 #30 找到 2 个可用overlap场景, 3 个可用between场景
2025-07-29 10:40:25,636 - INFO - 选择第一个overlap场景作为起点: scene_id=47
2025-07-29 10:40:25,636 - INFO - 添加起点场景: scene_id=47, 时长=1.00秒, 累计时长=1.00秒
2025-07-29 10:40:25,636 - INFO - 起点场景时长不足，需要延伸填充 2.13秒
2025-07-29 10:40:25,636 - INFO - 起点场景在原始列表中的索引: 46
2025-07-29 10:40:25,636 - INFO - 延伸添加场景: scene_id=48 (完整时长 1.00秒)
2025-07-29 10:40:25,636 - INFO - 累计时长: 2.00秒
2025-07-29 10:40:25,636 - INFO - 延伸添加场景: scene_id=49 (裁剪至 1.13秒)
2025-07-29 10:40:25,636 - INFO - 累计时长: 3.13秒
2025-07-29 10:40:25,636 - INFO - 字幕序号 #30 场景匹配完成，共选择 3 个场景，总时长: 3.13秒
2025-07-29 10:40:25,636 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:40:25,636 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:40:25,636 - INFO - 
--- 生成方案 #2：基于字幕序号 #34 ---
2025-07-29 10:40:25,636 - INFO - 开始为单个字幕序号 #34 匹配场景，目标时长: 3.13秒
2025-07-29 10:40:25,636 - INFO - 开始查找字幕序号 [34] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:25,636 - INFO - 找到related_overlap场景: scene_id=51, 字幕#34
2025-07-29 10:40:25,637 - INFO - 字幕 #34 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:25,637 - INFO - 字幕序号 #34 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:25,637 - INFO - 选择第一个overlap场景作为起点: scene_id=51
2025-07-29 10:40:25,637 - INFO - 添加起点场景: scene_id=51, 时长=2.44秒, 累计时长=2.44秒
2025-07-29 10:40:25,637 - INFO - 起点场景时长不足，需要延伸填充 0.69秒
2025-07-29 10:40:25,637 - INFO - 起点场景在原始列表中的索引: 50
2025-07-29 10:40:25,637 - INFO - 延伸添加场景: scene_id=52 (裁剪至 0.69秒)
2025-07-29 10:40:25,638 - INFO - 累计时长: 3.13秒
2025-07-29 10:40:25,638 - INFO - 字幕序号 #34 场景匹配完成，共选择 2 个场景，总时长: 3.13秒
2025-07-29 10:40:25,638 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:25,638 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:25,638 - INFO - ========== 当前模式：为字幕 #8 生成 1 套场景方案 ==========
2025-07-29 10:40:25,638 - INFO - 开始查找字幕序号 [30, 34] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:25,638 - INFO - 找到related_overlap场景: scene_id=47, 字幕#30
2025-07-29 10:40:25,638 - INFO - 找到related_overlap场景: scene_id=48, 字幕#30
2025-07-29 10:40:25,638 - INFO - 找到related_overlap场景: scene_id=51, 字幕#34
2025-07-29 10:40:25,638 - INFO - 找到related_between场景: scene_id=44, 字幕#30
2025-07-29 10:40:25,638 - INFO - 找到related_between场景: scene_id=45, 字幕#30
2025-07-29 10:40:25,638 - INFO - 找到related_between场景: scene_id=46, 字幕#30
2025-07-29 10:40:25,638 - INFO - 字幕 #30 找到 2 个overlap场景, 3 个between场景
2025-07-29 10:40:25,638 - INFO - 字幕 #34 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:25,638 - INFO - 共收集 3 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 10:40:25,638 - INFO - 开始生成方案 #1
2025-07-29 10:40:25,638 - INFO - 方案 #1: 为字幕#30选择初始化overlap场景id=48
2025-07-29 10:40:25,638 - INFO - 方案 #1: 为字幕#34选择初始化overlap场景id=51
2025-07-29 10:40:25,638 - INFO - 方案 #1: 初始选择后，当前总时长=3.44秒
2025-07-29 10:40:25,638 - INFO - 方案 #1: 额外between选择后，当前总时长=3.44秒
2025-07-29 10:40:25,638 - INFO - 方案 #1: 场景总时长(3.44秒)大于音频时长(3.13秒)，需要裁剪
2025-07-29 10:40:25,638 - INFO - 调整前总时长: 3.44秒, 目标时长: 3.13秒
2025-07-29 10:40:25,638 - INFO - 需要裁剪 0.31秒
2025-07-29 10:40:25,638 - INFO - 裁剪最长场景ID=51：从2.44秒裁剪至2.13秒
2025-07-29 10:40:25,638 - INFO - 调整后总时长: 3.13秒，与目标时长差异: 0.00秒
2025-07-29 10:40:25,638 - INFO - 方案 #1 调整/填充后最终总时长: 3.13秒
2025-07-29 10:40:25,638 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:25,639 - INFO - ========== 当前模式：字幕 #8 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:25,639 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:25,639 - INFO - ========== 新模式：字幕 #8 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:25,639 - INFO - 
----- 处理字幕 #8 的方案 #1 -----
2025-07-29 10:40:25,639 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-29 10:40:25,639 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpifusobtd
2025-07-29 10:40:25,639 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\47.mp4 (确认存在: True)
2025-07-29 10:40:25,639 - INFO - 添加场景ID=47，时长=1.00秒，累计时长=1.00秒
2025-07-29 10:40:25,640 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\48.mp4 (确认存在: True)
2025-07-29 10:40:25,640 - INFO - 添加场景ID=48，时长=1.00秒，累计时长=2.00秒
2025-07-29 10:40:25,640 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\49.mp4 (确认存在: True)
2025-07-29 10:40:25,640 - INFO - 添加场景ID=49，时长=1.76秒，累计时长=3.76秒
2025-07-29 10:40:25,640 - INFO - 准备合并 3 个场景文件，总时长约 3.76秒
2025-07-29 10:40:25,640 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/47.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/48.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/49.mp4'

2025-07-29 10:40:25,640 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpifusobtd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpifusobtd\temp_combined.mp4
2025-07-29 10:40:25,784 - INFO - 合并后的视频时长: 3.83秒，目标音频时长: 3.13秒
2025-07-29 10:40:25,784 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpifusobtd\temp_combined.mp4 -ss 0 -to 3.131 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-29 10:40:26,065 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:26,065 - INFO - 目标音频时长: 3.13秒
2025-07-29 10:40:26,065 - INFO - 实际视频时长: 3.18秒
2025-07-29 10:40:26,065 - INFO - 时长差异: 0.05秒 (1.66%)
2025-07-29 10:40:26,065 - INFO - ==========================================
2025-07-29 10:40:26,065 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:26,065 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-29 10:40:26,066 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpifusobtd
2025-07-29 10:40:26,110 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:26,110 - INFO -   - 音频时长: 3.13秒
2025-07-29 10:40:26,110 - INFO -   - 视频时长: 3.18秒
2025-07-29 10:40:26,110 - INFO -   - 时长差异: 0.05秒 (1.66%)
2025-07-29 10:40:26,110 - INFO - 
----- 处理字幕 #8 的方案 #2 -----
2025-07-29 10:40:26,110 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-07-29 10:40:26,111 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjn097_ni
2025-07-29 10:40:26,111 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\51.mp4 (确认存在: True)
2025-07-29 10:40:26,111 - INFO - 添加场景ID=51，时长=2.44秒，累计时长=2.44秒
2025-07-29 10:40:26,111 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\52.mp4 (确认存在: True)
2025-07-29 10:40:26,111 - INFO - 添加场景ID=52，时长=1.36秒，累计时长=3.80秒
2025-07-29 10:40:26,112 - INFO - 准备合并 2 个场景文件，总时长约 3.80秒
2025-07-29 10:40:26,112 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/51.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/52.mp4'

2025-07-29 10:40:26,112 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjn097_ni\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjn097_ni\temp_combined.mp4
2025-07-29 10:40:26,237 - INFO - 合并后的视频时长: 3.85秒，目标音频时长: 3.13秒
2025-07-29 10:40:26,237 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjn097_ni\temp_combined.mp4 -ss 0 -to 3.131 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-07-29 10:40:26,491 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:26,491 - INFO - 目标音频时长: 3.13秒
2025-07-29 10:40:26,491 - INFO - 实际视频时长: 3.18秒
2025-07-29 10:40:26,491 - INFO - 时长差异: 0.05秒 (1.66%)
2025-07-29 10:40:26,491 - INFO - ==========================================
2025-07-29 10:40:26,491 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:26,491 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-07-29 10:40:26,492 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjn097_ni
2025-07-29 10:40:26,535 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:26,535 - INFO -   - 音频时长: 3.13秒
2025-07-29 10:40:26,535 - INFO -   - 视频时长: 3.18秒
2025-07-29 10:40:26,535 - INFO -   - 时长差异: 0.05秒 (1.66%)
2025-07-29 10:40:26,535 - INFO - 
----- 处理字幕 #8 的方案 #3 -----
2025-07-29 10:40:26,535 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\8_3.mp4
2025-07-29 10:40:26,535 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5f_8s0wc
2025-07-29 10:40:26,536 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\48.mp4 (确认存在: True)
2025-07-29 10:40:26,536 - INFO - 添加场景ID=48，时长=1.00秒，累计时长=1.00秒
2025-07-29 10:40:26,536 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\51.mp4 (确认存在: True)
2025-07-29 10:40:26,536 - INFO - 添加场景ID=51，时长=2.44秒，累计时长=3.44秒
2025-07-29 10:40:26,536 - INFO - 准备合并 2 个场景文件，总时长约 3.44秒
2025-07-29 10:40:26,536 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/48.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/51.mp4'

2025-07-29 10:40:26,536 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5f_8s0wc\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5f_8s0wc\temp_combined.mp4
2025-07-29 10:40:26,658 - INFO - 合并后的视频时长: 3.49秒，目标音频时长: 3.13秒
2025-07-29 10:40:26,659 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5f_8s0wc\temp_combined.mp4 -ss 0 -to 3.131 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\8_3.mp4
