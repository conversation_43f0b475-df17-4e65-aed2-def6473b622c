2025-07-29 10:41:00,961 - INFO - ========== 字幕 #35 处理开始 ==========
2025-07-29 10:41:00,962 - INFO - 字幕内容: 绝望中，她急中生智，谎称是摄政王的爱妾。
2025-07-29 10:41:00,962 - INFO - 字幕序号: [202, 208]
2025-07-29 10:41:00,962 - INFO - 音频文件详情:
2025-07-29 10:41:00,962 - INFO -   - 路径: output\35.wav
2025-07-29 10:41:00,962 - INFO -   - 时长: 4.41秒
2025-07-29 10:41:00,962 - INFO -   - 验证音频时长: 4.41秒
2025-07-29 10:41:00,962 - INFO - 字幕时间戳信息:
2025-07-29 10:41:00,962 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:00,962 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:00,962 - INFO -   - 根据生成的音频时长(4.41秒)已调整字幕时间戳
2025-07-29 10:41:00,962 - INFO - ========== 新模式：为字幕 #35 生成4套场景方案 ==========
2025-07-29 10:41:00,962 - INFO - 字幕序号列表: [202, 208]
2025-07-29 10:41:00,962 - INFO - 
--- 生成方案 #1：基于字幕序号 #202 ---
2025-07-29 10:41:00,962 - INFO - 开始为单个字幕序号 #202 匹配场景，目标时长: 4.41秒
2025-07-29 10:41:00,962 - INFO - 开始查找字幕序号 [202] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:00,962 - INFO - 找到related_overlap场景: scene_id=281, 字幕#202
2025-07-29 10:41:00,962 - INFO - 找到related_overlap场景: scene_id=282, 字幕#202
2025-07-29 10:41:00,964 - INFO - 字幕 #202 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:00,964 - INFO - 字幕序号 #202 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:00,964 - INFO - 选择第一个overlap场景作为起点: scene_id=281
2025-07-29 10:41:00,964 - INFO - 添加起点场景: scene_id=281, 时长=2.00秒, 累计时长=2.00秒
2025-07-29 10:41:00,964 - INFO - 起点场景时长不足，需要延伸填充 2.41秒
2025-07-29 10:41:00,964 - INFO - 起点场景在原始列表中的索引: 280
2025-07-29 10:41:00,964 - INFO - 延伸添加场景: scene_id=282 (完整时长 1.68秒)
2025-07-29 10:41:00,964 - INFO - 累计时长: 3.68秒
2025-07-29 10:41:00,964 - INFO - 延伸添加场景: scene_id=283 (裁剪至 0.73秒)
2025-07-29 10:41:00,964 - INFO - 累计时长: 4.41秒
2025-07-29 10:41:00,964 - INFO - 字幕序号 #202 场景匹配完成，共选择 3 个场景，总时长: 4.41秒
2025-07-29 10:41:00,964 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:00,964 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:00,964 - INFO - 
--- 生成方案 #2：基于字幕序号 #208 ---
2025-07-29 10:41:00,964 - INFO - 开始为单个字幕序号 #208 匹配场景，目标时长: 4.41秒
2025-07-29 10:41:00,964 - INFO - 开始查找字幕序号 [208] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:00,964 - INFO - 找到related_overlap场景: scene_id=286, 字幕#208
2025-07-29 10:41:00,965 - INFO - 字幕 #208 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:00,965 - INFO - 字幕序号 #208 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:00,965 - INFO - 选择第一个overlap场景作为起点: scene_id=286
2025-07-29 10:41:00,965 - INFO - 添加起点场景: scene_id=286, 时长=1.36秒, 累计时长=1.36秒
2025-07-29 10:41:00,965 - INFO - 起点场景时长不足，需要延伸填充 3.05秒
2025-07-29 10:41:00,965 - INFO - 起点场景在原始列表中的索引: 285
2025-07-29 10:41:00,965 - INFO - 延伸添加场景: scene_id=287 (裁剪至 3.05秒)
2025-07-29 10:41:00,965 - INFO - 累计时长: 4.41秒
2025-07-29 10:41:00,965 - INFO - 字幕序号 #208 场景匹配完成，共选择 2 个场景，总时长: 4.41秒
2025-07-29 10:41:00,965 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:00,965 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:00,965 - INFO - ========== 当前模式：为字幕 #35 生成 1 套场景方案 ==========
2025-07-29 10:41:00,965 - INFO - 开始查找字幕序号 [202, 208] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:00,965 - INFO - 找到related_overlap场景: scene_id=281, 字幕#202
2025-07-29 10:41:00,965 - INFO - 找到related_overlap场景: scene_id=282, 字幕#202
2025-07-29 10:41:00,965 - INFO - 找到related_overlap场景: scene_id=286, 字幕#208
2025-07-29 10:41:00,966 - INFO - 字幕 #202 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:00,966 - INFO - 字幕 #208 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:00,966 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:00,966 - INFO - 开始生成方案 #1
2025-07-29 10:41:00,966 - INFO - 方案 #1: 为字幕#202选择初始化overlap场景id=281
2025-07-29 10:41:00,966 - INFO - 方案 #1: 为字幕#208选择初始化overlap场景id=286
2025-07-29 10:41:00,966 - INFO - 方案 #1: 初始选择后，当前总时长=3.36秒
2025-07-29 10:41:00,967 - INFO - 方案 #1: 额外添加overlap场景id=282, 当前总时长=5.04秒
2025-07-29 10:41:00,967 - INFO - 方案 #1: 额外between选择后，当前总时长=5.04秒
2025-07-29 10:41:00,967 - INFO - 方案 #1: 场景总时长(5.04秒)大于音频时长(4.41秒)，需要裁剪
2025-07-29 10:41:00,967 - INFO - 调整前总时长: 5.04秒, 目标时长: 4.41秒
2025-07-29 10:41:00,967 - INFO - 需要裁剪 0.63秒
2025-07-29 10:41:00,967 - INFO - 裁剪最长场景ID=281：从2.00秒裁剪至1.37秒
2025-07-29 10:41:00,967 - INFO - 调整后总时长: 4.41秒，与目标时长差异: 0.00秒
2025-07-29 10:41:00,967 - INFO - 方案 #1 调整/填充后最终总时长: 4.41秒
2025-07-29 10:41:00,967 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:00,967 - INFO - ========== 当前模式：字幕 #35 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:00,967 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:00,967 - INFO - ========== 新模式：字幕 #35 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:00,967 - INFO - 
----- 处理字幕 #35 的方案 #1 -----
2025-07-29 10:41:00,967 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-29 10:41:00,967 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw0ry3mb7
2025-07-29 10:41:00,968 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\281.mp4 (确认存在: True)
2025-07-29 10:41:00,968 - INFO - 添加场景ID=281，时长=2.00秒，累计时长=2.00秒
2025-07-29 10:41:00,968 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\282.mp4 (确认存在: True)
2025-07-29 10:41:00,968 - INFO - 添加场景ID=282，时长=1.68秒，累计时长=3.68秒
2025-07-29 10:41:00,968 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\283.mp4 (确认存在: True)
2025-07-29 10:41:00,968 - INFO - 添加场景ID=283，时长=3.88秒，累计时长=7.56秒
2025-07-29 10:41:00,968 - INFO - 场景总时长(7.56秒)已达到音频时长(4.41秒)的1.5倍，停止添加场景
2025-07-29 10:41:00,968 - INFO - 准备合并 3 个场景文件，总时长约 7.56秒
2025-07-29 10:41:00,968 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/281.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/282.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/283.mp4'

2025-07-29 10:41:00,968 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpw0ry3mb7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpw0ry3mb7\temp_combined.mp4
2025-07-29 10:41:01,083 - INFO - 合并后的视频时长: 7.63秒，目标音频时长: 4.41秒
2025-07-29 10:41:01,083 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpw0ry3mb7\temp_combined.mp4 -ss 0 -to 4.412 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-29 10:41:01,380 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:01,380 - INFO - 目标音频时长: 4.41秒
2025-07-29 10:41:01,380 - INFO - 实际视频时长: 4.46秒
2025-07-29 10:41:01,380 - INFO - 时长差异: 0.05秒 (1.16%)
2025-07-29 10:41:01,380 - INFO - ==========================================
2025-07-29 10:41:01,380 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:01,380 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-29 10:41:01,381 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw0ry3mb7
2025-07-29 10:41:01,423 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:01,423 - INFO -   - 音频时长: 4.41秒
2025-07-29 10:41:01,423 - INFO -   - 视频时长: 4.46秒
2025-07-29 10:41:01,423 - INFO -   - 时长差异: 0.05秒 (1.16%)
2025-07-29 10:41:01,423 - INFO - 
----- 处理字幕 #35 的方案 #2 -----
2025-07-29 10:41:01,423 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-29 10:41:01,423 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmyt9fxo4
2025-07-29 10:41:01,424 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\286.mp4 (确认存在: True)
2025-07-29 10:41:01,424 - INFO - 添加场景ID=286，时长=1.36秒，累计时长=1.36秒
2025-07-29 10:41:01,424 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\287.mp4 (确认存在: True)
2025-07-29 10:41:01,424 - INFO - 添加场景ID=287，时长=3.40秒，累计时长=4.76秒
2025-07-29 10:41:01,424 - INFO - 准备合并 2 个场景文件，总时长约 4.76秒
2025-07-29 10:41:01,424 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/286.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/287.mp4'

2025-07-29 10:41:01,424 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmyt9fxo4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmyt9fxo4\temp_combined.mp4
2025-07-29 10:41:01,539 - INFO - 合并后的视频时长: 4.81秒，目标音频时长: 4.41秒
2025-07-29 10:41:01,539 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmyt9fxo4\temp_combined.mp4 -ss 0 -to 4.412 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-29 10:41:01,823 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:01,823 - INFO - 目标音频时长: 4.41秒
2025-07-29 10:41:01,823 - INFO - 实际视频时长: 4.46秒
2025-07-29 10:41:01,823 - INFO - 时长差异: 0.05秒 (1.16%)
2025-07-29 10:41:01,823 - INFO - ==========================================
2025-07-29 10:41:01,823 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:01,823 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-29 10:41:01,824 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmyt9fxo4
2025-07-29 10:41:01,865 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:01,865 - INFO -   - 音频时长: 4.41秒
2025-07-29 10:41:01,865 - INFO -   - 视频时长: 4.46秒
2025-07-29 10:41:01,865 - INFO -   - 时长差异: 0.05秒 (1.16%)
2025-07-29 10:41:01,865 - INFO - 
----- 处理字幕 #35 的方案 #3 -----
2025-07-29 10:41:01,865 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-29 10:41:01,865 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc_i2dg2t
2025-07-29 10:41:01,866 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\281.mp4 (确认存在: True)
2025-07-29 10:41:01,866 - INFO - 添加场景ID=281，时长=2.00秒，累计时长=2.00秒
2025-07-29 10:41:01,866 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\286.mp4 (确认存在: True)
2025-07-29 10:41:01,866 - INFO - 添加场景ID=286，时长=1.36秒，累计时长=3.36秒
2025-07-29 10:41:01,866 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\282.mp4 (确认存在: True)
2025-07-29 10:41:01,866 - INFO - 添加场景ID=282，时长=1.68秒，累计时长=5.04秒
2025-07-29 10:41:01,866 - INFO - 准备合并 3 个场景文件，总时长约 5.04秒
2025-07-29 10:41:01,866 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/281.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/286.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/282.mp4'

2025-07-29 10:41:01,866 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpc_i2dg2t\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpc_i2dg2t\temp_combined.mp4
2025-07-29 10:41:01,997 - INFO - 合并后的视频时长: 5.11秒，目标音频时长: 4.41秒
2025-07-29 10:41:01,997 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpc_i2dg2t\temp_combined.mp4 -ss 0 -to 4.412 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-29 10:41:02,311 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:02,311 - INFO - 目标音频时长: 4.41秒
2025-07-29 10:41:02,311 - INFO - 实际视频时长: 4.46秒
2025-07-29 10:41:02,311 - INFO - 时长差异: 0.05秒 (1.16%)
2025-07-29 10:41:02,311 - INFO - ==========================================
2025-07-29 10:41:02,311 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:02,311 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-29 10:41:02,312 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc_i2dg2t
2025-07-29 10:41:02,353 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:02,353 - INFO -   - 音频时长: 4.41秒
2025-07-29 10:41:02,353 - INFO -   - 视频时长: 4.46秒
2025-07-29 10:41:02,353 - INFO -   - 时长差异: 0.05秒 (1.16%)
2025-07-29 10:41:02,354 - INFO - 
字幕 #35 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:02,354 - INFO - 生成的视频文件:
2025-07-29 10:41:02,354 - INFO -   1. F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-29 10:41:02,354 - INFO -   2. F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-29 10:41:02,354 - INFO -   3. F:/github/aicut_auto/newcut_ai\35_3.mp4
2025-07-29 10:41:02,354 - INFO - ========== 字幕 #35 处理结束 ==========

