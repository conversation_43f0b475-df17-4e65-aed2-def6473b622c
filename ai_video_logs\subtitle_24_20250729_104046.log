2025-07-29 10:40:46,397 - INFO - ========== 字幕 #24 处理开始 ==========
2025-07-29 10:40:46,397 - INFO - 字幕内容: 原来他嘴上说着要让她经历风雨，却还是不忍。
2025-07-29 10:40:46,397 - INFO - 字幕序号: [128, 132]
2025-07-29 10:40:46,397 - INFO - 音频文件详情:
2025-07-29 10:40:46,397 - INFO -   - 路径: output\24.wav
2025-07-29 10:40:46,397 - INFO -   - 时长: 3.12秒
2025-07-29 10:40:46,397 - INFO -   - 验证音频时长: 3.12秒
2025-07-29 10:40:46,397 - INFO - 字幕时间戳信息:
2025-07-29 10:40:46,397 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:46,397 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:46,397 - INFO -   - 根据生成的音频时长(3.12秒)已调整字幕时间戳
2025-07-29 10:40:46,397 - INFO - ========== 新模式：为字幕 #24 生成4套场景方案 ==========
2025-07-29 10:40:46,397 - INFO - 字幕序号列表: [128, 132]
2025-07-29 10:40:46,397 - INFO - 
--- 生成方案 #1：基于字幕序号 #128 ---
2025-07-29 10:40:46,397 - INFO - 开始为单个字幕序号 #128 匹配场景，目标时长: 3.12秒
2025-07-29 10:40:46,397 - INFO - 开始查找字幕序号 [128] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:46,397 - INFO - 找到related_overlap场景: scene_id=185, 字幕#128
2025-07-29 10:40:46,398 - INFO - 找到related_between场景: scene_id=182, 字幕#128
2025-07-29 10:40:46,398 - INFO - 找到related_between场景: scene_id=183, 字幕#128
2025-07-29 10:40:46,398 - INFO - 找到related_between场景: scene_id=184, 字幕#128
2025-07-29 10:40:46,399 - INFO - 字幕 #128 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:40:46,399 - INFO - 字幕序号 #128 找到 1 个可用overlap场景, 3 个可用between场景
2025-07-29 10:40:46,399 - INFO - 选择第一个overlap场景作为起点: scene_id=185
2025-07-29 10:40:46,399 - INFO - 添加起点场景: scene_id=185, 时长=3.16秒, 累计时长=3.16秒
2025-07-29 10:40:46,399 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:40:46,399 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:40:46,399 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:40:46,399 - INFO - 
--- 生成方案 #2：基于字幕序号 #132 ---
2025-07-29 10:40:46,399 - INFO - 开始为单个字幕序号 #132 匹配场景，目标时长: 3.12秒
2025-07-29 10:40:46,399 - INFO - 开始查找字幕序号 [132] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:46,399 - INFO - 找到related_overlap场景: scene_id=187, 字幕#132
2025-07-29 10:40:46,399 - INFO - 找到related_between场景: scene_id=188, 字幕#132
2025-07-29 10:40:46,399 - INFO - 找到related_between场景: scene_id=189, 字幕#132
2025-07-29 10:40:46,399 - INFO - 找到related_between场景: scene_id=190, 字幕#132
2025-07-29 10:40:46,399 - INFO - 找到related_between场景: scene_id=191, 字幕#132
2025-07-29 10:40:46,400 - INFO - 字幕 #132 找到 1 个overlap场景, 4 个between场景
2025-07-29 10:40:46,400 - INFO - 字幕序号 #132 找到 1 个可用overlap场景, 4 个可用between场景
2025-07-29 10:40:46,400 - INFO - 选择第一个overlap场景作为起点: scene_id=187
2025-07-29 10:40:46,400 - INFO - 添加起点场景: scene_id=187, 时长=2.60秒, 累计时长=2.60秒
2025-07-29 10:40:46,400 - INFO - 起点场景时长不足，需要延伸填充 0.52秒
2025-07-29 10:40:46,400 - INFO - 起点场景在原始列表中的索引: 186
2025-07-29 10:40:46,400 - INFO - 延伸添加场景: scene_id=188 (裁剪至 0.52秒)
2025-07-29 10:40:46,400 - INFO - 累计时长: 3.12秒
2025-07-29 10:40:46,400 - INFO - 字幕序号 #132 场景匹配完成，共选择 2 个场景，总时长: 3.12秒
2025-07-29 10:40:46,400 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:46,400 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:46,400 - INFO - ========== 当前模式：为字幕 #24 生成 1 套场景方案 ==========
2025-07-29 10:40:46,400 - INFO - 开始查找字幕序号 [128, 132] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:46,400 - INFO - 找到related_overlap场景: scene_id=185, 字幕#128
2025-07-29 10:40:46,400 - INFO - 找到related_overlap场景: scene_id=187, 字幕#132
2025-07-29 10:40:46,401 - INFO - 找到related_between场景: scene_id=182, 字幕#128
2025-07-29 10:40:46,401 - INFO - 找到related_between场景: scene_id=183, 字幕#128
2025-07-29 10:40:46,401 - INFO - 找到related_between场景: scene_id=184, 字幕#128
2025-07-29 10:40:46,401 - INFO - 找到related_between场景: scene_id=188, 字幕#132
2025-07-29 10:40:46,401 - INFO - 找到related_between场景: scene_id=189, 字幕#132
2025-07-29 10:40:46,401 - INFO - 找到related_between场景: scene_id=190, 字幕#132
2025-07-29 10:40:46,401 - INFO - 找到related_between场景: scene_id=191, 字幕#132
2025-07-29 10:40:46,402 - INFO - 字幕 #128 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:40:46,402 - INFO - 字幕 #132 找到 1 个overlap场景, 4 个between场景
2025-07-29 10:40:46,402 - INFO - 共收集 2 个未使用的overlap场景和 7 个未使用的between场景
2025-07-29 10:40:46,402 - INFO - 开始生成方案 #1
2025-07-29 10:40:46,402 - INFO - 方案 #1: 为字幕#128选择初始化overlap场景id=185
2025-07-29 10:40:46,402 - INFO - 方案 #1: 为字幕#132选择初始化overlap场景id=187
2025-07-29 10:40:46,402 - INFO - 方案 #1: 初始选择后，当前总时长=5.76秒
2025-07-29 10:40:46,402 - INFO - 方案 #1: 额外between选择后，当前总时长=5.76秒
2025-07-29 10:40:46,402 - INFO - 方案 #1: 场景总时长(5.76秒)大于音频时长(3.12秒)，需要裁剪
2025-07-29 10:40:46,402 - INFO - 调整前总时长: 5.76秒, 目标时长: 3.12秒
2025-07-29 10:40:46,402 - INFO - 需要裁剪 2.64秒
2025-07-29 10:40:46,402 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:40:46,402 - INFO - 裁剪场景ID=185：从3.16秒裁剪至1.00秒
2025-07-29 10:40:46,402 - INFO - 裁剪场景ID=187：从2.60秒裁剪至2.12秒
2025-07-29 10:40:46,402 - INFO - 调整后总时长: 3.12秒，与目标时长差异: 0.00秒
2025-07-29 10:40:46,402 - INFO - 方案 #1 调整/填充后最终总时长: 3.12秒
2025-07-29 10:40:46,402 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:46,402 - INFO - ========== 当前模式：字幕 #24 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:46,402 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:46,402 - INFO - ========== 新模式：字幕 #24 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:46,402 - INFO - 
----- 处理字幕 #24 的方案 #1 -----
2025-07-29 10:40:46,402 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-29 10:40:46,402 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnyo6g15d
2025-07-29 10:40:46,403 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\185.mp4 (确认存在: True)
2025-07-29 10:40:46,403 - INFO - 添加场景ID=185，时长=3.16秒，累计时长=3.16秒
2025-07-29 10:40:46,403 - INFO - 准备合并 1 个场景文件，总时长约 3.16秒
2025-07-29 10:40:46,403 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/185.mp4'

2025-07-29 10:40:46,403 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnyo6g15d\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnyo6g15d\temp_combined.mp4
2025-07-29 10:40:46,511 - INFO - 合并后的视频时长: 3.18秒，目标音频时长: 3.12秒
2025-07-29 10:40:46,511 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnyo6g15d\temp_combined.mp4 -ss 0 -to 3.118 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-29 10:40:46,760 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:46,760 - INFO - 目标音频时长: 3.12秒
2025-07-29 10:40:46,760 - INFO - 实际视频时长: 3.14秒
2025-07-29 10:40:46,760 - INFO - 时长差异: 0.02秒 (0.80%)
2025-07-29 10:40:46,760 - INFO - ==========================================
2025-07-29 10:40:46,760 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:46,760 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-29 10:40:46,761 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnyo6g15d
2025-07-29 10:40:46,804 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:46,804 - INFO -   - 音频时长: 3.12秒
2025-07-29 10:40:46,804 - INFO -   - 视频时长: 3.14秒
2025-07-29 10:40:46,804 - INFO -   - 时长差异: 0.02秒 (0.80%)
2025-07-29 10:40:46,804 - INFO - 
----- 处理字幕 #24 的方案 #2 -----
2025-07-29 10:40:46,804 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-29 10:40:46,804 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc1dm0vzw
2025-07-29 10:40:46,805 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\187.mp4 (确认存在: True)
2025-07-29 10:40:46,805 - INFO - 添加场景ID=187，时长=2.60秒，累计时长=2.60秒
2025-07-29 10:40:46,805 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\188.mp4 (确认存在: True)
2025-07-29 10:40:46,805 - INFO - 添加场景ID=188，时长=1.00秒，累计时长=3.60秒
2025-07-29 10:40:46,805 - INFO - 准备合并 2 个场景文件，总时长约 3.60秒
2025-07-29 10:40:46,805 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/187.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/188.mp4'

2025-07-29 10:40:46,805 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpc1dm0vzw\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpc1dm0vzw\temp_combined.mp4
2025-07-29 10:40:46,932 - INFO - 合并后的视频时长: 3.65秒，目标音频时长: 3.12秒
2025-07-29 10:40:46,932 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpc1dm0vzw\temp_combined.mp4 -ss 0 -to 3.118 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-29 10:40:47,162 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:47,162 - INFO - 目标音频时长: 3.12秒
2025-07-29 10:40:47,162 - INFO - 实际视频时长: 3.14秒
2025-07-29 10:40:47,163 - INFO - 时长差异: 0.02秒 (0.80%)
2025-07-29 10:40:47,163 - INFO - ==========================================
2025-07-29 10:40:47,163 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:47,163 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-29 10:40:47,163 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc1dm0vzw
2025-07-29 10:40:47,206 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:47,206 - INFO -   - 音频时长: 3.12秒
2025-07-29 10:40:47,206 - INFO -   - 视频时长: 3.14秒
2025-07-29 10:40:47,206 - INFO -   - 时长差异: 0.02秒 (0.80%)
2025-07-29 10:40:47,206 - INFO - 
----- 处理字幕 #24 的方案 #3 -----
2025-07-29 10:40:47,206 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-29 10:40:47,206 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6x7_52mz
2025-07-29 10:40:47,207 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\185.mp4 (确认存在: True)
2025-07-29 10:40:47,207 - INFO - 添加场景ID=185，时长=3.16秒，累计时长=3.16秒
2025-07-29 10:40:47,207 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\187.mp4 (确认存在: True)
2025-07-29 10:40:47,207 - INFO - 添加场景ID=187，时长=2.60秒，累计时长=5.76秒
2025-07-29 10:40:47,207 - INFO - 场景总时长(5.76秒)已达到音频时长(3.12秒)的1.5倍，停止添加场景
2025-07-29 10:40:47,207 - INFO - 准备合并 2 个场景文件，总时长约 5.76秒
2025-07-29 10:40:47,207 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/185.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/187.mp4'

2025-07-29 10:40:47,207 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6x7_52mz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6x7_52mz\temp_combined.mp4
2025-07-29 10:40:47,334 - INFO - 合并后的视频时长: 5.81秒，目标音频时长: 3.12秒
2025-07-29 10:40:47,334 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6x7_52mz\temp_combined.mp4 -ss 0 -to 3.118 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-29 10:40:47,577 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:47,577 - INFO - 目标音频时长: 3.12秒
2025-07-29 10:40:47,577 - INFO - 实际视频时长: 3.14秒
2025-07-29 10:40:47,578 - INFO - 时长差异: 0.02秒 (0.80%)
2025-07-29 10:40:47,578 - INFO - ==========================================
2025-07-29 10:40:47,578 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:47,578 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-29 10:40:47,578 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6x7_52mz
2025-07-29 10:40:47,625 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:47,625 - INFO -   - 音频时长: 3.12秒
2025-07-29 10:40:47,625 - INFO -   - 视频时长: 3.14秒
2025-07-29 10:40:47,625 - INFO -   - 时长差异: 0.02秒 (0.80%)
2025-07-29 10:40:47,625 - INFO - 
字幕 #24 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:47,625 - INFO - 生成的视频文件:
2025-07-29 10:40:47,625 - INFO -   1. F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-29 10:40:47,625 - INFO -   2. F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-29 10:40:47,625 - INFO -   3. F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-29 10:40:47,625 - INFO - ========== 字幕 #24 处理结束 ==========

