#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI视频生成工具

该工具用于处理新格式的字幕文件，生成语音，并根据字幕序号合并对应的视频场景。
重要：：该程序要配合着2-2-2_ai_video_splitter(全视频场景分割).py使用，2-2-2_ai_video_splitter(全视频场景分割).py是根据视频时间轴，将视频分割成多个场景。


工作流程:
1. 解析新格式的字幕文件 (格式: [3,9,32] 文本内容...)
2. 为所有字幕文本统一生成语音，确保总语音时长不超过目标时长(60秒)
3. 在语音生成成功后，根据字幕序号从scenes_report.json查找对应场景
4. 合并场景并调整长度以匹配语音时长
5. 输出最终视频文件

使用方法:
python 5_ai_video_generator.py [--srt 字幕文件] [--json 场景JSON文件] [--output 输出目录]
"""




import os
import re
import json
import shutil
import subprocess
import tempfile
import argparse
import logging
from datetime import datetime
import importlib.util
from pydub import AudioSegment
import traceback
import requests # 添加requests库用于调用Ollama API
import sys # 添加sys模块用于刷新标准输出
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))  # 添加上级目录到搜索路径
from close_capcut import close_capcut_hybrid

# 设置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ai_video_generator.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("AI视频生成器")


# 添加日志刷新函数
def flush_all_logs():
    """强制刷新所有日志处理器的缓冲区"""
    for handler in logging.root.handlers:
        handler.flush()
    sys.stdout.flush()
    sys.stderr.flush()

# 创建日志目录
log_dir = "ai_video_logs"
os.makedirs(log_dir, exist_ok=True)

# 设置日志格式
logger = logging.getLogger("AI视频生成器")

# 创建日志目录

# 添加日志辅助函数
def setup_subtitle_logger(subtitle_index):
    """为特定字幕创建专用的日志处理器"""
    # 创建日志文件名，包含时间戳和字幕索引
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"subtitle_{subtitle_index}_{timestamp}.log")
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    
    # 创建专用的日志器
    subtitle_logger = logging.getLogger(f"字幕#{subtitle_index}")
    subtitle_logger.setLevel(logging.INFO)
    subtitle_logger.addHandler(file_handler)
    subtitle_logger.propagate = False  # 防止重复输出
    
    logger.info(f"为字幕 #{subtitle_index} 创建专用日志文件: {log_file}")
    return subtitle_logger, log_file

# 默认路径配置
DEFAULT_SRT_PATH = "yuan.srt"
DEFAULT_SCENES_JSON_PATH = "ai-video-splitter/scenes_report.json"
DEFAULT_OUTPUT_DIR = "F:/github/aicut_auto/newcut_ai"
DEFAULT_AUDIO_OUTPUT_DIR = "output"

# 语音合成配置
MIN_SPEED = 1.1      # 最低语速限制
MAX_SPEED = 1.7      # 最高语速限制
DEFAULT_SPEED = 1.4  # 默认语速
TARGET_DURATION = 60 # 目标时长（秒）
USE_ADAPTIVE_SPEED = False  # 是否使用自适应语速（True: 自动调整语速适应目标时长, False: 使用固定1.2语速）
FIXED_SPEED = 1.2    # 固定语速模式下使用的语速

# 翻译API配置
TRANSLATION_API_MODE = 3 # 0: 使用现有API, 1: 使用Ollama API, 2: 使用LM Studio API, 3: 使用Gemini Balance API
OLLAMA_API_URL = "http://***************:11434/api/chat"
OLLAMA_MODEL = "qwen3:8b"

# 场景匹配模式配置
SCENE_MATCHING_MODE = 1  # 0: 当前模式(6套方案), 1: 新模式(4套方案，单序号匹配)

# LM Studio API配置
LM_STUDIO_API_URL = "http://localhost:1234/v1/chat/completions"
LM_STUDIO_MODEL = "qwen/qwen3-8b"

# Gemini Balance API配置
GEMINI_BALANCE_API_URL = "http://***************:7770/v1/chat/completions"
GEMINI_BALANCE_API_KEY = "123456"
GEMINI_BALANCE_MODEL = "gemini-2.5-flash-preview-05-20"  # 可以根据实际支持的模型名称调整

translation_instruction = """# Role: 专业中英翻译助手

## Profile
- 你是一位经验丰富的中英互译专家。
- 你精通中文和英文的口语和书面表达，尤其擅长将中文文本翻译成地道、自然的英文。
- 你理解文化差异和习语，能够捕捉原文的细微之处和语境。

## Goals
- 将用户提供的中文文本准确地、完整地翻译成英文。
- 确保翻译后的英文自然流畅，符合英文母语者的表达习惯。
- 使翻译内容具有口语化风格，避免生硬或直译。
- 保持原文的原意、语气和风格，做到言简意赅，尽可能简短精炼。

## Constraints
- **必须**避免逐字翻译或生硬的直译。
- **必须**生成符合英文日常交流习惯的译文。
- 不得在译文之外添加任何解释性文字或其他内容，除非用户明确要求。
- 如果原文有歧义，尝试根据上下文给出最可能的自然翻译。
- **必须保留文本中出现的 "##" 分隔符，不得对其进行翻译、移动位置或删除。这些分隔符用于文本分割。**
- **不得遗漏原文中的任何句子、词组或信息点。** (Ensure no content is lost)
- **不得在翻译过程中擅自添加原文没有的任何新的文字内容或信息。** (Ensure no new content is added)
- **严格只执行"翻译"任务，不进行改写、总结或创造。** (Strictly translation only)
- **输出的翻译结果中，不得添加任何额外的换行符、回车符或其他会改变文本布局的空白字符，除非原文中明确存在对应的需要保留的此类字符。** (No extra newlines or formatting)

## Skills
- 高级中英双语能力。
- 熟悉中英文的各种表达方式、俚语、俗语和惯用语。
- 强大的语境理解能力。
- 优秀的文本风格转换能力（从中文直白表达转换为英文自然表达）。
- 精准捕捉并完整传达原文所有信息点的能力。
- **在保持自然流畅的同时，严格控制输出格式，不添加额外格式符的能力。**

## Workflow
1. 接收用户提供的中文文本。
2. 仔细阅读并理解中文文本的整体含义、潜在语境和语气，同时识别所有需要翻译的信息点。
3. 分析文本中是否存在需要特别处理的习语、俗语或文化相关表达。
4. 在翻译过程中，识别并完全忽略 "##" 分隔符，确保它们原样保留在译文的相应位置，同时确保翻译覆盖原文的所有信息点，不增不减。
5. 将中文文本翻译成英文，重点关注使用自然的、口语化的英文表达，严格对照原文，确保所有内容都已翻译，且未添加额外信息，并保持 "##" 分隔符不变。
6. 对生成的英文译文进行校对，确保其准确性、流畅性和自然度，并再次确认 "##" 分隔符被保留，以及译文内容与原文内容（忽略分隔符）在信息上完全对应。
7. **输出最终的英文译文，确保其中不含任何额外添加的换行符或其他格式字符。**
8. 文化适配：避免使用需要特定文化背景才能理解的词汇（如"归宁"翻译成"回娘家"，"白月光"翻译成"心上人"）

## Output Format
只输出翻译后的英文文本，包含原文本中的 "##" 分隔符。输出应为一行或与原文的行结构严格对应的格式，不包含任何多余的换行。不包含任何其他前缀或后缀。

## Example
用户输入：
"这事儿挺麻烦的，我得好好想想办法。## 这边还有点细节需要处理，请注意一下。"

AI 翻译助手输出（期望的自然口语化翻译，保留 ##，内容完整，无额外换行）：
"This is quite tricky. I need to really think about how to handle it.## There are still some details here that need to be addressed, please pay attention to that."

重要：
1.中文必须全部翻译成英文，不能漏翻译或让翻译后的内容中存在中文！！！并且必须保留"##"的分隔符，以便后续程序进行分割！！并且保证翻译的英文尽可能的简短！
2.在保持原意的基础上，优先选择更短、更直接的词汇和句式，使翻译内容尽可能简短精炼，在不损失原意的基础上进行最大程度的压缩！！！！
3.翻译完后，必须检查“##”分隔符数量是否等于字幕数量-1，如果不匹配，需重新翻译或整理，必须保证“##”分隔符数量等于字幕数量-1！！
中文内容：
"""

def contains_chinese(text):
    """
    检查文本是否包含中文字符

    参数:
        text (str): 要检查的文本

    返回:
        bool: 如果包含中文字符返回True，否则返回False
    """
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
    return bool(chinese_pattern.search(text))

def is_meaningful_text(text):
    """
    检查文本是否有意义（不是空白、无意义字符或过短）

    参数:
        text (str): 要检查的文本

    返回:
        bool: 如果文本有意义返回True，否则返回False
    """
    if not text or not text.strip():
        return False

    # 移除空白字符后检查长度
    cleaned_text = text.strip()
    if len(cleaned_text) < 2:
        return False

    # 检查是否只包含标点符号和空格
    meaningful_chars = re.sub(r'[^\w\s]', '', cleaned_text)
    if len(meaningful_chars.strip()) < 2:
        return False

    # 检查是否是常见的无意义文本
    meaningless_patterns = [
        r'^[.\-_=\s]*$',  # 只有标点符号和空格
        r'^(missing|error|fail|null|undefined|none)$',  # 常见错误文本
        r'^[0-9\s\-_.]*$',  # 只有数字和符号
    ]

    for pattern in meaningless_patterns:
        if re.match(pattern, cleaned_text.lower()):
            return False

    return True

def validate_translation_result(translated_text, expected_count, original_texts):
    """
    严格验证翻译结果

    参数:
        translated_text (str): 翻译API返回的文本
        expected_count (int): 期望的分段数量
        original_texts (list): 原始文本列表

    返回:
        dict: 验证结果，包含 'valid' (bool), 'errors' (list), 'segments' (list)
    """
    errors = []

    # 1. 检查翻译结果是否为空
    if not translated_text or not translated_text.strip():
        errors.append("翻译结果为空")
        return {'valid': False, 'errors': errors, 'segments': []}

    # 2. 检查是否包含中文字符
    if contains_chinese(translated_text):
        chinese_matches = re.findall(r'[\u4e00-\u9fff]+', translated_text)
        errors.append(f"翻译结果包含中文字符: {', '.join(set(chinese_matches))}")

    # 3. 验证分隔符数量
    separator_count = translated_text.count('##')
    expected_separator_count = expected_count - 1  # n段文本需要n-1个分隔符

    if separator_count != expected_separator_count:
        errors.append(f"分隔符数量不匹配: 期望 {expected_separator_count} 个 '##'，实际找到 {separator_count} 个")

    # 4. 分割文本并验证每段内容
    if '##' not in translated_text and expected_count > 1:
        errors.append("缺少必需的 '##' 分隔符")
        return {'valid': False, 'errors': errors, 'segments': []}

    # 分割文本
    if expected_count == 1:
        segments = [translated_text.strip()]
    else:
        segments = [segment.strip() for segment in translated_text.split('##')]

    # 5. 验证分段数量
    if len(segments) != expected_count:
        errors.append(f"分段数量不匹配: 期望 {expected_count} 段，实际得到 {len(segments)} 段")

    # 6. 验证每段内容的有效性
    for i, segment in enumerate(segments):
        if not segment:
            errors.append(f"第 {i+1} 段翻译内容为空")
        elif not is_meaningful_text(segment):
            errors.append(f"第 {i+1} 段翻译内容无意义: '{segment}'")
        elif contains_chinese(segment):
            chinese_chars = re.findall(r'[\u4e00-\u9fff]+', segment)
            errors.append(f"第 {i+1} 段翻译内容包含中文: '{segment}' (中文字符: {', '.join(set(chinese_chars))})")

    # 7. 验证翻译完整性（检查是否有明显的遗漏）
    if len(segments) == expected_count:
        total_translated_length = sum(len(seg.strip()) for seg in segments)
        total_original_length = sum(len(text.strip()) for text in original_texts)

        # 如果翻译后的总长度明显小于原文（考虑中英文长度差异，设置阈值为原文的30%）
        if total_translated_length < total_original_length * 0.3:
            errors.append(f"翻译内容可能不完整: 翻译后总长度 {total_translated_length} 字符，原文总长度 {total_original_length} 字符")

    # 返回验证结果
    is_valid = len(errors) == 0
    return {
        'valid': is_valid,
        'errors': errors,
        'segments': segments if is_valid else []
    }

def parse_new_subtitle_format(srt_path):
    """
    解析新格式的字幕文件，提取序号列表和文本内容
    格式示例: [3,9,32] 文本内容...（注释说明...）
    也支持无空格格式: [3,9,32]文本内容...（注释说明...）
    
    返回:
        list: 包含字幕信息的字典列表，每个字典包含:
            - subtitle_numbers: 字幕序号列表
            - text: 字幕文本内容
            - index: 在文件中的序号
    """
    logger.info(f"开始解析字幕文件: {srt_path}")
    subtitles = []
    
    # 定义可能的编码列表
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5']
    
    # 尝试使用不同编码读取文件
    lines = []
    for encoding in encodings:
        try:
            with open(srt_path, 'r', encoding=encoding) as file:
                lines = file.readlines()
                logger.info(f"成功使用 {encoding} 编码读取字幕文件")
                break  # 如果成功读取，跳出循环
        except UnicodeDecodeError:
            logger.warning(f"使用 {encoding} 编码读取字幕文件失败，尝试下一种编码")
        except Exception as e:
            logger.error(f"读取字幕文件时出错: {e}")
            return []
    
    if not lines:
        logger.error("无法使用任何编码成功读取字幕文件")
        return []
    
    try:
        # 修改匹配格式，移除空格要求：[数字,数字,...] 文本内容（可能包含括号内的注释）
        # 使用更灵活的正则表达式，同时支持有空格和无空格的情况
        pattern = r'\[([\d,\s]+)\]\s*(.*?)(?:\s*（.*）|\s*\(.*\)|$)'
        
        # 记录每种解析方法的成功次数，用于诊断
        pattern1_count = 0
        pattern2_count = 0
        pattern3_count = 0
        failed_count = 0
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # 记录正在解析的行，方便诊断
            logger.debug(f"正在解析第 {i+1} 行: {line}")
                
            match = re.match(pattern, line)
            if match:
                numbers_str = match.group(1)
                text = match.group(2).strip()  # 获取括号前的实际文案内容并去除两端空格
                
                # 解析序号列表
                subtitle_numbers = [int(num.strip()) for num in numbers_str.split(',')]
                
                subtitles.append({
                    'subtitle_numbers': subtitle_numbers,
                    'text': text,
                    'index': i + 1
                })
                logger.debug(f"【方法1】解析到字幕 #{i+1}: 序号={subtitle_numbers}, 文本={text[:50]}...")
                pattern1_count += 1
            else:
                # 更强大的备用匹配模式，尝试处理复杂情况
                # 1. 首先匹配方括号内的数字部分
                # 2. 然后提取剩余文本，再尝试分离正文和注释
                basic_pattern = r'\[([\d,\s]+)\](.*)'
                basic_match = re.match(basic_pattern, line)
                
                if basic_match:
                    numbers_str = basic_match.group(1)
                    full_text = basic_match.group(2).strip()
                    
                    # 尝试分离文本和注释
                    # 查找第一个中文或英文括号的位置
                    text = full_text
                    
                    # 查找各种可能的注释起始位置
                    comment_starts = []
                    for char in ['（', '(']:
                        pos = full_text.find(char)
                        if pos > 0:
                            comment_starts.append(pos)
                    
                    # 如果找到了注释起始位置，截取到最前面的那个
                    if comment_starts:
                        first_comment = min(comment_starts)
                        text = full_text[:first_comment].strip()
                    
                    # 解析序号列表
                    subtitle_numbers = [int(num.strip()) for num in numbers_str.split(',')]
                    
                    subtitles.append({
                        'subtitle_numbers': subtitle_numbers,
                        'text': text,
                        'index': i + 1
                    })
                    logger.debug(f"【方法2】使用备用方式解析字幕 #{i+1}: 序号={subtitle_numbers}, 文本={text[:50]}...")
                    pattern2_count += 1
                else:
                    # 最后的尝试：只提取方括号内的数字部分
                    last_resort_pattern = r'\[([\d,\s]+)\]'
                    last_match = re.match(last_resort_pattern, line)
                    if last_match:
                        numbers_str = last_match.group(1)
                        # 提取方括号后的所有文本
                        rest_text = line[line.find(']')+1:].strip()
                        
                        # 同样尝试分离文本和注释
                        text = rest_text
                        for char in ['（', '(']:
                            pos = rest_text.find(char)
                            if pos > 0:
                                text = rest_text[:pos].strip()
                                break
                        
                        # 解析序号列表
                        subtitle_numbers = [int(num.strip()) for num in numbers_str.split(',')]
                        
                        subtitles.append({
                            'subtitle_numbers': subtitle_numbers,
                            'text': text,
                            'index': i + 1
                        })
                        logger.debug(f"【方法3】使用最后手段解析字幕 #{i+1}: 序号={subtitle_numbers}, 文本={text[:50]}...")
                        pattern3_count += 1
                    else:
                        logger.warning(f"无法解析行 #{i+1}: {line}")
                        failed_count += 1
        
        # 输出解析统计
        total_lines = len([line for line in lines if line.strip()])
        logger.info(f"解析统计: 总行数={total_lines}, 方法1成功={pattern1_count}, 方法2成功={pattern2_count}, 方法3成功={pattern3_count}, 失败={failed_count}")
        logger.info(f"共解析到 {len(subtitles)} 条字幕")
        
        # 若无解析成功，给出更明确的提示
        if len(subtitles) == 0:
            logger.error("没有成功解析任何字幕！请检查字幕文件格式是否正确。")
            logger.error("支持的格式示例: [1,2,3] 文本内容...（注释）或[1,2,3]文本内容...（注释）")
        
        return subtitles
    except Exception as e:
        logger.error(f"解析字幕文件时出错: {e}")
        logger.error(traceback.format_exc())
        return []

def load_sound_hecheng_module():
    """加载4-sound_hecheng.py模块"""
    try:
        spec = importlib.util.spec_from_file_location("sound_hecheng", "4-sound_hecheng.py")
        sound_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(sound_module)
        logger.info("成功加载4-sound_hecheng.py模块")
        return sound_module
    except Exception as e:
        logger.error(f"加载语音合成模块时出错: {e}")
        return None

def create_temp_output_dir(base_dir):
    """创建临时输出目录"""
    temp_dir = os.path.join(base_dir, "temp")
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)
    logger.info(f"创建临时目录: {temp_dir}")
    return temp_dir

def clear_directory(directory):
    """清空指定目录中的所有文件"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")
        return
        
    logger.info(f"清空目录: {directory}")
    for file in os.listdir(directory):
        file_path = os.path.join(directory, file)
        try:
            if os.path.isfile(file_path):
                os.unlink(file_path)
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
            logger.debug(f"删除: {file_path}")
        except Exception as e:
            logger.error(f"删除文件时出错: {file_path} - {e}")

def create_combined_subtitle(subtitles, output_dir):
    """
    为所有字幕创建一个合并的SRT文件，时间戳连续
    
    参数:
        subtitles (list): 字幕信息列表
        output_dir (str): 输出目录路径
        
    返回:
        tuple: (combined_srt_path, total_duration)
    """
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 输出文件路径
        combined_srt_path = os.path.join(output_dir, "combined.srt")
        
        logger.info(f"创建合并字幕文件: {combined_srt_path}")
        
        # 按索引排序字幕
        sorted_subtitles = sorted(subtitles, key=lambda x: x['index'])
        
        # 估计每条字幕的平均时长 (假设总时间为60秒，均分给所有字幕)
        avg_duration = TARGET_DURATION / len(sorted_subtitles)
        
        with open(combined_srt_path, 'w', encoding='utf-8') as file:
            current_time = 0  # 当前时间戳，用于记录累积时间
            
            for i, subtitle in enumerate(sorted_subtitles):
                index = subtitle['index']
                text = subtitle['text']
                
                # 计算开始和结束时间
                start_time_str = format_time(current_time)
                end_time_str = format_time(current_time + avg_duration)
                
                # 写入SRT条目
                file.write(f"{index}\n")
                file.write(f"{start_time_str} --> {end_time_str}\n")
                file.write(f"{text}\n\n")
                
                # 更新当前时间戳
                current_time += avg_duration
        
        total_duration = current_time
        logger.info(f"已创建字幕文件(时间戳连续): {combined_srt_path}")
        logger.info(f"总时长: {total_duration:.2f}秒")
        
        return combined_srt_path, total_duration
    except Exception as e:
        logger.error(f"创建合并字幕文件时出错: {e}")
        return None, 0

def format_time(seconds):
    """将秒数转换为SRT时间格式字符串 (HH:MM:SS,mmm)"""
    h = int(seconds // 3600)
    m = int((seconds % 3600) // 60)
    s = seconds % 60
    whole_s = int(s)
    ms = int((s - whole_s) * 1000)
    return f"{h:02d}:{m:02d}:{whole_s:02d},{ms:03d}"

def translate_with_ollama(text, api_url, model):
    """
    使用Ollama API进行文本翻译。

    Args:
        text (str): 需要翻译的文本。
        api_url (str): Ollama API的地址。
        model (str): 使用的Ollama模型名称。

    Returns:
        str or None: 翻译后的文本，如果失败则返回None。
    """
    try:
        logger.info(f"正在调用Ollama API ({api_url}) 进行翻译...")
        payload = {
            "model": model,
            "messages": [

                {
                    "role": "user",
                    "content": f"{text}"
                }
            ],
            "stream": False # 不需要流式输出
        }

        # 发送POST请求到Ollama API，设置超时时间为2分钟 (120秒)
        response = requests.post(api_url, json=payload, timeout=240)
        response.raise_for_status() # 检查HTTP请求是否成功

        result = response.json()
        # 提取翻译结果，假设结果在message.content中
        translated_text = result.get("message", {}).get("content", "").strip()

        if translated_text:
            logger.info("Ollama翻译成功")
            # 使用正则表达式移除 <think>...</think> 标签及其内容
            translated_text = re.sub(r'<think>.*?<\/think>', '', translated_text, flags=re.DOTALL)
            return translated_text
        else:
            logger.error("Ollama翻译返回空结果")
            return None

    except requests.exceptions.RequestException as e:
        logger.error(f"调用Ollama API失败: {e}")
        return None
    except Exception as e:
        logger.error(f"处理Ollama翻译结果时出错: {e}")
        return None

def translate_with_lm_studio(text, api_url, model):
    """
    使用LM Studio API进行文本翻译。

    Args:
        text (str): 需要翻译的文本。
        api_url (str): LM Studio API的地址。
        model (str): 使用的LM Studio模型名称。

    Returns:
        str or None: 翻译后的文本，如果失败则返回None。
    """
    try:
        logger.info(f"正在调用LM Studio API ({api_url}) 进行翻译...")
        payload = {
            "model": model,
            "messages": [

                {
                    "role": "user",
                    "content": f"{text}"
                }
            ],
            "temperature": 0.7,
            "max_tokens": -1,
            "stream": False
        }

        # 发送POST请求到LM Studio API，设置超时时间为2分钟 (120秒)
        headers = {
            "Content-Type": "application/json"
        }
        
        response = requests.post(api_url, json=payload, headers=headers, timeout=240)
        response.raise_for_status() # 检查HTTP请求是否成功

        result = response.json()
        # 提取翻译结果，LM Studio使用OpenAI兼容格式
        translated_text = result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()

        if translated_text:
            logger.info("LM Studio翻译成功")
            # 使用正则表达式移除可能的标记内容
            translated_text = re.sub(r'<think>.*?<\/think>', '', translated_text, flags=re.DOTALL)
            translated_text = re.sub(r'```.*?```', '', translated_text, flags=re.DOTALL)
            return translated_text
        else:
            logger.error("LM Studio翻译返回空结果")
            return None

    except requests.exceptions.RequestException as e:
        logger.error(f"调用LM Studio API失败: {e}")
        return None
    except Exception as e:
        logger.error(f"处理LM Studio翻译结果时出错: {e}")
        return None

def translate_with_gemini_balance(text, api_url, api_key, model):
    """
    使用Gemini Balance API进行文本翻译。

    Args:
        text (str): 需要翻译的文本。
        api_url (str): Gemini Balance API的地址。
        api_key (str): Gemini Balance API的密钥。
        model (str): 使用的Gemini Balance模型名称。

    Returns:
        str or None: 翻译后的文本，如果失败则返回None。
    """
    try:
        logger.info(f"正在调用Gemini Balance API ({api_url}) 进行翻译...")
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": f"{text}"
                }
            ],
                 "stream": False
        }

        # 发送POST请求到Gemini Balance API，设置超时时间为2分钟 (120秒)
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        response = requests.post(api_url, json=payload, headers=headers, timeout=240)
        response.raise_for_status() # 检查HTTP请求是否成功

        result = response.json()
        # 提取翻译结果，Gemini Balance使用OpenAI兼容格式
        translated_text = result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()

        if translated_text:
            logger.info("Gemini Balance翻译成功")
            # 使用正则表达式移除可能的标记内容
            translated_text = re.sub(r'<think>.*?<\/think>', '', translated_text, flags=re.DOTALL)
            translated_text = re.sub(r'```.*?```', '', translated_text, flags=re.DOTALL)
            return translated_text
        else:
            logger.error("Gemini Balance翻译返回空结果")
            return None

    except requests.exceptions.RequestException as e:
        logger.error(f"调用Gemini Balance API失败: {e}")
        return None
    except Exception as e:
        logger.error(f"处理Gemini Balance翻译结果时出错: {e}")
        return None

def translate_combined_subtitle(srt_path):
    """
    调用翻译API翻译字幕文件，将字幕文本内容用##分隔符拼接后提交给API

    参数:
        srt_path (str): 字幕文件路径

    返回:
        str: 翻译后的字幕文件路径
    """
    try:
        logger.info("\n======== 开始字幕翻译过程 ========")
        logger.info(f"字幕文件: {srt_path}")

        # 读取字幕文件内容
        logger.info(f"正在读取字幕内容...")
        with open(srt_path, 'r', encoding='utf-8') as f:
            srt_content = f.read()

        # 解析原始SRT文件结构，保存原始信息
        logger.info("解析原始SRT文件结构...")
        original_subtitles = parse_srt_structure(srt_path)
        original_count = len(original_subtitles)
        logger.info(f"原始SRT文件包含 {original_count} 段字幕")

        # 提取所有字幕的文本内容，用##分隔符拼接
        logger.info("提取字幕文本内容并用##分隔符拼接...")
        subtitle_texts = []
        for subtitle in original_subtitles:
            text = subtitle['text'].strip()
            if text:
                subtitle_texts.append(text)
        
        # 用##拼接所有字幕文本
        combined_text = "##".join(subtitle_texts)
        logger.info(f"提取到 {len(subtitle_texts)} 段字幕文本，总长度: {len(combined_text)} 字符")
        logger.info(f"拼接后的文本预览: {combined_text[:200]}...")

        # 构建翻译请求内容
        translation_request = translation_instruction + combined_text

        translated_text = None

        if TRANSLATION_API_MODE == 0:
            logger.info("使用现有翻译API模式")
            # 动态导入3-fanyi.py模块
            fanyi_path = "3-fanyi.py"
            if not os.path.exists(fanyi_path):
                logger.error(f"错误: 翻译模块 {fanyi_path} 不存在")
                return None

            logger.info(f"正在加载翻译模块: {fanyi_path}")
            try:
                spec = importlib.util.spec_from_file_location("fanyi_module", fanyi_path)
                fanyi_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(fanyi_module)
                logger.info(f"成功加载翻译模块")
            except Exception as e:
                logger.error(f"错误: 加载翻译模块失败: {str(e)}")
                return None

            # 调用现有API进行翻译
            logger.info(f"正在调用现有翻译API翻译 {len(subtitle_texts)} 段字幕文本...")
            api_url = 'http://192.168.100.159:3000/v1/chat/completions'
            # 请替换为实际的API密钥
            api_key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJnb29nbGUtb2F1dGgyfHVzZXJfMDFKRVdTWlJCQzdHSE5WMzAySFhXSldNR1ciLCJ0aW1lIjoiMTc0NDY0OTM1MCIsInJhbmRvbW5lc3MiOiJkZWY4MDZhYS03MzBlLTQ5ODciLCJleHAiOjE3NDk4MzMzNTAsImlzcyI6Imh0dHBzOi8vYXV0aGVudGljYXRpb24uY3Vyc29yLnNoIiwic2NvcGUiOiJvcGVuaWQgcHJvZmlsZSBlbWFpbCBvZmZsaW5lX2FjY2VzcyIsImF1ZCI6Imh0dHBzOi8vY3Vyc29yLmNvbSJ9.bIWIoPXLXpRoJIOHDIOkz9-bwFLzaZTnPrB6YhT_T8I,OkBCW8jSb6e18c5254e71d930527c11261724827f1e39a31810909a88eb812c62f2122c6/3456c3008f40f3aebbea52f5fdb3a003b0f3c480f95b8947bea84d22530db5d6'

            translated_text = fanyi_module.translate_with_api(
                translation_request,
                api_url,
                api_key
            )

        elif TRANSLATION_API_MODE == 1:
            logger.info("使用Ollama翻译API模式")
            logger.info(f"正在调用Ollama API翻译 {len(subtitle_texts)} 段字幕文本...")

            translated_text = translate_with_ollama(
                translation_request,
                OLLAMA_API_URL,
                OLLAMA_MODEL
            )

        elif TRANSLATION_API_MODE == 2:
            logger.info("使用LM Studio翻译API模式")
            logger.info(f"正在调用LM Studio API翻译 {len(subtitle_texts)} 段字幕文本...")

            translated_text = translate_with_lm_studio(
                translation_request,
                LM_STUDIO_API_URL,
                LM_STUDIO_MODEL
            )

        elif TRANSLATION_API_MODE == 3:
            logger.info("使用Gemini Balance翻译API模式")
            logger.info(f"正在调用Gemini Balance API翻译 {len(subtitle_texts)} 段字幕文本...")

            translated_text = translate_with_gemini_balance(
                translation_request,
                GEMINI_BALANCE_API_URL,
                GEMINI_BALANCE_API_KEY,
                GEMINI_BALANCE_MODEL
            )

        if not translated_text:
            logger.error(f"翻译API返回空结果 (模式: {TRANSLATION_API_MODE})")
            return None

        # 保存原始翻译内容到txt文件
        translation_txt_path = "F:\\github\\aicut_auto\\翻译内容.txt"
        try:
            logger.info(f"正在保存翻译内容到: {translation_txt_path}")
            
            # 确保目录存在
            txt_dir = os.path.dirname(translation_txt_path)
            if not os.path.exists(txt_dir):
                os.makedirs(txt_dir)
                logger.info(f"创建目录: {txt_dir}")
            
            # 保存翻译内容，包含时间戳和详细信息
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            with open(translation_txt_path, 'w', encoding='utf-8') as f:
                f.write(f"翻译时间: {current_time}\n")
                f.write(f"原始字幕文件: {srt_path}\n")
                f.write(f"字幕条数: {len(subtitle_texts)}\n")
                f.write(f"翻译API模式: {TRANSLATION_API_MODE}\n")
                if TRANSLATION_API_MODE == 1:
                    f.write(f"Ollama API地址: {OLLAMA_API_URL}\n")
                    f.write(f"Ollama模型: {OLLAMA_MODEL}\n")
                elif TRANSLATION_API_MODE == 2:
                    f.write(f"LM Studio API地址: {LM_STUDIO_API_URL}\n")
                    f.write(f"LM Studio模型: {LM_STUDIO_MODEL}\n")
                elif TRANSLATION_API_MODE == 3:
                    f.write(f"Gemini Balance API地址: {GEMINI_BALANCE_API_URL}\n")
                    f.write(f"Gemini Balance模型: {GEMINI_BALANCE_MODEL}\n")
                f.write("="*60 + "\n")
                f.write("提交给API的原始内容:\n")
                f.write("="*60 + "\n\n")
                f.write(translation_request)
                f.write("\n\n" + "="*60 + "\n")
                f.write("API返回的翻译结果:\n")
                f.write("="*60 + "\n\n")
                f.write(translated_text)
                f.write("\n\n" + "="*60 + "\n")
                f.write("翻译处理完成\n")
            
            logger.info(f"翻译内容已成功保存到: {translation_txt_path}")
            
        except Exception as e:
            logger.error(f"警告: 保存翻译内容到txt文件失败: {str(e)}")
            logger.error(f"翻译过程将继续，但txt文件可能未保存成功")

        # 清理翻译结果，移除可能存在的Markdown代码块标记
        translated_text = translated_text.strip()
        translated_text = re.sub(r'^```.*?\n', '', translated_text)
        translated_text = re.sub(r'\n```\s*$', '', translated_text)
        translated_text = re.sub(r'```', '', translated_text)

        logger.info("开始处理翻译结果，进行严格验证")

        # 严格验证翻译结果
        validation_result = validate_translation_result(translated_text, original_count, subtitle_texts)
        if not validation_result['valid']:
            logger.error("=" * 60)
            logger.error("翻译结果验证失败！程序终止！")
            logger.error("=" * 60)
            for error in validation_result['errors']:
                logger.error(f"❌ {error}")
            logger.error("=" * 60)
            logger.error("请检查翻译API配置或联系技术支持！")
            logger.error("程序将立即终止执行。")
            logger.error("=" * 60)
            sys.exit(1)

        # 验证通过，获取分割后的翻译段落
        translated_segments = validation_result['segments']
        translated_count = len(translated_segments)

        logger.info("✅ 翻译结果验证通过！")
        logger.info(f"✅ 成功分割得到 {translated_count} 段翻译文本")
        logger.info("开始生成标准SRT文件")

        # 生成标准SRT格式的en.srt文件
        en_srt_path = "F:\\github\\aicut_auto\\en.srt"
        logger.info(f"正在生成标准SRT文件: {en_srt_path}")

        with open(en_srt_path, 'w', encoding='utf-8') as f:
            for i, original_subtitle in enumerate(original_subtitles):
                # 写入ID
                f.write(f"{original_subtitle['id']}\n")
                
                # 写入时间戳
                f.write(f"{original_subtitle['timestamp']}\n")
                
                # 写入翻译后的文本内容
                f.write(f"{translated_segments[i]}\n")
                
                # 写入空行
                f.write("\n")

        logger.info(f"标准SRT文件生成完成: {en_srt_path}")
        logger.info("======== 字幕翻译过程结束 ========\n")

        return en_srt_path
    except Exception as e:
        logger.error(f"翻译过程出错: {e}")
        logger.error(traceback.format_exc())
        return None

def parse_srt_structure(srt_path):
    """
    解析SRT文件结构，保留ID、时间戳等信息
    
    参数:
        srt_path (str): SRT文件路径
        
    返回:
        list: 包含字幕结构信息的列表
    """
    try:
        subtitles = []
        with open(srt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        current_subtitle = {}
        text_lines = []
        
        for line in lines:
            line = line.strip()
            
            # 字幕ID行
            if line and line.isdigit():
                # 保存上一个字幕
                if current_subtitle:
                    current_subtitle['text'] = '\n'.join(text_lines)
                    subtitles.append(current_subtitle)
                
                # 开始新的字幕
                current_subtitle = {'id': line}
                text_lines = []
                
            # 时间戳行
            elif '-->' in line:
                current_subtitle['timestamp'] = line
                
            # 文本内容行
            elif line:
                text_lines.append(line)
                
            # 空行（字幕结束标志）
            # 继续处理下一行
        
        # 保存最后一个字幕
        if current_subtitle:
            current_subtitle['text'] = '\n'.join(text_lines)
            subtitles.append(current_subtitle)
            
        return subtitles
        
    except Exception as e:
        logger.error(f"解析SRT文件结构时出错: {e}")
        return []

def generate_audio_for_subtitles(subtitles, output_dir, sound_module):
    """
    为所有字幕生成语音，遵循原始的语音生成流程
    
    参数:
        subtitles (list): 字幕列表
        output_dir (str): 音频输出目录
        sound_module: 已加载的sound_hecheng模块
        
    返回:
        dict: 处理结果信息
    """
    try:
        logger.info("\n========= 开始语音生成过程 =========")
        
        # 步骤1: 创建合并的SRT文件(combined.srt)
        combined_srt_path, estimated_duration = create_combined_subtitle(subtitles, "F:\\github\\aicut_auto\\newcut")
        if not combined_srt_path:
            return {"success": False, "reason": "创建合并字幕文件失败"}
        
        # 步骤2: 翻译字幕文件
        en_srt_path = translate_combined_subtitle(combined_srt_path)
        if not en_srt_path:
            return {"success": False, "reason": "翻译字幕文件失败"}
        
        # 步骤3: 调用sound_hecheng模块进行语音合成
        logger.info("\n======== 开始音频合成过程 ========")
        logger.info(f"使用字幕文件: {en_srt_path}")
        logger.info(f"输出目录: {output_dir}")
        
        # 清空音频输出目录
        clear_directory(output_dir)
        
        logger.info("正在调用语音合成模块，这可能需要一些时间...")
        flush_all_logs()  # 确保之前的日志立即显示

        # 根据USE_ADAPTIVE_SPEED开关调用不同的语音合成逻辑
        if USE_ADAPTIVE_SPEED:
            logger.info("🎯 使用自适应语速模式：自动调整语速以适应目标时长")
            logger.info(f"   目标时长: {TARGET_DURATION}秒, 语速范围: {MIN_SPEED}-{MAX_SPEED}")
            # 调用sound_hecheng.py的main函数 - 自适应语速模式
            result = sound_module.main(
                srt_path=en_srt_path,
                output_dir=output_dir,
                engine="kokoro",
                speaker="am_adam_男.pt",
                speed=None,  # 使用自动语速
                min_speed=MIN_SPEED,
                max_speed=MAX_SPEED,
                target_duration=TARGET_DURATION
            )
        else:
            logger.info("🔧 使用固定语速模式：使用固定语速配音，不限制时长")
            logger.info(f"   固定语速: {FIXED_SPEED}, 不参考目标时长限制")
            # 调用sound_hecheng.py的main函数 - 固定语速模式
            result = sound_module.main(
                srt_path=en_srt_path,
                output_dir=output_dir,
                engine="kokoro",
                speaker="am_adam_男.pt",
                speed=FIXED_SPEED,  # 使用固定语速
                min_speed=None,     # 不设置最低语速限制
                max_speed=None,     # 不设置最高语速限制
                target_duration=9999  # 使用大数值代替None，表示不限制时长
            )
        
        logger.info("语音合成模块调用完成")
        flush_all_logs()  # 确保语音合成的日志立即显示
        
        if not result:
            logger.error("语音合成失败: 未返回结果")
            return {"success": False, "reason": "语音合成失败"}
        
        total_duration = result.get("total_duration", 0)
        speed = result.get("speed", DEFAULT_SPEED)
        is_auto_speed = result.get("is_auto_speed", True)
        
        logger.info(f"语音合成完成: 总时长={total_duration:.2f}秒, 语速={speed}")

        # 根据模式检查生成的音频是否符合要求
        if USE_ADAPTIVE_SPEED:
            # 自适应语速模式：检查是否超过目标时长
            if total_duration > TARGET_DURATION:
                logger.error(f"即使使用最高语速({MAX_SPEED})，总语音时长({total_duration:.2f}秒)仍超过目标时长({TARGET_DURATION}秒)")
                return {
                    "success": False,
                    "reason": "语音总时长超过目标时长",
                    "total_duration": total_duration,
                    "speed": speed
                }
            logger.info(f"✅ 自适应语速模式：语音时长({total_duration:.2f}秒)符合目标时长({TARGET_DURATION}秒)要求")
        else:
            # 固定语速模式：不检查时长限制，直接接受结果
            logger.info(f"✅ 固定语速模式：使用固定语速{FIXED_SPEED}完成配音，总时长={total_duration:.2f}秒")
            logger.info("   注意：固定语速模式不限制配音时长，将根据实际配音时长匹配场景")
        
        # 收集每个字幕的音频信息
        audio_durations = {}
        for i, subtitle in enumerate(subtitles, 1):
            audio_path = os.path.join(output_dir, f"{i}.wav")
            if os.path.exists(audio_path):
                # 获取音频时长
                audio = AudioSegment.from_wav(audio_path)
                duration = len(audio) / 1000.0  # 转换为秒
                audio_durations[subtitle['index']] = {
                    "duration": duration,
                    "audio_path": audio_path
                }
                logger.info(f"字幕 #{subtitle['index']} 的音频文件: {audio_path}, 时长: {duration:.2f}秒")
        
        logger.info(f"所有音频生成完成，共 {len(audio_durations)} 个音频文件")
        
        # 新增：合并所有音频文件
        all_audio_path = "F:\\github\\aicut_auto\\PaddleSpeech\\all.wav"
        if not merge_wav_files(audio_durations, all_audio_path):
            logger.error("合并音频文件失败")
            return {"success": False, "reason": "合并音频文件失败"}
        
        # 步骤4: 根据实际生成的音频时长调整字幕时间戳
        logger.info("\n======== 开始调整字幕时间戳 ========")
        # 先调整英文字幕的时间戳
        adjusted_en_srt = adjust_subtitle_timestamps(en_srt_path, audio_durations)
        
        # 然后根据相同规则调整中文字幕的时间戳
        adjusted_combined_srt = adjust_subtitle_timestamps(combined_srt_path, audio_durations)
        
        logger.info("字幕时间戳已根据实际音频时长进行调整")
        logger.info("======== 音频合成过程结束 ========\n")
        flush_all_logs()  # 确保所有音频处理的日志立即显示
        
        return {
            "success": True,
            "total_duration": total_duration,
            "speed": speed,
            "is_auto_speed": is_auto_speed,
            "audio_durations": audio_durations,
            "combined_srt": adjusted_combined_srt,
            "en_srt": adjusted_en_srt
        }
    except Exception as e:
        logger.error(f"生成语音时出错: {e}")
        return {"success": False, "reason": str(e)}

def load_scenes_json(json_path):
    """
    加载并解析scenes_report.json文件
    
    参数:
        json_path (str): JSON文件路径
        
    返回:
        dict: 解析后的JSON数据
    """
    try:
        logger.info(f"加载场景JSON文件: {json_path}")
        with open(json_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        logger.info(f"JSON文件加载成功，共 {len(data.get('scenes', []))} 个场景")
        return data
    except Exception as e:
        logger.error(f"加载JSON文件时出错: {e}")
        return {}

def find_scenes_by_subtitle_numbers(subtitle_numbers, scenes_data):
    """
    根据字幕序号查找对应的场景，优先使用overlap_with_subtitle类型场景
    
    参数:
        subtitle_numbers (list): 字幕序号列表，如 [3, 9, 32]
        scenes_data (dict): 场景数据
        
    返回:
        dict: 按字幕序号分组的场景字典，每个字幕有两个场景列表：
             'overlap': 优先使用的overlap类型场景
             'between': 备选的between类型场景
    """
    try:
        # 初始化结果字典
        result = {}
        for subtitle_num in subtitle_numbers:
            result[subtitle_num] = {
                'overlap': [],  # 优先使用的overlap类型场景
                'between': []   # 备选的between类型场景
            }
        
        # 获取所有场景
        all_scenes = scenes_data.get('scenes', [])
        logger.info(f"开始查找字幕序号 {subtitle_numbers} 对应的场景，共有 {len(all_scenes)} 个场景可选")
        
        # 第一轮：查找所有overlap_with_subtitle类型场景
        for scene in all_scenes:
            # 检查overlap_with_subtitle字段
            if scene.get('overlap_with_subtitle') in subtitle_numbers:
                subtitle_num = scene.get('overlap_with_subtitle')
                scene_copy = scene.copy()
                scene_copy['match_method'] = "overlap_with_subtitle"
                result[subtitle_num]['overlap'].append(scene_copy)
                logger.info(f"找到overlap场景: scene_id={scene['scene_id']}, 字幕#{subtitle_num}")
                continue
                
            # 检查subtitle_info.related_subtitles数组中的overlap类型
            subtitle_info = scene.get('subtitle_info', {})
            related_subtitles = subtitle_info.get('related_subtitles', [])
            position = subtitle_info.get('position', '')
            
            if position == 'overlap_with_subtitle':
                for related in related_subtitles:
                    if related.get('number') in subtitle_numbers:
                        subtitle_num = related.get('number')
                        scene_copy = scene.copy()
                        scene_copy['matched_subtitle'] = subtitle_num
                        scene_copy['match_method'] = "related_subtitles_overlap"
                        result[subtitle_num]['overlap'].append(scene_copy)
                        logger.info(f"找到related_overlap场景: scene_id={scene['scene_id']}, 字幕#{subtitle_num}")
                        break  # 一个场景只匹配一个字幕
        
        # 第二轮：查找所有between类型场景作为备选
        for scene in all_scenes:
            # 检查between_subtitles字段
            between = scene.get('between_subtitles', [])
            if between and len(between) == 2:
                start, end = between
                for subtitle_num in subtitle_numbers:
                    if start <= subtitle_num <= end:
                        scene_copy = scene.copy()
                        scene_copy['matched_subtitle'] = subtitle_num
                        scene_copy['match_method'] = "between_subtitles"
                        result[subtitle_num]['between'].append(scene_copy)
                        logger.info(f"找到between场景: scene_id={scene['scene_id']}, 字幕范围#{start}-#{end}, 用于字幕#{subtitle_num}")
            
            # 检查subtitle_info.related_subtitles数组中的between类型
            subtitle_info = scene.get('subtitle_info', {})
            related_subtitles = subtitle_info.get('related_subtitles', [])
            position = subtitle_info.get('position', '')
            
            if position == 'between':
                for related in related_subtitles:
                    if related.get('number') in subtitle_numbers:
                        subtitle_num = related.get('number')
                        scene_copy = scene.copy()
                        scene_copy['matched_subtitle'] = subtitle_num
                        scene_copy['match_method'] = "related_subtitles_between"
                        result[subtitle_num]['between'].append(scene_copy)
                        logger.info(f"找到related_between场景: scene_id={scene['scene_id']}, 字幕#{subtitle_num}")
                        break  # 一个场景只匹配一个字幕
        
        # 统计和汇总
        for subtitle_num in subtitle_numbers:
            overlap_count = len(result[subtitle_num]['overlap'])
            between_count = len(result[subtitle_num]['between'])
            logger.info(f"字幕 #{subtitle_num} 找到 {overlap_count} 个overlap场景, {between_count} 个between场景")
            
            # 如果没有找到任何场景，发出警告
            if overlap_count == 0 and between_count == 0:
                logger.warning(f"字幕 #{subtitle_num} 没有找到任何匹配场景!")
        
        return result
    except Exception as e:
        logger.error(f"查找字幕场景时出错: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        # 返回空结果结构
        return {num: {'overlap': [], 'between': []} for num in subtitle_numbers}

def single_subtitle_scene_matching(subtitle_num, scenes_data, audio_duration, all_scenes_list, globally_used_scene_ids):
    """
    为单个字幕序号匹配场景，选择第一个overlap场景作为起点，不够则顺延

    参数:
        subtitle_num (int): 单个字幕序号
        scenes_data (dict): 场景数据
        audio_duration (float): 音频时长
        all_scenes_list (list): 所有场景的列表
        globally_used_scene_ids (set): 全局已使用的场景ID集合

    返回:
        list: 选中的场景列表
    """
    try:
        logger.info(f"开始为单个字幕序号 #{subtitle_num} 匹配场景，目标时长: {audio_duration:.2f}秒")

        # 查找该字幕序号对应的场景
        scenes_by_types = find_scenes_by_subtitle_numbers([subtitle_num], scenes_data)

        # 获取该序号的overlap场景，排除全局已使用的
        overlap_scenes = [
            s for s in scenes_by_types[subtitle_num]['overlap']
            if s['scene_id'] not in globally_used_scene_ids
        ]

        # 获取该序号的between场景作为备选，排除全局已使用的
        between_scenes = [
            s for s in scenes_by_types[subtitle_num]['between']
            if s['scene_id'] not in globally_used_scene_ids
        ]

        logger.info(f"字幕序号 #{subtitle_num} 找到 {len(overlap_scenes)} 个可用overlap场景, {len(between_scenes)} 个可用between场景")

        selected_scenes = []
        total_duration = 0

        # 选择第一个可用的overlap场景作为起点
        start_scene = None
        if overlap_scenes:
            start_scene = overlap_scenes[0]  # 选择第一个overlap场景
            logger.info(f"选择第一个overlap场景作为起点: scene_id={start_scene['scene_id']}")
        elif between_scenes:
            start_scene = between_scenes[0]  # 如果没有overlap，选择第一个between场景
            logger.info(f"没有overlap场景，选择第一个between场景作为起点: scene_id={start_scene['scene_id']}")
        else:
            logger.error(f"字幕序号 #{subtitle_num} 没有找到任何可用的匹配场景")
            return []

        # 添加起点场景
        selected_scenes.append(start_scene)
        total_duration += get_scene_duration_seconds(start_scene)
        logger.info(f"添加起点场景: scene_id={start_scene['scene_id']}, 时长={get_scene_duration_seconds(start_scene):.2f}秒, 累计时长={total_duration:.2f}秒")

        # 如果起点场景时长已经足够，直接返回
        if total_duration >= audio_duration:
            logger.info(f"起点场景时长已满足要求，无需延伸")
            return selected_scenes

        # 时长不够，需要延伸填充
        logger.info(f"起点场景时长不足，需要延伸填充 {audio_duration - total_duration:.2f}秒")

        # 在原始场景列表中找到起点场景的索引
        start_scene_id = start_scene.get('scene_id')
        start_scene_index = -1
        for i, scene in enumerate(all_scenes_list):
            if scene.get('scene_id') == start_scene_id:
                start_scene_index = i
                break

        if start_scene_index == -1:
            logger.error(f"无法在原始场景列表中找到起点场景: scene_id={start_scene_id}")
            return selected_scenes

        logger.info(f"起点场景在原始列表中的索引: {start_scene_index}")

        # 从下一个场景开始顺序填充
        current_index = start_scene_index + 1
        used_scene_ids = {start_scene_id}  # 记录本次已使用的场景ID

        while total_duration < audio_duration and current_index < len(all_scenes_list):
            next_scene = all_scenes_list[current_index]
            next_scene_id = next_scene.get('scene_id')

            # 检查是否已被全局使用或本次已使用
            if next_scene_id in globally_used_scene_ids or next_scene_id in used_scene_ids:
                logger.debug(f"跳过已使用的场景: scene_id={next_scene_id}")
                current_index += 1
                continue

            scene_duration = get_scene_duration_seconds(next_scene)
            fill_duration_needed = audio_duration - total_duration

            # 计算本次可以添加的时长
            duration_to_add = min(fill_duration_needed, scene_duration)

            # 添加场景（如果需要裁剪，记录裁剪信息）
            scene_to_add = next_scene.copy()
            if duration_to_add < scene_duration:
                scene_to_add['trim_duration'] = duration_to_add
                logger.info(f"延伸添加场景: scene_id={next_scene_id} (裁剪至 {duration_to_add:.2f}秒)")
            else:
                logger.info(f"延伸添加场景: scene_id={next_scene_id} (完整时长 {scene_duration:.2f}秒)")

            selected_scenes.append(scene_to_add)
            total_duration += duration_to_add
            used_scene_ids.add(next_scene_id)

            logger.info(f"累计时长: {total_duration:.2f}秒")
            current_index += 1

        # 如果到达列表末尾仍需填充，从列表开头继续查找
        if total_duration < audio_duration:
            logger.info(f"到达列表末尾，仍需填充 {audio_duration - total_duration:.2f}秒，从列表开头继续查找")
            current_index = 0

            while total_duration < audio_duration and current_index < start_scene_index:
                next_scene = all_scenes_list[current_index]
                next_scene_id = next_scene.get('scene_id')

                # 检查是否已被全局使用或本次已使用
                if next_scene_id in globally_used_scene_ids or next_scene_id in used_scene_ids:
                    current_index += 1
                    continue

                scene_duration = get_scene_duration_seconds(next_scene)
                fill_duration_needed = audio_duration - total_duration

                # 计算本次可以添加的时长
                duration_to_add = min(fill_duration_needed, scene_duration)

                # 添加场景（如果需要裁剪，记录裁剪信息）
                scene_to_add = next_scene.copy()
                if duration_to_add < scene_duration:
                    scene_to_add['trim_duration'] = duration_to_add
                    logger.info(f"从列表开头延伸添加场景: scene_id={next_scene_id} (裁剪至 {duration_to_add:.2f}秒)")
                else:
                    logger.info(f"从列表开头延伸添加场景: scene_id={next_scene_id} (完整时长 {scene_duration:.2f}秒)")

                selected_scenes.append(scene_to_add)
                total_duration += duration_to_add
                used_scene_ids.add(next_scene_id)

                logger.info(f"累计时长: {total_duration:.2f}秒")
                current_index += 1

        logger.info(f"字幕序号 #{subtitle_num} 场景匹配完成，共选择 {len(selected_scenes)} 个场景，总时长: {total_duration:.2f}秒")
        return selected_scenes

    except Exception as e:
        logger.error(f"单序号场景匹配时出错: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return []

def get_scene_duration_seconds(scene):
    """计算场景时长（秒）"""
    try:
        duration_str = scene.get('duration', '00:00:00,000')
        h, m, s = duration_str.split(':')
        s, ms = s.split(',')
        return int(h) * 3600 + int(m) * 60 + int(s) + int(ms) / 1000
    except Exception as e:
        logger.error(f"计算场景时长时出错: {e}")
        return 0

def time_to_seconds(time_str):
    """将时间字符串转换为秒数"""
    try:
        h, m, s = time_str.split(':')
        s, ms = s.split(',')
        return int(h) * 3600 + int(m) * 60 + int(s) + int(ms) / 1000
    except Exception as e:
        logger.error(f"时间转换为秒数时出错: {e}")
        return 0

def seconds_to_time(seconds):
    """将秒数转换为时间字符串 (HH:MM:SS.mmm)"""
    h = int(seconds // 3600)
    m = int((seconds % 3600) // 60)
    s = seconds % 60
    whole_s = int(s)
    ms = int((s - whole_s) * 1000)
    return f"{h:02d}:{m:02d}:{whole_s:02d}.{ms:03d}"

def merge_scenes(scenes, output_path, audio_duration, base_video_dir="F:\\github\\aicut_auto\\ai-video-splitter"):
    """
    合并场景并精确调整长度以匹配语音时长，采用先合并后裁剪的方式
    
    参数:
        scenes (list): 场景列表
        output_path (str): 输出视频文件路径
        audio_duration (float): 语音时长（秒）
        base_video_dir (str): 视频文件基础目录
        
    返回:
        bool: 是否成功
    """
    if not scenes:
        logger.error("没有找到匹配的场景，无法合并")
        return False
    
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        logger.info(f"创建临时目录: {temp_dir}")
        
        # 临时文件路径定义
        temp_combined_path = os.path.join(temp_dir, "temp_combined.mp4")
        file_list_path = os.path.join(temp_dir, "file_list.txt")
        
        # 验证基础目录是否存在
        if not os.path.exists(base_video_dir):
            logger.error(f"基础视频目录不存在: {base_video_dir}")
            shutil.rmtree(temp_dir)
            return False
            
        # 准备文件列表，将所有场景直接合并而不进行预处理
        with open(file_list_path, 'w', encoding='utf-8') as file_list:
            # 添加足够的场景以确保总时长超过音频时长
            total_duration = 0
            written_files = 0
            
            for scene in scenes:
                # 构建完整的绝对路径
                scene_file = os.path.join(base_video_dir, scene['file_name'])
                
                # 检查文件是否存在
                if not os.path.exists(scene_file):
                    logger.warning(f"场景文件不存在: {scene_file}")
                    continue
                
                # 验证文件存在
                logger.info(f"添加场景文件: {scene_file} (确认存在: {os.path.exists(scene_file)})")
                
                # 使用绝对路径并转换为FFmpeg兼容的格式（正斜杠）
                # 注意: FFmpeg需要完整的绝对路径
                scene_file_ffmpeg = os.path.abspath(scene_file).replace('\\', '/')
                
                # 写入file_list.txt
                file_list.write(f"file '{scene_file_ffmpeg}'\n")
                written_files += 1
                
                # 累计时长
                scene_duration = get_scene_duration_seconds(scene)
                total_duration += scene_duration
                logger.info(f"添加场景ID={scene.get('scene_id', 'unknown')}，时长={scene_duration:.2f}秒，累计时长={total_duration:.2f}秒")
                
                # 当累计时长达到音频时长的1.5倍时停止（确保足够长）
                if total_duration >= audio_duration * 1.5:
                    logger.info(f"场景总时长({total_duration:.2f}秒)已达到音频时长({audio_duration:.2f}秒)的1.5倍，停止添加场景")
                    break
        
        if written_files == 0:
            logger.error("没有有效的场景文件可以合并")
            shutil.rmtree(temp_dir)
            return False
        
        logger.info(f"准备合并 {written_files} 个场景文件，总时长约 {total_duration:.2f}秒")
        
        # 检查生成的file_list.txt内容
        with open(file_list_path, 'r', encoding='utf-8') as f:
            file_list_content = f.read()
            logger.info(f"生成的文件列表内容:\n{file_list_content}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 步骤1: 使用ffmpeg合并所有场景为临时文件
        merge_command = [
            "ffmpeg", "-y", 
            "-f", "concat", 
            "-safe", "0", 
            "-i", file_list_path, 
            "-c", "copy", 
            temp_combined_path
        ]
        logger.info(f"合并命令: {' '.join(merge_command)}")
        
        merge_process = subprocess.run(merge_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        if merge_process.returncode != 0:
            error_output = merge_process.stderr.decode('utf-8', errors='ignore')
            logger.error(f"合并视频失败: {error_output}")
            
            # 尝试列出目录内容，帮助诊断问题
            try:
                logger.info(f"基础目录 {base_video_dir} 内容:")
                for item in os.listdir(base_video_dir):
                    logger.info(f"  - {item}")
            except Exception as e:
                logger.error(f"无法列出目录内容: {e}")
                
            shutil.rmtree(temp_dir)
            return False
            
        # 步骤2: 获取合并后的视频时长
        duration_command = [
            "ffprobe", "-v", "error", 
            "-show_entries", "format=duration", 
            "-of", "default=noprint_wrappers=1:nokey=1", 
            temp_combined_path
        ]
        
        duration_process = subprocess.run(duration_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        if duration_process.returncode != 0:
            logger.error(f"无法获取合并视频时长: {duration_process.stderr.decode('utf-8', errors='ignore')}")
            shutil.rmtree(temp_dir)
            return False
        
        combined_duration = float(duration_process.stdout.decode('utf-8').strip())
        logger.info(f"合并后的视频时长: {combined_duration:.2f}秒，目标音频时长: {audio_duration:.2f}秒")
        
        # 步骤3: 精确裁剪视频以匹配音频时长
        # 如果合并后的视频比音频短，则报错（无法延长）
        if combined_duration < audio_duration:
            logger.error(f"合并后的视频时长({combined_duration:.2f}秒)小于音频时长({audio_duration:.2f}秒)，无法进行精确裁剪")
            # 尝试直接使用已有视频
            logger.warning("将直接使用合并后的视频，可能导致音视频不同步")
            shutil.copy(temp_combined_path, output_path)
        else:
            # 精确裁剪视频
            # 使用-to参数指定精确的结束时间点，而不是使用-t指定时长
            trim_command = [
                "ffmpeg", "-y", 
                "-i", temp_combined_path,
                "-ss", "0", 
                "-to", str(audio_duration),  # 使用-to而非-t确保精确的结束时间
                "-c:v", "libx264", 
                "-crf", "18",
                "-preset", "ultrafast",
                "-avoid_negative_ts", "1",
                "-video_track_timescale", "90000",  # 使用高精度时间刻度
                output_path
            ]
            logger.info(f"精确裁剪命令: {' '.join(trim_command)}")
            
            trim_process = subprocess.run(trim_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            if trim_process.returncode != 0:
                logger.error(f"精确裁剪失败: {trim_process.stderr.decode('utf-8', errors='ignore')}")
                # 回退方案：直接使用合并后的视频
                logger.warning("裁剪失败，将使用合并后的原始视频")
                shutil.copy(temp_combined_path, output_path)
        
        # 步骤4: 验证最终输出的视频时长
        verify_command = [
            "ffprobe", "-v", "error", 
            "-show_entries", "format=duration", 
            "-of", "default=noprint_wrappers=1:nokey=1", 
            output_path
        ]
        
        verify_process = subprocess.run(verify_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        if verify_process.returncode == 0:
            actual_duration = float(verify_process.stdout.decode('utf-8').strip())
            duration_diff = abs(actual_duration - audio_duration)
            
            # 增加更清晰的日志输出
            logger.info("============ 视频与音频时长比较 ============")
            logger.info(f"目标音频时长: {audio_duration:.2f}秒")
            logger.info(f"实际视频时长: {actual_duration:.2f}秒")
            logger.info(f"时长差异: {duration_diff:.2f}秒 ({(duration_diff/audio_duration)*100:.2f}%)")
            logger.info("==========================================")
            
            # 如果差异超过0.3秒，再次尝试精确裁剪
            if duration_diff > 0.3 and actual_duration > audio_duration:
                logger.warning(f"第一次裁剪后仍有较大差异({duration_diff:.2f}秒)，尝试二次裁剪")
                
                # 使用严格模式再次裁剪
                retry_output = os.path.join(temp_dir, "retry_output.mp4")
                retry_command = [
                    "ffmpeg", "-y", 
                    "-i", output_path,
                    "-ss", "0", 
                    "-to", str(audio_duration),
                    "-c:v", "libx264", 
                    "-crf", "18",
                    "-preset", "ultrafast",
                    "-strict", "experimental",  # 使用严格实验模式
                    "-avoid_negative_ts", "1",
                    retry_output
                ]
                
                retry_process = subprocess.run(retry_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                
                if retry_process.returncode == 0:
                    # 再次验证
                    retry_verify = subprocess.run(verify_command[:-1] + [retry_output], 
                                                 stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    
                    if retry_verify.returncode == 0:
                        retry_duration = float(retry_verify.stdout.decode('utf-8').strip())
                        retry_diff = abs(retry_duration - audio_duration)
                        
                        logger.info(f"二次裁剪后视频时长: {retry_duration:.2f}秒，差异: {retry_diff:.2f}秒")
                        
                        # 如果二次裁剪效果更好，使用二次裁剪结果
                        if retry_diff < duration_diff:
                            shutil.copy(retry_output, output_path)
                            logger.info(f"使用二次裁剪结果，差异从 {duration_diff:.2f}秒 减少到 {retry_diff:.2f}秒")
                            actual_duration = retry_duration
            
            # 判断最终结果
            if duration_diff > 0.5:  # 允许0.5秒的误差
                logger.warning(f"警告: 输出视频时长与目标时长差异较大: {duration_diff:.2f}秒 ({(duration_diff/audio_duration)*100:.2f}%)")
            else:
                logger.info(f"成功: 输出视频时长与目标时长匹配良好，误差在允许范围内")
        else:
            logger.warning(f"无法验证最终输出视频时长: {verify_process.stderr.decode('utf-8', errors='ignore')}")
        
        logger.info(f"视频合并和裁剪完成: {output_path}")
        
        # 清理临时目录
        shutil.rmtree(temp_dir)
        logger.info(f"清理临时目录: {temp_dir}")
        
        return True
    except Exception as e:
        logger.error(f"合并场景时出错: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        
        # 尝试清理临时目录
        try:
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except:
            pass
            
        return False

def adjust_scenes_duration(scenes, target_duration):
    """
    精确调整场景组合的时长，使其匹配目标时长
    
    参数:
        scenes (list): 场景列表
        target_duration (float): 目标时长（秒）
        
    返回:
        list: 调整后的场景列表
    """
    try:
        # 计算当前组合的总时长
        total_duration = sum(get_scene_duration_seconds(scene) for scene in scenes)
        logger.info(f"调整前总时长: {total_duration:.2f}秒, 目标时长: {target_duration:.2f}秒")
        
        # 如果已经完全匹配，无需调整
        if abs(total_duration - target_duration) < 0.01:  # 允许0.01秒的误差
            logger.info("场景时长已经匹配目标时长，无需调整")
            return scenes
        
        # 如果总时长大于目标时长，需要裁剪
        if total_duration > target_duration:
            # 计算需要裁剪的时长
            trim_duration = total_duration - target_duration
            logger.info(f"需要裁剪 {trim_duration:.2f}秒")
            
            # 按时长从长到短排序场景
            scenes_with_duration = [(i, scene, get_scene_duration_seconds(scene)) 
                                   for i, scene in enumerate(scenes)]
            scenes_with_duration.sort(key=lambda x: x[2], reverse=True)
            
            # 找到最长的场景
            longest_index, longest_scene, longest_duration = scenes_with_duration[0]
            
            # 确保裁剪后场景至少保留一定比例的时长
            min_allowed_duration = max(1.0, longest_duration * 0.3)  # 至少保留1秒或30%的时长
            
            # 如果裁剪后的时长过短，尝试裁剪多个场景
            if longest_duration - trim_duration < min_allowed_duration:
                logger.info(f"裁剪单个场景会导致时长过短，尝试裁剪多个场景")
                
                remaining_trim = trim_duration
                for idx, scene, duration in scenes_with_duration:
                    # 计算当前场景可以裁剪的最大时长
                    max_trim_for_scene = duration - min_allowed_duration
                    if max_trim_for_scene <= 0:
                        continue  # 这个场景太短，不能裁剪
                    
                    # 确定实际裁剪的时长
                    actual_trim = min(remaining_trim, max_trim_for_scene)
                    
                    # 更新场景的裁剪信息
                    new_duration = duration - actual_trim
                    scenes[idx]['trim_duration'] = new_duration
                    logger.info(f"裁剪场景ID={scenes[idx]['scene_id']}：从{duration:.2f}秒裁剪至{new_duration:.2f}秒")
                    
                    # 更新剩余需要裁剪的时长
                    remaining_trim -= actual_trim
                    
                    # 如果已经裁剪足够，可以停止
                    if remaining_trim <= 0.01:
                        break
                
                # 如果仍然需要裁剪，移除最短的场景
                if remaining_trim > 0.01:
                    logger.warning(f"通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: {remaining_trim:.2f}秒")
                    
                    # 按时长从短到长排序
                    scenes_with_duration.sort(key=lambda x: x[2])
                    
                    # 移除场景直到达到目标时长
                    for idx, scene, duration in scenes_with_duration:
                        if remaining_trim <= 0.01:
                            break
                        
                        # 跳过已经裁剪过的场景
                        if 'trim_duration' in scenes[idx]:
                            continue
                        
                        logger.info(f"移除场景ID={scenes[idx]['scene_id']}，时长={duration:.2f}秒")
                        scenes[idx]['remove'] = True
                        remaining_trim -= duration
            else:
                # 可以只裁剪最长场景
                new_duration = longest_duration - trim_duration
                scenes[longest_index]['trim_duration'] = new_duration
                logger.info(f"裁剪最长场景ID={longest_scene['scene_id']}：从{longest_duration:.2f}秒裁剪至{new_duration:.2f}秒")
            
            # 移除标记为删除的场景
            scenes = [scene for scene in scenes if not scene.get('remove')]
            
        # 如果总时长小于目标时长，需要填充
        elif total_duration < target_duration:
            # 计算需要填充的时长
            fill_duration = target_duration - total_duration
            logger.info(f"需要填充 {fill_duration:.2f}秒")
            
            # 使用最后一个场景的副本作为填充
            if scenes:
                last_scene = scenes[-1].copy()
                last_scene['is_padding'] = True
                last_scene['trim_duration'] = fill_duration
                scenes.append(last_scene)
                logger.info(f"添加填充场景ID={last_scene['scene_id']}，时长={fill_duration:.2f}秒")
        
        # 计算调整后的总时长
        adjusted_duration = 0
        for scene in scenes:
            if 'trim_duration' in scene:
                adjusted_duration += scene['trim_duration']
            else:
                adjusted_duration += get_scene_duration_seconds(scene)
        
        logger.info(f"调整后总时长: {adjusted_duration:.2f}秒，与目标时长差异: {abs(adjusted_duration - target_duration):.2f}秒")
        return scenes
    except Exception as e:
        logger.error(f"调整场景时长时出错: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return scenes

def optimize_scene_selection(subtitle, scenes_data, audio_duration, num_plans=6, globally_used_scene_ids=set()):
    """
    优化场景选择，生成多套方案，优先使用overlap类型场景

    参数:
        subtitle (dict): 字幕信息
        scenes_data (dict): 场景数据
        audio_duration (float): 音频时长
        num_plans (int): 要生成的方案数量
        globally_used_scene_ids (set): 全局已使用的场景ID集合

    返回:
        list: 场景组合方案列表
    """
    try:
        # 根据场景匹配模式选择不同的处理逻辑
        if SCENE_MATCHING_MODE == 1:
            # 新模式：单序号匹配 + 传统模式
            return optimize_scene_selection_new_mode(subtitle, scenes_data, audio_duration, globally_used_scene_ids)
        else:
            # 当前模式：传统的6套方案生成
            return optimize_scene_selection_current_mode(subtitle, scenes_data, audio_duration, num_plans, globally_used_scene_ids)
    except Exception as e:
        logger.error(f"场景选择优化时出错: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return []

def optimize_scene_selection_new_mode(subtitle, scenes_data, audio_duration, globally_used_scene_ids):
    """
    新模式：为每个字幕序号单独生成方案 + 传统模式方案

    参数:
        subtitle (dict): 字幕信息
        scenes_data (dict): 场景数据
        audio_duration (float): 音频时长
        globally_used_scene_ids (set): 全局已使用的场景ID集合

    返回:
        list: 4套场景方案列表
    """
    try:
        subtitle_numbers = subtitle['subtitle_numbers']
        logger.info(f"========== 新模式：为字幕 #{subtitle['index']} 生成4套场景方案 ==========")
        logger.info(f"字幕序号列表: {subtitle_numbers}")

        plans = []
        all_scenes_list = scenes_data.get('scenes', [])

        # 为每个字幕序号生成一套方案（前3套）
        for i, subtitle_num in enumerate(subtitle_numbers):
            logger.info(f"\n--- 生成方案 #{i+1}：基于字幕序号 #{subtitle_num} ---")

            selected_scenes = single_subtitle_scene_matching(
                subtitle_num, scenes_data, audio_duration, all_scenes_list, globally_used_scene_ids
            )

            if selected_scenes:
                plans.append(selected_scenes)
                logger.info(f"方案 #{i+1} 生成成功，包含 {len(selected_scenes)} 个场景")

                # 新模式：只将第1套方案（序号最小的单点方案）的场景加入全局集合
                if i == 0:  # 第一套方案
                    for scene in selected_scenes:
                        scene_id = scene.get('scene_id')
                        if scene_id is not None:
                            globally_used_scene_ids.add(scene_id)
                            logger.debug(f"新模式：将第1套方案的场景 scene_id={scene_id} 添加到全局已使用集合")
                    logger.info(f"新模式：第1套方案的 {len(selected_scenes)} 个场景已加入全局已使用集合")
            else:
                logger.warning(f"方案 #{i+1} 生成失败，未找到合适的场景")

        # 第4套方案：使用传统模式生成（完全独立）
        logger.info(f"\n--- 生成方案 #{len(plans)+1}：使用传统模式 ---")
        traditional_plans = optimize_scene_selection_current_mode(
            subtitle, scenes_data, audio_duration, 1, set()  # 传入空的已使用集合，完全独立生成
        )

        if traditional_plans:
            plans.extend(traditional_plans)
            logger.info(f"方案 #{len(plans)} (传统模式) 生成成功")
        else:
            logger.warning("传统模式方案生成失败")

        logger.info(f"========== 新模式：字幕 #{subtitle['index']} 共生成 {len(plans)} 套有效场景方案 ==========")
        return plans

    except Exception as e:
        logger.error(f"新模式场景选择时出错: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return []

def optimize_scene_selection_current_mode(subtitle, scenes_data, audio_duration, num_plans, globally_used_scene_ids):
    """
    当前模式：传统的场景选择逻辑

    参数:
        subtitle (dict): 字幕信息
        scenes_data (dict): 场景数据
        audio_duration (float): 音频时长
        num_plans (int): 要生成的方案数量
        globally_used_scene_ids (set): 全局已使用的场景ID集合

    返回:
        list: 场景组合方案列表
    """
    try:
        logger.info(f"========== 当前模式：为字幕 #{subtitle['index']} 生成 {num_plans} 套场景方案 ==========")

        # 查找字幕序号对应的场景，区分overlap和between类型
        # 在查找时就排除全局已使用的场景
        scenes_by_types = find_scenes_by_subtitle_numbers(
            subtitle['subtitle_numbers'], scenes_data
        )

        # 收集所有overlap类型场景，排除全局已使用的
        all_overlap_scenes = [
            s for subtitle_num in subtitle['subtitle_numbers']
            for s in scenes_by_types[subtitle_num]['overlap']
            if s['scene_id'] not in globally_used_scene_ids
        ]

        # 收集所有between类型场景作为备选，排除全局已使用的
        all_between_scenes = [
            s for subtitle_num in subtitle['subtitle_numbers']
            for s in scenes_by_types[subtitle_num]['between']
            if s['scene_id'] not in globally_used_scene_ids
        ]

        logger.info(f"共收集 {len(all_overlap_scenes)} 个未使用的overlap场景和 {len(all_between_scenes)} 个未使用的between场景")

        # 如果没有任何可用的场景，无法生成方案
        if not all_overlap_scenes and not all_between_scenes:
            logger.error(f"字幕 #{subtitle['index']} 没有找到任何可用的匹配场景，无法生成方案")
            return []

        # 获取原始场景列表，用于后续查找下一个场景
        all_scenes_list = scenes_data.get('scenes', [])

        # 为每个方案生成不同的场景组合
        import random

        plans = []

        # 创建字幕内部的已使用场景ID集合，确保同一字幕的不同方案不使用相同的场景
        subtitle_used_scene_ids = set()

        for plan_index in range(num_plans):
            logger.info(f"开始生成方案 #{plan_index+1}")

            # 过滤掉已在全局使用的场景和字幕内部已使用的场景
            current_plan_available_overlap = [
                s for s in all_overlap_scenes 
                if s['scene_id'] not in globally_used_scene_ids and s['scene_id'] not in subtitle_used_scene_ids
            ]
            current_plan_available_between = [
                s for s in all_between_scenes 
                if s['scene_id'] not in globally_used_scene_ids and s['scene_id'] not in subtitle_used_scene_ids
            ]

            # 如果当前方案没有可用的场景，跳过
            if not current_plan_available_overlap and not current_plan_available_between:
                logger.warning(f"方案 #{plan_index+1}: 没有可用的未使用的场景，跳过此方案生成。")
                continue

            selected_scenes = []
            total_duration = 0

            # 尝试优先使用overlap场景
            available_for_selection = current_plan_available_overlap.copy()
            random.shuffle(available_for_selection) # 随机打乱以便选择多样性

            # 首先为每个字幕序号尝试添加一个未使用的overlap场景（如果存在且未被全局使用）
            added_subtitle_scenes = set() # 记录已为哪些字幕序号添加了场景
            scenes_to_add_initially = []

            for subtitle_num in subtitle['subtitle_numbers']:
                # 找到与当前字幕序号关联且未被全局使用和字幕内部使用的场景
                num_scenes = [
                    s for s in available_for_selection
                    if (s.get('overlap_with_subtitle') == subtitle_num or s.get('matched_subtitle') == subtitle_num)
                    and s['scene_id'] not in globally_used_scene_ids
                    and s['scene_id'] not in subtitle_used_scene_ids
                ]
                if num_scenes and subtitle_num not in added_subtitle_scenes:
                    scene = random.choice(num_scenes)
                    scenes_to_add_initially.append(scene)
                    # Mark as added for this subtitle number
                    added_subtitle_scenes.add(subtitle_num)
                    logger.info(f"方案 #{plan_index+1}: 为字幕#{subtitle_num}选择初始化overlap场景id={scene['scene_id']}")

            # Add initial scenes and update total duration and used scene sets
            for scene in scenes_to_add_initially:
                selected_scenes.append(scene)
                total_duration += get_scene_duration_seconds(scene)
                scene_id = scene.get('scene_id')
                if scene_id is not None:
                    # 只有方案1才添加到全局已使用集合
                    if plan_index == 0:
                        globally_used_scene_ids.add(scene_id)
                        logger.debug(f"方案 #{plan_index+1}: 将场景 scene_id={scene_id} 添加到全局已使用集合")
                    # 所有方案都添加到字幕内部已使用集合
                    subtitle_used_scene_ids.add(scene_id)
                    logger.debug(f"方案 #{plan_index+1}: 将场景 scene_id={scene_id} 添加到字幕内部已使用集合")

            logger.info(f"方案 #{plan_index+1}: 初始选择后，当前总时长={total_duration:.2f}秒")

            # 继续添加剩余的 overlap 场景直到超过音频时长或没有更多可用的未使用的场景
            remaining_overlap = [
                s for s in available_for_selection 
                if s['scene_id'] not in globally_used_scene_ids and s['scene_id'] not in subtitle_used_scene_ids
            ]

            while total_duration < audio_duration and remaining_overlap:
                scene = remaining_overlap.pop(random.randrange(len(remaining_overlap))) # 从剩余中随机移除并获取
                selected_scenes.append(scene)
                total_duration += get_scene_duration_seconds(scene)
                scene_id = scene.get('scene_id')
                if scene_id is not None:
                    # 只有方案1才添加到全局已使用集合
                    if plan_index == 0:
                        globally_used_scene_ids.add(scene_id)
                        logger.debug(f"方案 #{plan_index+1}: 额外添加overlap场景id={scene_id} 到全局已使用集合")
                    # 所有方案都添加到字幕内部已使用集合
                    subtitle_used_scene_ids.add(scene_id)
                    logger.debug(f"方案 #{plan_index+1}: 额外添加overlap场景id={scene_id} 到字幕内部已使用集合")
                logger.info(f"方案 #{plan_index+1}: 额外添加overlap场景id={scene['scene_id']}, 当前总时长={total_duration:.2f}秒")

            # 如果overlap场景不足且仍需填充，添加between场景
            available_for_selection_between = [
                s for s in current_plan_available_between 
                if s['scene_id'] not in globally_used_scene_ids and s['scene_id'] not in subtitle_used_scene_ids
            ]
            random.shuffle(available_for_selection_between) # 随机打乱

            # 如果之前没有为某个字幕序号添加场景，尝试使用between场景
            scenes_to_add_between = []
            for subtitle_num in subtitle['subtitle_numbers']:
                if subtitle_num not in added_subtitle_scenes:
                    num_scenes = [
                        s for s in available_for_selection_between
                        if s.get('matched_subtitle') == subtitle_num
                        and s['scene_id'] not in globally_used_scene_ids
                        and s['scene_id'] not in subtitle_used_scene_ids
                    ]
                    if num_scenes:
                        scene = random.choice(num_scenes)
                        scenes_to_add_between.append(scene)
                        # Mark as added for this subtitle number
                        added_subtitle_scenes.add(subtitle_num)
                        logger.info(f"方案 #{plan_index+1}: 为字幕#{subtitle_num}选择初始化between场景id={scene['scene_id']}")

            # Add initial between scenes and update total duration and used scene sets
            for scene in scenes_to_add_between:
                selected_scenes.append(scene)
                total_duration += get_scene_duration_seconds(scene)
                scene_id = scene.get('scene_id')
                if scene_id is not None:
                    # 只有方案1才添加到全局已使用集合
                    if plan_index == 0:
                        globally_used_scene_ids.add(scene_id)
                        logger.debug(f"方案 #{plan_index+1}: 将场景 scene_id={scene_id} 添加到全局已使用集合")
                    # 所有方案都添加到字幕内部已使用集合
                    subtitle_used_scene_ids.add(scene_id)
                    logger.debug(f"方案 #{plan_index+1}: 将场景 scene_id={scene_id} 添加到字幕内部已使用集合")

            logger.info(f"方案 #{plan_index+1}: 额外between选择后，当前总时长={total_duration:.2f}秒")

            # 继续添加剩余的 between 场景直到超过音频时长或没有更多可用的未使用的场景
            remaining_between = [
                s for s in available_for_selection_between 
                if s['scene_id'] not in globally_used_scene_ids and s['scene_id'] not in subtitle_used_scene_ids
            ]
            while total_duration < audio_duration and remaining_between:
                scene = remaining_between.pop(random.randrange(len(remaining_between))) # 从剩余中随机移除并获取
                selected_scenes.append(scene)
                total_duration += get_scene_duration_seconds(scene)
                scene_id = scene.get('scene_id')
                if scene_id is not None:
                    # 只有方案1才添加到全局已使用集合
                    if plan_index == 0:
                        globally_used_scene_ids.add(scene_id)
                        logger.debug(f"方案 #{plan_index+1}: 额外添加between场景id={scene_id} 到全局已使用集合")
                    # 所有方案都添加到字幕内部已使用集合
                    subtitle_used_scene_ids.add(scene_id)
                    logger.debug(f"方案 #{plan_index+1}: 额外添加between场景id={scene_id} 到字幕内部已使用集合")
                logger.info(f"方案 #{plan_index+1}: 额外添加between场景id={scene['scene_id']}, 当前总时长={total_duration:.2f}秒")

            # 如果成功选择了场景且仍需填充
            if selected_scenes and total_duration < audio_duration:
                logger.info(f"方案 #{plan_index+1}: 场景总时长({total_duration:.2f}秒)小于音频时长({audio_duration:.2f}秒)，需要延伸填充")

                # 获取当前选中的最后一个场景
                if selected_scenes:
                    last_scene = selected_scenes[-1]
                    last_scene_id = last_scene.get('scene_id')
                    
                    if last_scene_id is not None:
                        logger.info(f"方案 #{plan_index+1}: 最后一个场景ID: {last_scene_id}")
                        
                        # 在原始场景列表中找到这个场景的索引
                        last_scene_index = -1
                        for i, scene in enumerate(all_scenes_list):
                            if scene.get('scene_id') == last_scene_id:
                                last_scene_index = i
                                break
                        
                        if last_scene_index != -1 and last_scene_index < len(all_scenes_list) - 1:
                            logger.info(f"方案 #{plan_index+1}: 找到最后一个场景在原始列表中的索引: {last_scene_index}")
                            
                            # 计算需要填充的时长
                            fill_duration_needed = audio_duration - total_duration
                            logger.info(f"方案 #{plan_index+1}: 需要填充时长: {fill_duration_needed:.2f}秒")
                            
                            # 从下一个场景开始填充，跳过已使用的场景
                            current_index = last_scene_index + 1
                            
                            while fill_duration_needed > 0.01 and current_index < len(all_scenes_list):
                                next_scene = all_scenes_list[current_index]
                                next_scene_id = next_scene.get('scene_id')
                                
                                # 检查是否已被全局或字幕内部使用
                                if (next_scene_id in globally_used_scene_ids or 
                                    next_scene_id in subtitle_used_scene_ids):
                                    logger.info(f"方案 #{plan_index+1}: 跳过已使用的场景: scene_id={next_scene_id}")
                                    current_index += 1
                                    continue
                                
                                scene_duration = get_scene_duration_seconds(next_scene)
                                
                                # 计算本次可以添加的时长 (不超过剩余需要填充的时长)
                                duration_to_add = min(fill_duration_needed, scene_duration)
                                
                                # 添加场景（如果需要裁剪，记录裁剪信息）
                                scene_to_add = next_scene.copy()
                                if duration_to_add < scene_duration:
                                    scene_to_add['trim_duration'] = duration_to_add
                                    logger.info(f"方案 #{plan_index+1}: 追加场景 scene_id={next_scene_id} (裁剪至 {duration_to_add:.2f}秒)")
                                else:
                                    logger.info(f"方案 #{plan_index+1}: 追加场景 scene_id={next_scene_id} (完整时长 {scene_duration:.2f}秒)")
                                
                                selected_scenes.append(scene_to_add)
                                fill_duration_needed -= duration_to_add
                                total_duration += duration_to_add
                                
                                # 将此场景标记为已使用
                                if next_scene_id is not None:
                                    # 只有方案1才添加到全局已使用集合
                                    if plan_index == 0:
                                        globally_used_scene_ids.add(next_scene_id)
                                        logger.debug(f"方案 #{plan_index+1}: 将场景 scene_id={next_scene_id} 添加到全局已使用集合")
                                    # 所有方案都添加到字幕内部已使用集合
                                    subtitle_used_scene_ids.add(next_scene_id)
                                    logger.debug(f"方案 #{plan_index+1}: 将场景 scene_id={next_scene_id} 添加到字幕内部已使用集合")
                                
                                current_index += 1
                            
                            if fill_duration_needed > 0.01:
                                logger.warning(f"方案 #{plan_index+1}: 已到达原始场景列表末尾，但仍需填充 {fill_duration_needed:.2f}秒")
                                # 如果已经到达原始场景列表末尾但仍需填充，则回到开头继续查找未使用的场景
                                current_index = 0
                                while fill_duration_needed > 0.01 and current_index < last_scene_index:
                                    next_scene = all_scenes_list[current_index]
                                    next_scene_id = next_scene.get('scene_id')
                                    
                                    # 检查是否已被全局或字幕内部使用
                                    if (next_scene_id in globally_used_scene_ids or 
                                        next_scene_id in subtitle_used_scene_ids):
                                        logger.info(f"方案 #{plan_index+1}: 跳过已使用的场景: scene_id={next_scene_id}")
                                        current_index += 1
                                        continue
                                    
                                    scene_duration = get_scene_duration_seconds(next_scene)
                                    
                                    # 计算本次可以添加的时长 (不超过剩余需要填充的时长)
                                    duration_to_add = min(fill_duration_needed, scene_duration)
                                    
                                    # 添加场景（如果需要裁剪，记录裁剪信息）
                                    scene_to_add = next_scene.copy()
                                    if duration_to_add < scene_duration:
                                        scene_to_add['trim_duration'] = duration_to_add
                                        logger.info(f"方案 #{plan_index+1}: 从列表开头追加场景 scene_id={next_scene_id} (裁剪至 {duration_to_add:.2f}秒)")
                                    else:
                                        logger.info(f"方案 #{plan_index+1}: 从列表开头追加场景 scene_id={next_scene_id} (完整时长 {scene_duration:.2f}秒)")
                                    
                                    selected_scenes.append(scene_to_add)
                                    fill_duration_needed -= duration_to_add
                                    total_duration += duration_to_add
                                    
                                    # 将此场景标记为已使用
                                    if next_scene_id is not None:
                                        # 只有方案1才添加到全局已使用集合
                                        if plan_index == 0:
                                            globally_used_scene_ids.add(next_scene_id)
                                            logger.debug(f"方案 #{plan_index+1}: 将场景 scene_id={next_scene_id} 添加到全局已使用集合")
                                        # 所有方案都添加到字幕内部已使用集合
                                        subtitle_used_scene_ids.add(next_scene_id)
                                        logger.debug(f"方案 #{plan_index+1}: 将场景 scene_id={next_scene_id} 添加到字幕内部已使用集合")
                                    
                                    current_index += 1
                            
                            if fill_duration_needed > 0.01:
                                logger.warning(f"方案 #{plan_index+1}: 已尝试所有未使用场景，但仍需填充 {fill_duration_needed:.2f}秒，将使用最后一个场景的副本")
                                # 如果仍然无法填充完整，则使用最后一个场景的副本（作为最后的备选方案）
                                padding_scene = selected_scenes[-1].copy()
                                padding_scene['is_padding'] = True
                                padding_scene['trim_duration'] = fill_duration_needed
                                selected_scenes.append(padding_scene)
                                total_duration += fill_duration_needed
                                logger.info(f"方案 #{plan_index+1}: 添加最后一个场景的副本作为填充，填充时长: {fill_duration_needed:.2f}秒")
                            else:
                                logger.info(f"方案 #{plan_index+1}: 成功填充至目标时长")
                        else:
                            logger.warning(f"方案 #{plan_index+1}: 最后一个场景是原始列表中的最后一个场景或未找到，将从列表开头寻找未使用场景")
                            # 如果最后一个场景是原始列表中的最后一个场景，则从开头开始查找未使用的场景
                            fill_duration_needed = audio_duration - total_duration
                            current_index = 0
                            
                            while fill_duration_needed > 0.01 and current_index < len(all_scenes_list):
                                next_scene = all_scenes_list[current_index]
                                next_scene_id = next_scene.get('scene_id')
                                
                                # 检查是否已被全局或字幕内部使用
                                if (next_scene_id in globally_used_scene_ids or 
                                    next_scene_id in subtitle_used_scene_ids):
                                    logger.info(f"方案 #{plan_index+1}: 跳过已使用的场景: scene_id={next_scene_id}")
                                    current_index += 1
                                    continue
                                
                                scene_duration = get_scene_duration_seconds(next_scene)
                                
                                # 计算本次可以添加的时长 (不超过剩余需要填充的时长)
                                duration_to_add = min(fill_duration_needed, scene_duration)
                                
                                # 添加场景（如果需要裁剪，记录裁剪信息）
                                scene_to_add = next_scene.copy()
                                if duration_to_add < scene_duration:
                                    scene_to_add['trim_duration'] = duration_to_add
                                    logger.info(f"方案 #{plan_index+1}: 从列表开头追加场景 scene_id={next_scene_id} (裁剪至 {duration_to_add:.2f}秒)")
                                else:
                                    logger.info(f"方案 #{plan_index+1}: 从列表开头追加场景 scene_id={next_scene_id} (完整时长 {scene_duration:.2f}秒)")
                                
                                selected_scenes.append(scene_to_add)
                                fill_duration_needed -= duration_to_add
                                total_duration += duration_to_add
                                
                                # 将此场景标记为已使用
                                if next_scene_id is not None:
                                    # 只有方案1才添加到全局已使用集合
                                    if plan_index == 0:
                                        globally_used_scene_ids.add(next_scene_id)
                                        logger.debug(f"方案 #{plan_index+1}: 将场景 scene_id={next_scene_id} 添加到全局已使用集合")
                                    # 所有方案都添加到字幕内部已使用集合
                                    subtitle_used_scene_ids.add(next_scene_id)
                                    logger.debug(f"方案 #{plan_index+1}: 将场景 scene_id={next_scene_id} 添加到字幕内部已使用集合")
                                
                                current_index += 1
                            
                            if fill_duration_needed > 0.01:
                                logger.warning(f"方案 #{plan_index+1}: 已尝试所有未使用场景，但仍需填充 {fill_duration_needed:.2f}秒，将使用最后一个场景的副本")
                                # 如果仍然无法填充完整，则使用最后一个场景的副本（作为最后的备选方案）
                                padding_scene = selected_scenes[-1].copy()
                                padding_scene['is_padding'] = True
                                padding_scene['trim_duration'] = fill_duration_needed
                                selected_scenes.append(padding_scene)
                                total_duration += fill_duration_needed
                                logger.info(f"方案 #{plan_index+1}: 添加最后一个场景的副本作为填充，填充时长: {fill_duration_needed:.2f}秒")
                            else:
                                logger.info(f"方案 #{plan_index+1}: 成功填充至目标时长")
                    else:
                        logger.warning(f"方案 #{plan_index+1}: 最后一个场景没有场景ID，无法找到其在原始列表中的位置")
                else:
                    logger.warning(f"方案 #{plan_index+1}: 没有选择任何场景，无法进行填充")

            # 如果成功选择了场景且总时长大于音频时长，调整场景时长
            elif selected_scenes and total_duration > audio_duration:
                 logger.info(f"方案 #{plan_index+1}: 场景总时长({total_duration:.2f}秒)大于音频时长({audio_duration:.2f}秒)，需要裁剪")
                 selected_scenes = adjust_scenes_duration(selected_scenes, audio_duration)

            # 如果没有选择任何场景
            elif not selected_scenes:
                 logger.warning(f"方案 #{plan_index+1}: 未能选择任何场景。")

            # 重新计算调整后的总时长以供日志记录 (应对裁剪或填充后的最终时长)
            adjusted_total_duration = sum(get_scene_duration_seconds(s) if 'trim_duration' not in s else s['trim_duration'] for s in selected_scenes)
            logger.info(f"方案 #{plan_index+1} 调整/填充后最终总时长: {adjusted_total_duration:.2f}秒")

            # 添加到方案列表 (只添加包含场景的方案)
            if selected_scenes:
                 plans.append(selected_scenes)
                 logger.info(f"方案 #{plan_index+1} 添加到方案列表")
            else:
                 logger.warning(f"方案 #{plan_index+1} 未包含任何场景，未添加到方案列表")

        logger.info(f"========== 当前模式：字幕 #{subtitle['index']} 的 {len(plans)} 套有效场景方案生成完成 ==========")
        # 检查是否生成了任何有效方案
        if not plans:
             logger.error(f"字幕 #{subtitle['index']} 未能生成任何有效的场景方案。")
        return plans
    except Exception as e:
        logger.error(f"当前模式场景选择时出错: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return []

def process_subtitle(subtitle, scenes_data, output_dir, audio_info, globally_used_scene_ids):
    """
    处理单条字幕
    
    参数:
        subtitle (dict): 字幕信息
        scenes_data (dict): 场景数据
        output_dir (str): 输出目录
        audio_info (dict): 音频信息，包含每个字幕的语音时长
        globally_used_scene_ids (set): 全局已使用的场景ID集合
        
    返回:
        bool: 是否成功
    """
    # 声明全局变量引用
    global logger
    
    try:
        index = subtitle['index']
        text = subtitle['text']
        subtitle_numbers = subtitle['subtitle_numbers']
        
        # 创建专用日志处理器
        subtitle_logger, log_file = setup_subtitle_logger(index)
        
        # 记录基本信息到两个日志
        logger.info(f"\n========== 开始处理字幕 #{index} ==========")
        subtitle_logger.info(f"========== 字幕 #{index} 处理开始 ==========")
        subtitle_logger.info(f"字幕内容: {text}")
        subtitle_logger.info(f"字幕序号: {subtitle_numbers}")
        
        logger.info(f"字幕内容: {text[:50]}...")
        logger.info(f"字幕序号: {subtitle_numbers}")
        
        # 获取对应音频文件信息
        audio_data = audio_info.get('audio_durations', {}).get(index)
        if not audio_data:
            error_msg = f"找不到字幕 #{index} 的音频信息"
            logger.error(error_msg)
            subtitle_logger.error(error_msg)
            return False
        
        audio_duration = audio_data['duration']
        audio_path = audio_data['audio_path']
        
        # 记录音频信息
        for log in [logger, subtitle_logger]:
            log.info(f"音频文件详情:")
            log.info(f"  - 路径: {audio_path}")
            log.info(f"  - 时长: {audio_duration:.2f}秒")
        
        # 验证音频文件
        if os.path.exists(audio_path):
            # 使用pydub重新检查音频时长
            try:
                audio = AudioSegment.from_wav(audio_path)
                actual_audio_duration = len(audio) / 1000.0  # 转换为秒
                
                for log in [logger, subtitle_logger]:
                    log.info(f"  - 验证音频时长: {actual_audio_duration:.2f}秒")
                
                if abs(actual_audio_duration - audio_duration) > 0.1:
                    warning_msg = f"  - 警告: 记录的音频时长({audio_duration:.2f}秒)与实际文件时长({actual_audio_duration:.2f}秒)不一致"
                    logger.warning(warning_msg)
                    subtitle_logger.warning(warning_msg)
                    
                    # 使用实际测量的音频时长
                    audio_duration = actual_audio_duration
                    
                    for log in [logger, subtitle_logger]:
                        log.info(f"  - 已更新为使用实际测量的音频时长: {audio_duration:.2f}秒")
            except Exception as e:
                error_msg = f"  - 验证音频文件时出错: {e}"
                logger.error(error_msg)
                subtitle_logger.error(error_msg)
        else:
            error_msg = f"  - 音频文件不存在: {audio_path}"
            logger.error(error_msg)
            subtitle_logger.error(error_msg)
            return False
        
        # 检查字幕文件中的时间戳信息
        combined_srt = audio_info.get('combined_srt')
        en_srt = audio_info.get('en_srt')
        
        for log in [logger, subtitle_logger]:
            log.info(f"字幕时间戳信息:")
            log.info(f"  - 中文字幕文件: {combined_srt}")
            log.info(f"  - 英文字幕文件: {en_srt}")
            log.info(f"  - 根据生成的音频时长({audio_duration:.2f}秒)已调整字幕时间戳")
        
        # 使用优化的场景选择算法生成多套方案
        # 修改全局日志器引用，使scene_selection函数也能记录到专用日志
        original_logger = logger
        logger = subtitle_logger
        
        # 传递 globally_used_scene_ids 集合
        scene_plans = optimize_scene_selection(subtitle, scenes_data, audio_duration, num_plans=6, globally_used_scene_ids=globally_used_scene_ids)
        
        # 恢复全局日志器
        logger = original_logger
        
        if not scene_plans:
            error_msg = f"字幕 #{index} 没有生成有效的场景方案"
            logger.error(error_msg)
            subtitle_logger.error(error_msg)
            return False
        
        # 处理每一套方案
        success_count = 0
        video_files = []
        
        for plan_index, scenes in enumerate(scene_plans):
            plan_num = plan_index + 1
            
            for log in [logger, subtitle_logger]:
                log.info(f"\n----- 处理字幕 #{index} 的方案 #{plan_num} -----")
            
            # 合并场景
            video_output_path = os.path.join(output_dir, f"{index}_{plan_num}.mp4")
            
            for log in [logger, subtitle_logger]:
                log.info(f"目标视频文件: {video_output_path}")
            
            # 临时修改全局日志器引用
            original_logger = logger
            logger = subtitle_logger
            
            result = merge_scenes(scenes, video_output_path, audio_duration)
            
            # 恢复全局日志器
            logger = original_logger
            
            if result:
                success_count += 1
                video_files.append(video_output_path)
                
                # 验证视频文件
                verify_command = [
                    "ffprobe", "-v", "error", 
                    "-show_entries", "format=duration", 
                    "-of", "default=noprint_wrappers=1:nokey=1", 
                    video_output_path
                ]
                verify_process = subprocess.run(verify_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                
                if verify_process.returncode == 0:
                    video_duration = float(verify_process.stdout.decode('utf-8').strip())
                    
                    for log in [logger, subtitle_logger]:
                        log.info(f"方案 #{plan_num} 处理完成:")
                        log.info(f"  - 音频时长: {audio_duration:.2f}秒")
                        log.info(f"  - 视频时长: {video_duration:.2f}秒")
                        log.info(f"  - 时长差异: {abs(video_duration - audio_duration):.2f}秒 ({(abs(video_duration - audio_duration)/audio_duration)*100:.2f}%)")
                else:
                    error_msg = f"无法验证视频时长: {verify_process.stderr.decode('utf-8', errors='ignore')}"
                    logger.error(error_msg)
                    subtitle_logger.error(error_msg)
            else:
                error_msg = f"无法为字幕 #{index} 的方案 #{plan_num} 合并场景"
                logger.error(error_msg)
                subtitle_logger.error(error_msg)
        
        if success_count > 0:
            for log in [logger, subtitle_logger]:
                log.info(f"\n字幕 #{index} 处理完成，成功生成 {success_count}/{len(scene_plans)} 套方案")
                log.info(f"生成的视频文件:")
                
                for i, video_file in enumerate(video_files, 1):
                    log.info(f"  {i}. {video_file}")
                    
                log.info(f"========== 字幕 #{index} 处理结束 ==========\n")
                
            # 记录日志文件位置，方便用户查看
            logger.info(f"详细处理日志已保存到文件: {log_file}")
            
            return True
        else:
            error_msg = f"字幕 #{index} 的所有方案处理失败"
            logger.error(error_msg)
            subtitle_logger.error(error_msg)
            
            for log in [logger, subtitle_logger]:
                log.info(f"========== 字幕 #{index} 处理结束 ==========\n")
                
            return False
    except Exception as e:
        error_msg = f"处理字幕时出错: {e}\n{traceback.format_exc()}"
        logger.error(error_msg)
        
        # 如果已创建字幕专用日志器，也记录错误
        if 'subtitle_logger' in locals():
            subtitle_logger.error(error_msg)
            
        return False

def adjust_subtitle_timestamps(srt_path, audio_durations, output_path=None):
    """
    根据实际生成的语音时长调整字幕文件的时间戳
    
    参数:
        srt_path (str): 原始字幕文件路径
        audio_durations (dict): 每个字幕ID对应的音频时长字典，格式为 {id: {'duration': 秒数}}
        output_path (str): 输出调整后的字幕文件路径，如果为None则覆盖原文件
        
    返回:
        str: 调整后的字幕文件路径
    """
    try:
        logger.info("\n======== 开始调整字幕时间戳 ========")
        logger.info(f"字幕文件: {srt_path}")
        
        # 如果没有指定输出路径，默认覆盖原文件
        if output_path is None:
            output_path = srt_path
            
        # 读取原SRT文件
        with open(srt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 解析SRT文件内容
        subtitles = []
        current_subtitle = {}
        line_index = 0
        
        while line_index < len(lines):
            line = lines[line_index].strip()
            
            # 字幕ID
            if line and line.isdigit():
                if current_subtitle:
                    subtitles.append(current_subtitle)
                current_subtitle = {'id': int(line), 'lines': []}
                line_index += 1
                continue
                
            # 时间戳
            if '-->' in line:
                current_subtitle['timestamp'] = line
                line_index += 1
                continue
                
            # 字幕内容
            if line:
                current_subtitle['lines'].append(line)
                line_index += 1
                continue
                
            # 空行，移到下一个字幕
            line_index += 1
            
        # 添加最后一个字幕
        if current_subtitle:
            subtitles.append(current_subtitle)
            
        # 根据实际音频时长调整时间戳
        logger.info(f"调整前字幕数量: {len(subtitles)}")
        current_time = 0.0
        
        adjusted_subtitles = []
        for subtitle in subtitles:
            subtitle_id = subtitle['id']
            
            # 获取此字幕对应的音频时长
            if subtitle_id in audio_durations:
                audio_duration = audio_durations[subtitle_id]['duration']
                logger.info(f"字幕 #{subtitle_id}: 音频时长 = {audio_duration:.2f}秒")
                
                # 设置新的时间戳
                start_time_str = format_time(current_time)
                end_time_str = format_time(current_time + audio_duration)
                
                subtitle['new_timestamp'] = f"{start_time_str} --> {end_time_str}"
                
                # 更新当前时间
                current_time += audio_duration
                
                adjusted_subtitles.append(subtitle)
            else:
                logger.warning(f"字幕 #{subtitle_id} 没有对应的音频时长信息，将保留原时间戳")
                adjusted_subtitles.append(subtitle)
                
        # 写入调整后的SRT文件
        with open(output_path, 'w', encoding='utf-8') as f:
            for subtitle in adjusted_subtitles:
                # 写入ID
                f.write(f"{subtitle['id']}\n")
                
                # 写入时间戳（优先使用新的时间戳）
                if 'new_timestamp' in subtitle:
                    f.write(f"{subtitle['new_timestamp']}\n")
                else:
                    f.write(f"{subtitle['timestamp']}\n")
                    
                # 写入字幕内容
                for line in subtitle['lines']:
                    f.write(f"{line}\n")
                    
                # 写入空行
                f.write("\n")
                
        logger.info(f"已调整 {len(adjusted_subtitles)} 条字幕的时间戳")
        logger.info(f"调整后的字幕文件已保存到: {output_path}")
        logger.info("======== 字幕时间戳调整完成 ========\n")
        
        return output_path
    except Exception as e:
        logger.error(f"调整字幕时间戳时出错: {e}")
        logger.error(traceback.format_exc())
        return srt_path

def merge_wav_files(audio_durations, output_path):
    """
    将所有WAV文件按照字幕编号顺序合并成一个文件
    
    参数:
        audio_durations (dict): 音频时长信息字典，包含每个音频文件的路径
        output_path (str): 合并后的音频文件保存路径
        
    返回:
        bool: 是否成功
    """
    try:
        from pydub import AudioSegment
        
        logger.info("\n======== 开始合并音频文件 ========")
        logger.info(f"目标输出文件: {output_path}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 按字幕编号排序
        sorted_indices = sorted(audio_durations.keys())
        if not sorted_indices:
            logger.error("没有找到任何音频文件")
            return False
            
        # 创建一个空的音频段
        merged_audio = None
        
        # 合并所有音频文件
        for index in sorted_indices:
            audio_path = audio_durations[index]["audio_path"]
            if not os.path.exists(audio_path):
                logger.error(f"音频文件不存在: {audio_path}")
                return False
                
            try:
                # 加载音频文件
                audio = AudioSegment.from_wav(audio_path)
                
                # 如果是第一个文件，直接赋值
                if merged_audio is None:
                    merged_audio = audio
                else:
                    # 否则追加到已有音频
                    merged_audio += audio
                    
                logger.info(f"已合并音频文件: {audio_path}")
                
            except Exception as e:
                logger.error(f"处理音频文件时出错 {audio_path}: {e}")
                return False
        
        if merged_audio is None:
            logger.error("没有成功加载任何音频文件")
            return False
            
        # 保存合并后的音频
        try:
            merged_audio.export(output_path, format="wav")
            logger.info(f"合并后的音频已保存到: {output_path}")
            logger.info(f"合并后音频总时长: {len(merged_audio)/1000.0:.2f}秒")
            return True
        except Exception as e:
            logger.error(f"保存合并音频时出错: {e}")
            return False
            
    except Exception as e:
        logger.error(f"合并音频文件时出错: {e}")
        return False

def main():
    """主函数"""
    global TRANSLATION_API_MODE, DEFAULT_AUDIO_OUTPUT_DIR, MIN_SPEED, MAX_SPEED, DEFAULT_SPEED, TARGET_DURATION
    global OLLAMA_API_URL, OLLAMA_MODEL, LM_STUDIO_API_URL, LM_STUDIO_MODEL
    global GEMINI_BALANCE_API_URL, GEMINI_BALANCE_API_KEY, GEMINI_BALANCE_MODEL
    global USE_ADAPTIVE_SPEED, FIXED_SPEED, SCENE_MATCHING_MODE

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='AI视频生成工具')
    parser.add_argument('--srt', default=DEFAULT_SRT_PATH, help='输入字幕文件路径')
    parser.add_argument('--json', default=DEFAULT_SCENES_JSON_PATH, help='场景JSON文件路径')
    parser.add_argument('--output', default=DEFAULT_OUTPUT_DIR, help='输出目录')
    parser.add_argument('--debug', action='store_true', help='启用详细调试日志')
    parser.add_argument('--api-mode', type=int, choices=[0, 1, 2, 3], default=TRANSLATION_API_MODE, help='翻译API模式 (0: 现有API, 1: Ollama API, 2: LM Studio API, 3: Gemini Balance API)')

    # 场景匹配模式配置参数
    parser.add_argument('--scene-mode', type=int, choices=[0, 1], default=SCENE_MATCHING_MODE, help='场景匹配模式 (0: 当前模式6套方案, 1: 新模式4套方案单序号匹配)')

    # 新增的路径配置参数
    parser.add_argument('--audio-output-dir', default=DEFAULT_AUDIO_OUTPUT_DIR, help='音频输出目录')
    
    # 新增的语音合成配置参数
    parser.add_argument('--min-speed', type=float, default=MIN_SPEED, help='最低语速限制')
    parser.add_argument('--max-speed', type=float, default=MAX_SPEED, help='最高语速限制')
    parser.add_argument('--default-speed', type=float, default=DEFAULT_SPEED, help='默认语速')
    parser.add_argument('--target-duration', type=int, default=TARGET_DURATION, help='目标时长（秒）')
    parser.add_argument('--use-adaptive-speed', action='store_true', default=USE_ADAPTIVE_SPEED, help='使用自适应语速模式（自动调整语速适应目标时长）')
    parser.add_argument('--use-fixed-speed', action='store_true', help='使用固定语速模式（使用固定语速配音，不限制时长）')
    parser.add_argument('--fixed-speed', type=float, default=FIXED_SPEED, help='固定语速模式下使用的语速')
    
    # 新增的翻译API配置参数
    parser.add_argument('--ollama-api-url', default=OLLAMA_API_URL, help='Ollama API地址')
    parser.add_argument('--ollama-model', default=OLLAMA_MODEL, help='Ollama模型名称')
    parser.add_argument('--lm-studio-api-url', default=LM_STUDIO_API_URL, help='LM Studio API地址')
    parser.add_argument('--lm-studio-model', default=LM_STUDIO_MODEL, help='LM Studio模型名称')
    parser.add_argument('--gemini-api-url', default=GEMINI_BALANCE_API_URL, help='Gemini Balance API地址')
    parser.add_argument('--gemini-api-key', default=GEMINI_BALANCE_API_KEY, help='Gemini Balance API密钥')
    parser.add_argument('--gemini-model', default=GEMINI_BALANCE_MODEL, help='Gemini Balance模型名称')
    
    args = parser.parse_args()

    # 更新全局变量
    TRANSLATION_API_MODE = args.api_mode
    SCENE_MATCHING_MODE = args.scene_mode
    DEFAULT_AUDIO_OUTPUT_DIR = args.audio_output_dir
    MIN_SPEED = args.min_speed
    MAX_SPEED = args.max_speed
    DEFAULT_SPEED = args.default_speed
    TARGET_DURATION = args.target_duration
    # 处理语速模式参数
    if hasattr(args, 'use_fixed_speed') and args.use_fixed_speed:
        USE_ADAPTIVE_SPEED = False
    elif hasattr(args, 'use_adaptive_speed') and args.use_adaptive_speed:
        USE_ADAPTIVE_SPEED = True
    # 如果都没有指定，保持默认值

    FIXED_SPEED = getattr(args, 'fixed_speed', FIXED_SPEED)
    OLLAMA_API_URL = args.ollama_api_url
    OLLAMA_MODEL = args.ollama_model
    LM_STUDIO_API_URL = args.lm_studio_api_url
    LM_STUDIO_MODEL = args.lm_studio_model
    GEMINI_BALANCE_API_URL = args.gemini_api_url
    GEMINI_BALANCE_API_KEY = args.gemini_api_key
    GEMINI_BALANCE_MODEL = args.gemini_model

    # 如果启用了调试模式，设置日志级别为DEBUG
    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("调试模式已启用，将显示更详细的日志信息")

    # 显示配置参数
    logger.info("=" * 50)
    logger.info("AI视频生成器配置参数:")
    logger.info(f"  翻译API模式: {TRANSLATION_API_MODE}")
    logger.info(f"  场景匹配模式: {SCENE_MATCHING_MODE} ({'当前模式(6套方案)' if SCENE_MATCHING_MODE == 0 else '新模式(4套方案,单序号匹配)'})")
    if USE_ADAPTIVE_SPEED:
        logger.info(f"  语音合成模式: 自适应语速模式")
        logger.info(f"  语音合成配置: 最低语速={MIN_SPEED}, 最高语速={MAX_SPEED}, 默认语速={DEFAULT_SPEED}, 目标时长={TARGET_DURATION}秒")
    else:
        logger.info(f"  语音合成模式: 固定语速模式")
        logger.info(f"  语音合成配置: 固定语速={FIXED_SPEED}, 不限制目标时长")
    logger.info(f"  Ollama配置: URL={OLLAMA_API_URL}, 模型={OLLAMA_MODEL}")
    logger.info(f"  LM Studio配置: URL={LM_STUDIO_API_URL}, 模型={LM_STUDIO_MODEL}")
    logger.info(f"  Gemini配置: URL={GEMINI_BALANCE_API_URL}, 模型={GEMINI_BALANCE_MODEL}")
    logger.info("=" * 50)

    # 设置路径
    srt_path = args.srt
    json_path = args.json
    output_dir = args.output
    audio_dir = DEFAULT_AUDIO_OUTPUT_DIR
    print("开始关闭capcut窗口")
    close_capcut_hybrid()
    print("关闭capcut窗口完成")  
    # 程序开始前清空指定目录
    logger.info("\n" + "="*60)
    logger.info("【预处理】清空指定目录")
    logger.info("="*60)
    
    # 定义需要清空的目录列表
    directories_to_clear = [
        r"F:\github\aicut_auto\newcut_ai",
        r"F:\github\aicut_auto\output"
    ]
    
    for directory in directories_to_clear:
        try:
            logger.info(f"正在清空目录: {directory}")
            clear_directory(directory)
            logger.info(f"目录清空完成: {directory}")
        except Exception as e:
            logger.error(f"清空目录时出错 {directory}: {e}")
            logger.error(traceback.format_exc())
    
    logger.info("=" * 60)
    logger.info("【预处理】目录清空完成")
    logger.info("=" * 60 + "\n")
    flush_all_logs()  # 确保清空目录的日志立即显示

    # 清空日志目录
    if os.path.exists(log_dir):
        for file in os.listdir(log_dir):
            file_path = os.path.join(log_dir, file)
            if os.path.isfile(file_path):
                os.unlink(file_path)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(audio_dir, exist_ok=True)
    
    logger.info("=" * 50)
    logger.info("AI视频生成器启动")
    logger.info(f"字幕文件: {srt_path}")
    logger.info(f"场景JSON: {json_path}")
    logger.info(f"输出目录: {output_dir}")
    logger.info(f"音频目录: {audio_dir}")
    logger.info(f"日志目录: {log_dir}")
    logger.info("=" * 50)
    
    # 验证字幕文件是否存在
    if not os.path.exists(srt_path):
        logger.error(f"错误: 字幕文件 {srt_path} 不存在!")
        return
    
    # 检查字幕文件内容并显示前几行
    try:
        with open(srt_path, 'r', encoding='utf-8') as f:
            sample_lines = f.readlines()[:5]  # 获取前5行
            logger.info(f"字幕文件样本内容 (前{len(sample_lines)}行):")
            for i, line in enumerate(sample_lines):
                logger.info(f"  行 {i+1}: {line.strip()}")
    except Exception as e:
        logger.error(f"读取字幕文件时出错: {e}")
    
    # 加载语音合成模块
    sound_module = load_sound_hecheng_module()
    if not sound_module:
        logger.error("无法加载语音合成模块，程序终止")
        return
    
    # 解析字幕
    logger.info("开始解析字幕文件...")
    subtitles = parse_new_subtitle_format(srt_path)
    if not subtitles:
        logger.error("没有解析到有效字幕，程序终止")
        return
    
    logger.info(f"成功解析 {len(subtitles)} 条字幕")
    logger.info("解析的第一条字幕示例:")
    if subtitles:
        first = subtitles[0]
        logger.info(f"  索引: {first['index']}")
        logger.info(f"  序号: {first['subtitle_numbers']}")
        logger.info(f"  文本: {first['text']}")
    
    # 加载场景数据
    scenes_data = load_scenes_json(json_path)
    if not scenes_data:
        logger.error("无法加载场景数据，程序终止")
        return
    
    # 添加明确的阶段标记
    logger.info("\n" + "="*60)
    logger.info("【阶段1】开始语音合成")
    logger.info("="*60)
    flush_all_logs()  # 确保阶段标记立即显示
    
    # 按照原始流程生成语音
    logger.info("=== 开始按照原始流程生成语音 ===")
    audio_result = generate_audio_for_subtitles(subtitles, audio_dir, sound_module)
    
    logger.info("\n" + "="*60)
    logger.info("【阶段1】语音合成结束")
    logger.info("="*60 + "\n")
    flush_all_logs()  # 确保语音合成结束的标记立即显示
    
    if not audio_result['success']:
        logger.error(f"语音生成失败: {audio_result.get('reason', '未知原因')}")
        return
    
    logger.info(f"所有字幕的语音生成成功: 总时长={audio_result['total_duration']:.2f}秒, 语速={audio_result['speed']}")

    # 添加合成音频总长度和语速的输出，区分两种模式
    if audio_result and audio_result['success']:
        if USE_ADAPTIVE_SPEED:
            logger.info("🎯 自适应语速模式 - 语音合成结果:")
            logger.info(f"   ✅ 合成音频总长度: {audio_result['total_duration']:.2f} 秒 (目标: {TARGET_DURATION} 秒)")
            logger.info(f"   ✅ 自动调整语速: {audio_result['speed']:.2f} (范围: {MIN_SPEED}-{MAX_SPEED})")
            logger.info(f"   ✅ 时长控制: 成功将语音时长控制在目标范围内")
        else:
            logger.info("🔧 固定语速模式 - 语音合成结果:")
            logger.info(f"   ✅ 合成音频总长度: {audio_result['total_duration']:.2f} 秒 (不限制目标时长)")
            logger.info(f"   ✅ 固定语速: {audio_result['speed']:.2f} (固定值: {FIXED_SPEED})")
            logger.info(f"   ✅ 时长策略: 根据实际配音时长匹配场景，不受60秒限制")

        # 输出详细的语音合成参数供后端提取
        logger.info("VOICE_PARAMS_START")
        logger.info(f"VOICE_SPEED: {audio_result['speed']:.2f}")
        logger.info(f"VOICE_ENGINE: kokoro")
        logger.info(f"VOICE_SPEAKER: am_adam_男.pt")
        logger.info(f"VOICE_OUTPUT_DIR: {audio_dir}")
        logger.info(f"VOICE_TOTAL_DURATION: {audio_result['total_duration']:.2f}")
        logger.info(f"VOICE_MODE: {'adaptive' if USE_ADAPTIVE_SPEED else 'fixed'}")
        logger.info(f"VOICE_TARGET_DURATION: {TARGET_DURATION if USE_ADAPTIVE_SPEED else 'unlimited'}")
        logger.info("VOICE_PARAMS_END")
    else:
        logger.warning("无法获取合成音频总长度和语速信息。")

    # 添加明确的阶段标记
    logger.info("\n" + "="*60)
    logger.info("【阶段2】开始场景匹配和视频生成")
    logger.info("="*60)
    flush_all_logs()  # 确保阶段标记立即显示

    # 初始化用于跟踪被用作延伸的场景ID集合
    # 修改：初始化用于跟踪所有已被使用的场景ID集合
    globally_used_scene_ids = set()
    logger.info("初始化全局已使用的场景ID集合")

    # 清空输出目录
    clear_directory(output_dir)

    # 处理每条字幕
    success_count = 0
    for subtitle in subtitles:
        # 传递 globally_used_scene_ids 集合
        if process_subtitle(subtitle, scenes_data, output_dir, audio_result, globally_used_scene_ids):
            success_count += 1
        flush_all_logs()  # 确保每个字幕处理的日志立即显示

    logger.info("\n" + "="*60)
    logger.info("【阶段2】场景匹配和视频生成结束")
    logger.info("="*60)
    flush_all_logs()  # 确保阶段结束标记立即显示

    logger.info("=" * 50)
    logger.info(f"处理完成: 成功 {success_count}/{len(subtitles)} 条字幕")
    logger.info(f"各字幕的详细日志已保存在目录: {os.path.abspath(log_dir)}")
        # 添加明确的阶段标记
    logger.info("\n" + "="*60)
    logger.info("【阶段3】开始生成CapCut JSON")
    logger.info("="*60)
    flush_all_logs()  # 确保阶段标记立即显示
    # === 添加调用 CapCut JSON 生成脚本的代码 ===
    logger.info("=== 调用 CapCut JSON 生成脚本 ===")
    # 构建命令字符串
    # 使用 os.path.join 确保路径兼容性，并用引号包围路径以处理空格或特殊字符
    capcut_script_path = os.path.join("草稿模板", "5-1_generate_capcut_json.py")
    video_output_dir = os.path.abspath(output_dir) # 使用绝对路径
    # combined.srt 文件生成在 audio_dir 下的 'newcut' 子目录里，然后由 adjust_subtitle_timestamps 保存到 audio_dir 的父目录 F:\github\aicut_auto\newcut\combined.srt
    # 实际路径应该是 F:\github\aicut_auto\newcut\combined.srt
    subtitle_file_path = os.path.abspath(os.path.join(os.path.dirname(audio_dir), "newcut", "combined.srt"))


    capcut_command_list = [
        "python",
        capcut_script_path,
        "--video_dir", video_output_dir, # 直接传递路径字符串
        "--subtitle_file", subtitle_file_path, # 直接传递路径字符串
        "--water_remove", "False",
        "--enable_random_flip", "False",
        "--disable_close_capcut", "True",
        "--ENABLE_WORD_LEVEL_SUBTITLES", "False",
        "--ENABLE_SUBTITLE_EFFECTS", "False",
        "--ENABLE_QUALITY_ENHANCE", "False",
        "--ENABLE_NOISE_REDUCTION", "False",
        "--enable_audio_alignment", "False",
  
    ]


    logger.info(f"执行命令: {' '.join(capcut_command_list)}")
    flush_all_logs()  # 确保命令日志立即显示

    try:
        # 设置环境变量以禁用调试器警告
        env = os.environ.copy()
        env['PYDEVD_DISABLE_FILE_VALIDATION'] = '1'
        logger.debug("已设置环境变量 PYDEVD_DISABLE_FILE_VALIDATION=1")

        # 使用 subprocess.run 执行命令
        # capture_output=True 捕获标准输出和标准错误
        # text=True 解码输出为文本 
        # 添加 env=env 参数
        process = subprocess.run(capcut_command_list, capture_output=True, text=True, check=True, env=env)

        logger.info("CapCut JSON 生成脚本执行成功")
        logger.info("标准输出:")
        logger.info(process.stdout)
        logger.info("标准错误:")
        logger.info(process.stderr)
        flush_all_logs()  # 确保输出日志立即显示

    except FileNotFoundError:
        logger.error(f"错误: 未找到 python 或脚本文件 {capcut_script_path}。请确保它们在 PATH 中或指定完整路径。")
    except subprocess.CalledProcessError as e:
        logger.error(f"错误: CapCut JSON 生成脚本执行失败，返回码 {e.returncode}")
        logger.error("标准输出:")
        logger.error(e.stdout)
        logger.error("标准错误:")
        logger.error(e.stderr)
    except Exception as e:
        logger.error(f"执行 CapCut JSON 生成脚本时发生未知错误: {e}")
        logger.error(traceback.format_exc())
    
    logger.info("\n" + "="*60)
    logger.info("【阶段3】CapCut JSON生成结束")
    logger.info("="*60)
    logger.info("\n所有处理完成！")
    flush_all_logs()  # 确保最终日志立即显示


if __name__ == "__main__":
    main() 