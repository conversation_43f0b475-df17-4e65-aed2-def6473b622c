2025-07-29 10:41:08,974 - INFO - ========== 字幕 #41 处理开始 ==========
2025-07-29 10:41:08,974 - INFO - 字幕内容: 摄政王带来罪魁祸首，哥哥怒不可遏，当场动手。
2025-07-29 10:41:08,974 - INFO - 字幕序号: [234, 239]
2025-07-29 10:41:08,975 - INFO - 音频文件详情:
2025-07-29 10:41:08,975 - INFO -   - 路径: output\41.wav
2025-07-29 10:41:08,975 - INFO -   - 时长: 3.15秒
2025-07-29 10:41:08,975 - INFO -   - 验证音频时长: 3.15秒
2025-07-29 10:41:08,975 - INFO - 字幕时间戳信息:
2025-07-29 10:41:08,975 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:08,975 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:08,975 - INFO -   - 根据生成的音频时长(3.15秒)已调整字幕时间戳
2025-07-29 10:41:08,975 - INFO - ========== 新模式：为字幕 #41 生成4套场景方案 ==========
2025-07-29 10:41:08,975 - INFO - 字幕序号列表: [234, 239]
2025-07-29 10:41:08,975 - INFO - 
--- 生成方案 #1：基于字幕序号 #234 ---
2025-07-29 10:41:08,975 - INFO - 开始为单个字幕序号 #234 匹配场景，目标时长: 3.15秒
2025-07-29 10:41:08,975 - INFO - 开始查找字幕序号 [234] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:08,975 - INFO - 找到related_overlap场景: scene_id=349, 字幕#234
2025-07-29 10:41:08,976 - INFO - 找到related_between场景: scene_id=348, 字幕#234
2025-07-29 10:41:08,976 - INFO - 字幕 #234 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:08,976 - INFO - 字幕序号 #234 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 10:41:08,976 - INFO - 选择第一个overlap场景作为起点: scene_id=349
2025-07-29 10:41:08,976 - INFO - 添加起点场景: scene_id=349, 时长=3.24秒, 累计时长=3.24秒
2025-07-29 10:41:08,976 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:08,976 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:41:08,976 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:41:08,976 - INFO - 
--- 生成方案 #2：基于字幕序号 #239 ---
2025-07-29 10:41:08,976 - INFO - 开始为单个字幕序号 #239 匹配场景，目标时长: 3.15秒
2025-07-29 10:41:08,976 - INFO - 开始查找字幕序号 [239] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:08,976 - INFO - 找到related_overlap场景: scene_id=358, 字幕#239
2025-07-29 10:41:08,976 - INFO - 找到related_overlap场景: scene_id=359, 字幕#239
2025-07-29 10:41:08,978 - INFO - 字幕 #239 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:08,978 - INFO - 字幕序号 #239 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:08,978 - INFO - 选择第一个overlap场景作为起点: scene_id=358
2025-07-29 10:41:08,978 - INFO - 添加起点场景: scene_id=358, 时长=1.16秒, 累计时长=1.16秒
2025-07-29 10:41:08,978 - INFO - 起点场景时长不足，需要延伸填充 1.99秒
2025-07-29 10:41:08,978 - INFO - 起点场景在原始列表中的索引: 357
2025-07-29 10:41:08,978 - INFO - 延伸添加场景: scene_id=359 (裁剪至 1.99秒)
2025-07-29 10:41:08,978 - INFO - 累计时长: 3.15秒
2025-07-29 10:41:08,978 - INFO - 字幕序号 #239 场景匹配完成，共选择 2 个场景，总时长: 3.15秒
2025-07-29 10:41:08,978 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:08,978 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:08,978 - INFO - ========== 当前模式：为字幕 #41 生成 1 套场景方案 ==========
2025-07-29 10:41:08,978 - INFO - 开始查找字幕序号 [234, 239] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:08,979 - INFO - 找到related_overlap场景: scene_id=349, 字幕#234
2025-07-29 10:41:08,979 - INFO - 找到related_overlap场景: scene_id=358, 字幕#239
2025-07-29 10:41:08,979 - INFO - 找到related_overlap场景: scene_id=359, 字幕#239
2025-07-29 10:41:08,979 - INFO - 找到related_between场景: scene_id=348, 字幕#234
2025-07-29 10:41:08,980 - INFO - 字幕 #234 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:08,980 - INFO - 字幕 #239 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:08,980 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 10:41:08,980 - INFO - 开始生成方案 #1
2025-07-29 10:41:08,980 - INFO - 方案 #1: 为字幕#234选择初始化overlap场景id=349
2025-07-29 10:41:08,980 - INFO - 方案 #1: 为字幕#239选择初始化overlap场景id=358
2025-07-29 10:41:08,980 - INFO - 方案 #1: 初始选择后，当前总时长=4.40秒
2025-07-29 10:41:08,980 - INFO - 方案 #1: 额外between选择后，当前总时长=4.40秒
2025-07-29 10:41:08,980 - INFO - 方案 #1: 场景总时长(4.40秒)大于音频时长(3.15秒)，需要裁剪
2025-07-29 10:41:08,980 - INFO - 调整前总时长: 4.40秒, 目标时长: 3.15秒
2025-07-29 10:41:08,980 - INFO - 需要裁剪 1.25秒
2025-07-29 10:41:08,980 - INFO - 裁剪最长场景ID=349：从3.24秒裁剪至1.99秒
2025-07-29 10:41:08,980 - INFO - 调整后总时长: 3.15秒，与目标时长差异: 0.00秒
2025-07-29 10:41:08,980 - INFO - 方案 #1 调整/填充后最终总时长: 3.15秒
2025-07-29 10:41:08,980 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:08,980 - INFO - ========== 当前模式：字幕 #41 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:08,980 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:08,980 - INFO - ========== 新模式：字幕 #41 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:08,980 - INFO - 
----- 处理字幕 #41 的方案 #1 -----
2025-07-29 10:41:08,980 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-29 10:41:08,980 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphjbp73c8
2025-07-29 10:41:08,981 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\349.mp4 (确认存在: True)
2025-07-29 10:41:08,981 - INFO - 添加场景ID=349，时长=3.24秒，累计时长=3.24秒
2025-07-29 10:41:08,981 - INFO - 准备合并 1 个场景文件，总时长约 3.24秒
2025-07-29 10:41:08,981 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/349.mp4'

2025-07-29 10:41:08,981 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphjbp73c8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphjbp73c8\temp_combined.mp4
2025-07-29 10:41:09,111 - INFO - 合并后的视频时长: 3.26秒，目标音频时长: 3.15秒
2025-07-29 10:41:09,111 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphjbp73c8\temp_combined.mp4 -ss 0 -to 3.147 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-29 10:41:09,372 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:09,372 - INFO - 目标音频时长: 3.15秒
2025-07-29 10:41:09,372 - INFO - 实际视频时长: 3.18秒
2025-07-29 10:41:09,372 - INFO - 时长差异: 0.04秒 (1.14%)
2025-07-29 10:41:09,372 - INFO - ==========================================
2025-07-29 10:41:09,372 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:09,372 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-29 10:41:09,374 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphjbp73c8
2025-07-29 10:41:09,419 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:09,419 - INFO -   - 音频时长: 3.15秒
2025-07-29 10:41:09,419 - INFO -   - 视频时长: 3.18秒
2025-07-29 10:41:09,419 - INFO -   - 时长差异: 0.04秒 (1.14%)
2025-07-29 10:41:09,419 - INFO - 
----- 处理字幕 #41 的方案 #2 -----
2025-07-29 10:41:09,419 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-07-29 10:41:09,420 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmmz2idp3
2025-07-29 10:41:09,420 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\358.mp4 (确认存在: True)
2025-07-29 10:41:09,420 - INFO - 添加场景ID=358，时长=1.16秒，累计时长=1.16秒
2025-07-29 10:41:09,420 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\359.mp4 (确认存在: True)
2025-07-29 10:41:09,420 - INFO - 添加场景ID=359，时长=2.84秒，累计时长=4.00秒
2025-07-29 10:41:09,420 - INFO - 准备合并 2 个场景文件，总时长约 4.00秒
2025-07-29 10:41:09,421 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/358.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/359.mp4'

2025-07-29 10:41:09,421 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmmz2idp3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmmz2idp3\temp_combined.mp4
2025-07-29 10:41:09,546 - INFO - 合并后的视频时长: 4.05秒，目标音频时长: 3.15秒
2025-07-29 10:41:09,546 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmmz2idp3\temp_combined.mp4 -ss 0 -to 3.147 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-07-29 10:41:09,807 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:09,807 - INFO - 目标音频时长: 3.15秒
2025-07-29 10:41:09,807 - INFO - 实际视频时长: 3.18秒
2025-07-29 10:41:09,807 - INFO - 时长差异: 0.04秒 (1.14%)
2025-07-29 10:41:09,807 - INFO - ==========================================
2025-07-29 10:41:09,807 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:09,807 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-07-29 10:41:09,808 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmmz2idp3
2025-07-29 10:41:09,853 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:09,853 - INFO -   - 音频时长: 3.15秒
2025-07-29 10:41:09,853 - INFO -   - 视频时长: 3.18秒
2025-07-29 10:41:09,853 - INFO -   - 时长差异: 0.04秒 (1.14%)
2025-07-29 10:41:09,853 - INFO - 
----- 处理字幕 #41 的方案 #3 -----
2025-07-29 10:41:09,853 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-07-29 10:41:09,854 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfyiuqxdi
2025-07-29 10:41:09,854 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\349.mp4 (确认存在: True)
2025-07-29 10:41:09,854 - INFO - 添加场景ID=349，时长=3.24秒，累计时长=3.24秒
2025-07-29 10:41:09,854 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\358.mp4 (确认存在: True)
2025-07-29 10:41:09,854 - INFO - 添加场景ID=358，时长=1.16秒，累计时长=4.40秒
2025-07-29 10:41:09,854 - INFO - 准备合并 2 个场景文件，总时长约 4.40秒
2025-07-29 10:41:09,854 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/349.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/358.mp4'

2025-07-29 10:41:09,855 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfyiuqxdi\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfyiuqxdi\temp_combined.mp4
2025-07-29 10:41:09,975 - INFO - 合并后的视频时长: 4.45秒，目标音频时长: 3.15秒
2025-07-29 10:41:09,975 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfyiuqxdi\temp_combined.mp4 -ss 0 -to 3.147 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-07-29 10:41:10,229 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:10,229 - INFO - 目标音频时长: 3.15秒
2025-07-29 10:41:10,229 - INFO - 实际视频时长: 3.18秒
2025-07-29 10:41:10,229 - INFO - 时长差异: 0.04秒 (1.14%)
2025-07-29 10:41:10,229 - INFO - ==========================================
2025-07-29 10:41:10,229 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:10,229 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-07-29 10:41:10,230 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfyiuqxdi
2025-07-29 10:41:10,276 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:10,276 - INFO -   - 音频时长: 3.15秒
2025-07-29 10:41:10,276 - INFO -   - 视频时长: 3.18秒
2025-07-29 10:41:10,276 - INFO -   - 时长差异: 0.04秒 (1.14%)
2025-07-29 10:41:10,276 - INFO - 
字幕 #41 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:10,276 - INFO - 生成的视频文件:
2025-07-29 10:41:10,276 - INFO -   1. F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-07-29 10:41:10,276 - INFO -   2. F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-07-29 10:41:10,276 - INFO -   3. F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-07-29 10:41:10,276 - INFO - ========== 字幕 #41 处理结束 ==========

