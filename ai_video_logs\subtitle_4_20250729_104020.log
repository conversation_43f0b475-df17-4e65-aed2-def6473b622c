2025-07-29 10:40:20,099 - INFO - ========== 字幕 #4 处理开始 ==========
2025-07-29 10:40:20,099 - INFO - 字幕内容: 哥哥轻佻承认，还与九姨娘当众调情。
2025-07-29 10:40:20,099 - INFO - 字幕序号: [12, 15]
2025-07-29 10:40:20,099 - INFO - 音频文件详情:
2025-07-29 10:40:20,099 - INFO -   - 路径: output\4.wav
2025-07-29 10:40:20,100 - INFO -   - 时长: 3.24秒
2025-07-29 10:40:20,100 - INFO -   - 验证音频时长: 3.24秒
2025-07-29 10:40:20,100 - INFO - 字幕时间戳信息:
2025-07-29 10:40:20,100 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:20,100 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:20,100 - INFO -   - 根据生成的音频时长(3.24秒)已调整字幕时间戳
2025-07-29 10:40:20,100 - INFO - ========== 新模式：为字幕 #4 生成4套场景方案 ==========
2025-07-29 10:40:20,100 - INFO - 字幕序号列表: [12, 15]
2025-07-29 10:40:20,100 - INFO - 
--- 生成方案 #1：基于字幕序号 #12 ---
2025-07-29 10:40:20,100 - INFO - 开始为单个字幕序号 #12 匹配场景，目标时长: 3.24秒
2025-07-29 10:40:20,100 - INFO - 开始查找字幕序号 [12] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:20,100 - INFO - 找到related_overlap场景: scene_id=24, 字幕#12
2025-07-29 10:40:20,102 - INFO - 字幕 #12 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:20,102 - INFO - 字幕序号 #12 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:20,102 - INFO - 选择第一个overlap场景作为起点: scene_id=24
2025-07-29 10:40:20,102 - INFO - 添加起点场景: scene_id=24, 时长=1.52秒, 累计时长=1.52秒
2025-07-29 10:40:20,102 - INFO - 起点场景时长不足，需要延伸填充 1.72秒
2025-07-29 10:40:20,102 - INFO - 起点场景在原始列表中的索引: 23
2025-07-29 10:40:20,102 - INFO - 延伸添加场景: scene_id=25 (完整时长 1.56秒)
2025-07-29 10:40:20,102 - INFO - 累计时长: 3.08秒
2025-07-29 10:40:20,102 - INFO - 延伸添加场景: scene_id=26 (裁剪至 0.16秒)
2025-07-29 10:40:20,102 - INFO - 累计时长: 3.24秒
2025-07-29 10:40:20,102 - INFO - 字幕序号 #12 场景匹配完成，共选择 3 个场景，总时长: 3.24秒
2025-07-29 10:40:20,102 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:40:20,102 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:40:20,102 - INFO - 
--- 生成方案 #2：基于字幕序号 #15 ---
2025-07-29 10:40:20,102 - INFO - 开始为单个字幕序号 #15 匹配场景，目标时长: 3.24秒
2025-07-29 10:40:20,102 - INFO - 开始查找字幕序号 [15] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:20,102 - INFO - 找到related_overlap场景: scene_id=27, 字幕#15
2025-07-29 10:40:20,102 - INFO - 找到related_overlap场景: scene_id=29, 字幕#15
2025-07-29 10:40:20,103 - INFO - 字幕 #15 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:20,103 - INFO - 字幕序号 #15 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:20,103 - INFO - 选择第一个overlap场景作为起点: scene_id=27
2025-07-29 10:40:20,103 - INFO - 添加起点场景: scene_id=27, 时长=1.04秒, 累计时长=1.04秒
2025-07-29 10:40:20,103 - INFO - 起点场景时长不足，需要延伸填充 2.20秒
2025-07-29 10:40:20,103 - INFO - 起点场景在原始列表中的索引: 26
2025-07-29 10:40:20,103 - INFO - 延伸添加场景: scene_id=28 (完整时长 1.04秒)
2025-07-29 10:40:20,103 - INFO - 累计时长: 2.08秒
2025-07-29 10:40:20,103 - INFO - 延伸添加场景: scene_id=29 (裁剪至 1.16秒)
2025-07-29 10:40:20,103 - INFO - 累计时长: 3.24秒
2025-07-29 10:40:20,103 - INFO - 字幕序号 #15 场景匹配完成，共选择 3 个场景，总时长: 3.24秒
2025-07-29 10:40:20,103 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:20,103 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:20,103 - INFO - ========== 当前模式：为字幕 #4 生成 1 套场景方案 ==========
2025-07-29 10:40:20,103 - INFO - 开始查找字幕序号 [12, 15] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:20,103 - INFO - 找到related_overlap场景: scene_id=24, 字幕#12
2025-07-29 10:40:20,103 - INFO - 找到related_overlap场景: scene_id=27, 字幕#15
2025-07-29 10:40:20,103 - INFO - 找到related_overlap场景: scene_id=29, 字幕#15
2025-07-29 10:40:20,104 - INFO - 字幕 #12 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:20,104 - INFO - 字幕 #15 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:20,104 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:40:20,104 - INFO - 开始生成方案 #1
2025-07-29 10:40:20,104 - INFO - 方案 #1: 为字幕#12选择初始化overlap场景id=24
2025-07-29 10:40:20,104 - INFO - 方案 #1: 为字幕#15选择初始化overlap场景id=29
2025-07-29 10:40:20,104 - INFO - 方案 #1: 初始选择后，当前总时长=3.64秒
2025-07-29 10:40:20,104 - INFO - 方案 #1: 额外between选择后，当前总时长=3.64秒
2025-07-29 10:40:20,104 - INFO - 方案 #1: 场景总时长(3.64秒)大于音频时长(3.24秒)，需要裁剪
2025-07-29 10:40:20,104 - INFO - 调整前总时长: 3.64秒, 目标时长: 3.24秒
2025-07-29 10:40:20,104 - INFO - 需要裁剪 0.40秒
2025-07-29 10:40:20,104 - INFO - 裁剪最长场景ID=29：从2.12秒裁剪至1.72秒
2025-07-29 10:40:20,104 - INFO - 调整后总时长: 3.24秒，与目标时长差异: 0.00秒
2025-07-29 10:40:20,104 - INFO - 方案 #1 调整/填充后最终总时长: 3.24秒
2025-07-29 10:40:20,104 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:20,104 - INFO - ========== 当前模式：字幕 #4 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:20,104 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:20,104 - INFO - ========== 新模式：字幕 #4 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:20,104 - INFO - 
----- 处理字幕 #4 的方案 #1 -----
2025-07-29 10:40:20,105 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-29 10:40:20,105 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6kqvie01
2025-07-29 10:40:20,105 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\24.mp4 (确认存在: True)
2025-07-29 10:40:20,105 - INFO - 添加场景ID=24，时长=1.52秒，累计时长=1.52秒
2025-07-29 10:40:20,106 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\25.mp4 (确认存在: True)
2025-07-29 10:40:20,106 - INFO - 添加场景ID=25，时长=1.56秒，累计时长=3.08秒
2025-07-29 10:40:20,106 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\26.mp4 (确认存在: True)
2025-07-29 10:40:20,106 - INFO - 添加场景ID=26，时长=1.92秒，累计时长=5.00秒
2025-07-29 10:40:20,106 - INFO - 场景总时长(5.00秒)已达到音频时长(3.24秒)的1.5倍，停止添加场景
2025-07-29 10:40:20,106 - INFO - 准备合并 3 个场景文件，总时长约 5.00秒
2025-07-29 10:40:20,106 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/24.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/25.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/26.mp4'

2025-07-29 10:40:20,106 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6kqvie01\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6kqvie01\temp_combined.mp4
2025-07-29 10:40:20,257 - INFO - 合并后的视频时长: 5.07秒，目标音频时长: 3.24秒
2025-07-29 10:40:20,257 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6kqvie01\temp_combined.mp4 -ss 0 -to 3.239 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-29 10:40:20,529 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:20,529 - INFO - 目标音频时长: 3.24秒
2025-07-29 10:40:20,529 - INFO - 实际视频时长: 3.26秒
2025-07-29 10:40:20,529 - INFO - 时长差异: 0.02秒 (0.74%)
2025-07-29 10:40:20,529 - INFO - ==========================================
2025-07-29 10:40:20,529 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:20,529 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-29 10:40:20,530 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6kqvie01
2025-07-29 10:40:20,574 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:20,574 - INFO -   - 音频时长: 3.24秒
2025-07-29 10:40:20,574 - INFO -   - 视频时长: 3.26秒
2025-07-29 10:40:20,574 - INFO -   - 时长差异: 0.02秒 (0.74%)
2025-07-29 10:40:20,574 - INFO - 
----- 处理字幕 #4 的方案 #2 -----
2025-07-29 10:40:20,575 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-29 10:40:20,575 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu6z4igz7
2025-07-29 10:40:20,575 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\27.mp4 (确认存在: True)
2025-07-29 10:40:20,575 - INFO - 添加场景ID=27，时长=1.04秒，累计时长=1.04秒
2025-07-29 10:40:20,575 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\28.mp4 (确认存在: True)
2025-07-29 10:40:20,575 - INFO - 添加场景ID=28，时长=1.04秒，累计时长=2.08秒
2025-07-29 10:40:20,575 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\29.mp4 (确认存在: True)
2025-07-29 10:40:20,575 - INFO - 添加场景ID=29，时长=2.12秒，累计时长=4.20秒
2025-07-29 10:40:20,575 - INFO - 准备合并 3 个场景文件，总时长约 4.20秒
2025-07-29 10:40:20,575 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/27.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/28.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/29.mp4'

2025-07-29 10:40:20,575 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpu6z4igz7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpu6z4igz7\temp_combined.mp4
2025-07-29 10:40:20,726 - INFO - 合并后的视频时长: 4.27秒，目标音频时长: 3.24秒
2025-07-29 10:40:20,726 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpu6z4igz7\temp_combined.mp4 -ss 0 -to 3.239 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-29 10:40:20,973 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:20,973 - INFO - 目标音频时长: 3.24秒
2025-07-29 10:40:20,973 - INFO - 实际视频时长: 3.26秒
2025-07-29 10:40:20,973 - INFO - 时长差异: 0.02秒 (0.74%)
2025-07-29 10:40:20,973 - INFO - ==========================================
2025-07-29 10:40:20,973 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:20,973 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-29 10:40:20,974 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu6z4igz7
2025-07-29 10:40:21,015 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:21,015 - INFO -   - 音频时长: 3.24秒
2025-07-29 10:40:21,015 - INFO -   - 视频时长: 3.26秒
2025-07-29 10:40:21,015 - INFO -   - 时长差异: 0.02秒 (0.74%)
2025-07-29 10:40:21,015 - INFO - 
----- 处理字幕 #4 的方案 #3 -----
2025-07-29 10:40:21,015 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-29 10:40:21,016 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7v9vm1nf
2025-07-29 10:40:21,016 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\24.mp4 (确认存在: True)
2025-07-29 10:40:21,016 - INFO - 添加场景ID=24，时长=1.52秒，累计时长=1.52秒
2025-07-29 10:40:21,016 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\29.mp4 (确认存在: True)
2025-07-29 10:40:21,016 - INFO - 添加场景ID=29，时长=2.12秒，累计时长=3.64秒
2025-07-29 10:40:21,016 - INFO - 准备合并 2 个场景文件，总时长约 3.64秒
2025-07-29 10:40:21,017 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/24.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/29.mp4'

2025-07-29 10:40:21,017 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7v9vm1nf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7v9vm1nf\temp_combined.mp4
2025-07-29 10:40:21,137 - INFO - 合并后的视频时长: 3.69秒，目标音频时长: 3.24秒
2025-07-29 10:40:21,137 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7v9vm1nf\temp_combined.mp4 -ss 0 -to 3.239 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-29 10:40:21,390 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:21,390 - INFO - 目标音频时长: 3.24秒
2025-07-29 10:40:21,390 - INFO - 实际视频时长: 3.26秒
2025-07-29 10:40:21,390 - INFO - 时长差异: 0.02秒 (0.74%)
2025-07-29 10:40:21,390 - INFO - ==========================================
2025-07-29 10:40:21,390 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:21,390 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-29 10:40:21,391 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7v9vm1nf
2025-07-29 10:40:21,433 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:21,433 - INFO -   - 音频时长: 3.24秒
2025-07-29 10:40:21,433 - INFO -   - 视频时长: 3.26秒
2025-07-29 10:40:21,433 - INFO -   - 时长差异: 0.02秒 (0.74%)
2025-07-29 10:40:21,433 - INFO - 
字幕 #4 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:21,433 - INFO - 生成的视频文件:
2025-07-29 10:40:21,433 - INFO -   1. F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-29 10:40:21,433 - INFO -   2. F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-29 10:40:21,433 - INFO -   3. F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-29 10:40:21,433 - INFO - ========== 字幕 #4 处理结束 ==========

