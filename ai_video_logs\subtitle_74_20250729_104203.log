2025-07-29 10:42:03,704 - INFO - ========== 字幕 #74 处理开始 ==========
2025-07-29 10:42:03,704 - INFO - 字幕内容: 历经波折，她终于看清真心，选择与摄政王相守，并怀上了他们的孩子，开启了真正属于自己的幸福人生。
2025-07-29 10:42:03,704 - INFO - 字幕序号: [2745, 2755]
2025-07-29 10:42:03,704 - INFO - 音频文件详情:
2025-07-29 10:42:03,704 - INFO -   - 路径: output\74.wav
2025-07-29 10:42:03,704 - INFO -   - 时长: 5.65秒
2025-07-29 10:42:03,704 - INFO -   - 验证音频时长: 5.65秒
2025-07-29 10:42:03,705 - INFO - 字幕时间戳信息:
2025-07-29 10:42:03,705 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:42:03,705 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:42:03,705 - INFO -   - 根据生成的音频时长(5.65秒)已调整字幕时间戳
2025-07-29 10:42:03,705 - INFO - ========== 新模式：为字幕 #74 生成4套场景方案 ==========
2025-07-29 10:42:03,705 - INFO - 字幕序号列表: [2745, 2755]
2025-07-29 10:42:03,705 - INFO - 
--- 生成方案 #1：基于字幕序号 #2745 ---
2025-07-29 10:42:03,705 - INFO - 开始为单个字幕序号 #2745 匹配场景，目标时长: 5.65秒
2025-07-29 10:42:03,705 - INFO - 开始查找字幕序号 [2745] 对应的场景，共有 3443 个场景可选
2025-07-29 10:42:03,706 - INFO - 找到related_overlap场景: scene_id=3424, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 找到related_between场景: scene_id=3413, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 找到related_between场景: scene_id=3414, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 找到related_between场景: scene_id=3415, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 找到related_between场景: scene_id=3416, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 找到related_between场景: scene_id=3417, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 找到related_between场景: scene_id=3418, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 找到related_between场景: scene_id=3419, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 找到related_between场景: scene_id=3420, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 找到related_between场景: scene_id=3421, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 找到related_between场景: scene_id=3422, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 找到related_between场景: scene_id=3423, 字幕#2745
2025-07-29 10:42:03,706 - INFO - 字幕 #2745 找到 1 个overlap场景, 11 个between场景
2025-07-29 10:42:03,706 - INFO - 字幕序号 #2745 找到 1 个可用overlap场景, 11 个可用between场景
2025-07-29 10:42:03,706 - INFO - 选择第一个overlap场景作为起点: scene_id=3424
2025-07-29 10:42:03,706 - INFO - 添加起点场景: scene_id=3424, 时长=4.08秒, 累计时长=4.08秒
2025-07-29 10:42:03,706 - INFO - 起点场景时长不足，需要延伸填充 1.57秒
2025-07-29 10:42:03,707 - INFO - 起点场景在原始列表中的索引: 3423
2025-07-29 10:42:03,707 - INFO - 延伸添加场景: scene_id=3425 (裁剪至 1.57秒)
2025-07-29 10:42:03,707 - INFO - 累计时长: 5.65秒
2025-07-29 10:42:03,707 - INFO - 字幕序号 #2745 场景匹配完成，共选择 2 个场景，总时长: 5.65秒
2025-07-29 10:42:03,707 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:42:03,707 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:42:03,707 - INFO - 
--- 生成方案 #2：基于字幕序号 #2755 ---
2025-07-29 10:42:03,707 - INFO - 开始为单个字幕序号 #2755 匹配场景，目标时长: 5.65秒
2025-07-29 10:42:03,707 - INFO - 开始查找字幕序号 [2755] 对应的场景，共有 3443 个场景可选
2025-07-29 10:42:03,707 - INFO - 找到related_overlap场景: scene_id=3437, 字幕#2755
2025-07-29 10:42:03,707 - INFO - 字幕 #2755 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:42:03,707 - INFO - 字幕序号 #2755 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:42:03,707 - INFO - 选择第一个overlap场景作为起点: scene_id=3437
2025-07-29 10:42:03,707 - INFO - 添加起点场景: scene_id=3437, 时长=9.80秒, 累计时长=9.80秒
2025-07-29 10:42:03,707 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:42:03,707 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:42:03,707 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:42:03,707 - INFO - ========== 当前模式：为字幕 #74 生成 1 套场景方案 ==========
2025-07-29 10:42:03,707 - INFO - 开始查找字幕序号 [2745, 2755] 对应的场景，共有 3443 个场景可选
2025-07-29 10:42:03,708 - INFO - 找到related_overlap场景: scene_id=3424, 字幕#2745
2025-07-29 10:42:03,708 - INFO - 找到related_overlap场景: scene_id=3437, 字幕#2755
2025-07-29 10:42:03,708 - INFO - 找到related_between场景: scene_id=3413, 字幕#2745
2025-07-29 10:42:03,708 - INFO - 找到related_between场景: scene_id=3414, 字幕#2745
2025-07-29 10:42:03,708 - INFO - 找到related_between场景: scene_id=3415, 字幕#2745
2025-07-29 10:42:03,708 - INFO - 找到related_between场景: scene_id=3416, 字幕#2745
2025-07-29 10:42:03,709 - INFO - 找到related_between场景: scene_id=3417, 字幕#2745
2025-07-29 10:42:03,709 - INFO - 找到related_between场景: scene_id=3418, 字幕#2745
2025-07-29 10:42:03,709 - INFO - 找到related_between场景: scene_id=3419, 字幕#2745
2025-07-29 10:42:03,709 - INFO - 找到related_between场景: scene_id=3420, 字幕#2745
2025-07-29 10:42:03,709 - INFO - 找到related_between场景: scene_id=3421, 字幕#2745
2025-07-29 10:42:03,709 - INFO - 找到related_between场景: scene_id=3422, 字幕#2745
2025-07-29 10:42:03,709 - INFO - 找到related_between场景: scene_id=3423, 字幕#2745
2025-07-29 10:42:03,709 - INFO - 字幕 #2745 找到 1 个overlap场景, 11 个between场景
2025-07-29 10:42:03,709 - INFO - 字幕 #2755 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:42:03,709 - INFO - 共收集 2 个未使用的overlap场景和 11 个未使用的between场景
2025-07-29 10:42:03,709 - INFO - 开始生成方案 #1
2025-07-29 10:42:03,709 - INFO - 方案 #1: 为字幕#2745选择初始化overlap场景id=3424
2025-07-29 10:42:03,709 - INFO - 方案 #1: 为字幕#2755选择初始化overlap场景id=3437
2025-07-29 10:42:03,709 - INFO - 方案 #1: 初始选择后，当前总时长=13.88秒
2025-07-29 10:42:03,709 - INFO - 方案 #1: 额外between选择后，当前总时长=13.88秒
2025-07-29 10:42:03,709 - INFO - 方案 #1: 场景总时长(13.88秒)大于音频时长(5.65秒)，需要裁剪
2025-07-29 10:42:03,709 - INFO - 调整前总时长: 13.88秒, 目标时长: 5.65秒
2025-07-29 10:42:03,709 - INFO - 需要裁剪 8.23秒
2025-07-29 10:42:03,709 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:42:03,709 - INFO - 裁剪场景ID=3437：从9.80秒裁剪至2.94秒
2025-07-29 10:42:03,709 - INFO - 裁剪场景ID=3424：从4.08秒裁剪至2.94秒
2025-07-29 10:42:03,709 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.23秒
2025-07-29 10:42:03,709 - INFO - 调整后总时长: 5.88秒，与目标时长差异: 0.23秒
2025-07-29 10:42:03,709 - INFO - 方案 #1 调整/填充后最终总时长: 5.88秒
2025-07-29 10:42:03,709 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:42:03,709 - INFO - ========== 当前模式：字幕 #74 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:42:03,709 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:42:03,709 - INFO - ========== 新模式：字幕 #74 共生成 3 套有效场景方案 ==========
2025-07-29 10:42:03,709 - INFO - 
----- 处理字幕 #74 的方案 #1 -----
2025-07-29 10:42:03,709 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\74_1.mp4
2025-07-29 10:42:03,709 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0se106wx
2025-07-29 10:42:03,710 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3424.mp4 (确认存在: True)
2025-07-29 10:42:03,710 - INFO - 添加场景ID=3424，时长=4.08秒，累计时长=4.08秒
2025-07-29 10:42:03,710 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3425.mp4 (确认存在: True)
2025-07-29 10:42:03,710 - INFO - 添加场景ID=3425，时长=2.76秒，累计时长=6.84秒
2025-07-29 10:42:03,710 - INFO - 准备合并 2 个场景文件，总时长约 6.84秒
2025-07-29 10:42:03,710 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3424.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3425.mp4'

2025-07-29 10:42:03,710 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0se106wx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0se106wx\temp_combined.mp4
2025-07-29 10:42:03,869 - INFO - 合并后的视频时长: 6.89秒，目标音频时长: 5.65秒
2025-07-29 10:42:03,869 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0se106wx\temp_combined.mp4 -ss 0 -to 5.646 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\74_1.mp4
2025-07-29 10:42:04,237 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:42:04,237 - INFO - 目标音频时长: 5.65秒
2025-07-29 10:42:04,237 - INFO - 实际视频时长: 5.70秒
2025-07-29 10:42:04,237 - INFO - 时长差异: 0.06秒 (1.01%)
2025-07-29 10:42:04,237 - INFO - ==========================================
2025-07-29 10:42:04,237 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:42:04,237 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\74_1.mp4
2025-07-29 10:42:04,238 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0se106wx
2025-07-29 10:42:04,296 - INFO - 方案 #1 处理完成:
2025-07-29 10:42:04,296 - INFO -   - 音频时长: 5.65秒
2025-07-29 10:42:04,296 - INFO -   - 视频时长: 5.70秒
2025-07-29 10:42:04,296 - INFO -   - 时长差异: 0.06秒 (1.01%)
2025-07-29 10:42:04,296 - INFO - 
----- 处理字幕 #74 的方案 #2 -----
2025-07-29 10:42:04,296 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\74_2.mp4
2025-07-29 10:42:04,297 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbqrnzx1w
2025-07-29 10:42:04,298 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3437.mp4 (确认存在: True)
2025-07-29 10:42:04,298 - INFO - 添加场景ID=3437，时长=9.80秒，累计时长=9.80秒
2025-07-29 10:42:04,298 - INFO - 场景总时长(9.80秒)已达到音频时长(5.65秒)的1.5倍，停止添加场景
2025-07-29 10:42:04,298 - INFO - 准备合并 1 个场景文件，总时长约 9.80秒
2025-07-29 10:42:04,298 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3437.mp4'

2025-07-29 10:42:04,298 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbqrnzx1w\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbqrnzx1w\temp_combined.mp4
2025-07-29 10:42:04,458 - INFO - 合并后的视频时长: 9.82秒，目标音频时长: 5.65秒
2025-07-29 10:42:04,458 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbqrnzx1w\temp_combined.mp4 -ss 0 -to 5.646 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\74_2.mp4
2025-07-29 10:42:04,804 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:42:04,804 - INFO - 目标音频时长: 5.65秒
2025-07-29 10:42:04,804 - INFO - 实际视频时长: 5.70秒
2025-07-29 10:42:04,804 - INFO - 时长差异: 0.06秒 (1.01%)
2025-07-29 10:42:04,804 - INFO - ==========================================
2025-07-29 10:42:04,804 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:42:04,805 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\74_2.mp4
2025-07-29 10:42:04,807 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbqrnzx1w
2025-07-29 10:42:04,874 - INFO - 方案 #2 处理完成:
2025-07-29 10:42:04,874 - INFO -   - 音频时长: 5.65秒
2025-07-29 10:42:04,874 - INFO -   - 视频时长: 5.70秒
2025-07-29 10:42:04,874 - INFO -   - 时长差异: 0.06秒 (1.01%)
2025-07-29 10:42:04,874 - INFO - 
----- 处理字幕 #74 的方案 #3 -----
2025-07-29 10:42:04,874 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\74_3.mp4
2025-07-29 10:42:04,875 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjlndkxt9
2025-07-29 10:42:04,875 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3424.mp4 (确认存在: True)
2025-07-29 10:42:04,875 - INFO - 添加场景ID=3424，时长=4.08秒，累计时长=4.08秒
2025-07-29 10:42:04,875 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3437.mp4 (确认存在: True)
2025-07-29 10:42:04,875 - INFO - 添加场景ID=3437，时长=9.80秒，累计时长=13.88秒
2025-07-29 10:42:04,876 - INFO - 场景总时长(13.88秒)已达到音频时长(5.65秒)的1.5倍，停止添加场景
2025-07-29 10:42:04,876 - INFO - 准备合并 2 个场景文件，总时长约 13.88秒
2025-07-29 10:42:04,876 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3424.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3437.mp4'

2025-07-29 10:42:04,876 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjlndkxt9\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjlndkxt9\temp_combined.mp4
2025-07-29 10:42:05,029 - INFO - 合并后的视频时长: 13.93秒，目标音频时长: 5.65秒
2025-07-29 10:42:05,030 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjlndkxt9\temp_combined.mp4 -ss 0 -to 5.646 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\74_3.mp4
2025-07-29 10:42:05,369 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:42:05,369 - INFO - 目标音频时长: 5.65秒
2025-07-29 10:42:05,369 - INFO - 实际视频时长: 5.70秒
2025-07-29 10:42:05,369 - INFO - 时长差异: 0.06秒 (1.01%)
2025-07-29 10:42:05,369 - INFO - ==========================================
2025-07-29 10:42:05,369 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:42:05,369 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\74_3.mp4
2025-07-29 10:42:05,371 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjlndkxt9
2025-07-29 10:42:05,416 - INFO - 方案 #3 处理完成:
2025-07-29 10:42:05,416 - INFO -   - 音频时长: 5.65秒
2025-07-29 10:42:05,416 - INFO -   - 视频时长: 5.70秒
2025-07-29 10:42:05,416 - INFO -   - 时长差异: 0.06秒 (1.01%)
2025-07-29 10:42:05,416 - INFO - 
字幕 #74 处理完成，成功生成 3/3 套方案
2025-07-29 10:42:05,416 - INFO - 生成的视频文件:
2025-07-29 10:42:05,416 - INFO -   1. F:/github/aicut_auto/newcut_ai\74_1.mp4
2025-07-29 10:42:05,416 - INFO -   2. F:/github/aicut_auto/newcut_ai\74_2.mp4
2025-07-29 10:42:05,416 - INFO -   3. F:/github/aicut_auto/newcut_ai\74_3.mp4
2025-07-29 10:42:05,416 - INFO - ========== 字幕 #74 处理结束 ==========

