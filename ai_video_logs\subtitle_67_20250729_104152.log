2025-07-29 10:41:52,316 - INFO - ========== 字幕 #67 处理开始 ==========
2025-07-29 10:41:52,316 - INFO - 字幕内容: 他立誓婚后不动她，并随时同意和离，君子一言。
2025-07-29 10:41:52,316 - INFO - 字幕序号: [1272, 1279]
2025-07-29 10:41:52,317 - INFO - 音频文件详情:
2025-07-29 10:41:52,317 - INFO -   - 路径: output\67.wav
2025-07-29 10:41:52,317 - INFO -   - 时长: 3.95秒
2025-07-29 10:41:52,317 - INFO -   - 验证音频时长: 3.95秒
2025-07-29 10:41:52,317 - INFO - 字幕时间戳信息:
2025-07-29 10:41:52,317 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:52,317 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:52,317 - INFO -   - 根据生成的音频时长(3.95秒)已调整字幕时间戳
2025-07-29 10:41:52,317 - INFO - ========== 新模式：为字幕 #67 生成4套场景方案 ==========
2025-07-29 10:41:52,317 - INFO - 字幕序号列表: [1272, 1279]
2025-07-29 10:41:52,317 - INFO - 
--- 生成方案 #1：基于字幕序号 #1272 ---
2025-07-29 10:41:52,317 - INFO - 开始为单个字幕序号 #1272 匹配场景，目标时长: 3.95秒
2025-07-29 10:41:52,318 - INFO - 开始查找字幕序号 [1272] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:52,318 - INFO - 找到related_overlap场景: scene_id=1623, 字幕#1272
2025-07-29 10:41:52,321 - INFO - 字幕 #1272 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:52,321 - INFO - 字幕序号 #1272 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:52,321 - INFO - 选择第一个overlap场景作为起点: scene_id=1623
2025-07-29 10:41:52,321 - INFO - 添加起点场景: scene_id=1623, 时长=9.68秒, 累计时长=9.68秒
2025-07-29 10:41:52,321 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:52,321 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:41:52,322 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:41:52,322 - INFO - 
--- 生成方案 #2：基于字幕序号 #1279 ---
2025-07-29 10:41:52,322 - INFO - 开始为单个字幕序号 #1279 匹配场景，目标时长: 3.95秒
2025-07-29 10:41:52,322 - INFO - 开始查找字幕序号 [1279] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:52,322 - INFO - 找到related_overlap场景: scene_id=1624, 字幕#1279
2025-07-29 10:41:52,323 - INFO - 找到related_between场景: scene_id=1625, 字幕#1279
2025-07-29 10:41:52,323 - INFO - 找到related_between场景: scene_id=1626, 字幕#1279
2025-07-29 10:41:52,324 - INFO - 字幕 #1279 找到 1 个overlap场景, 2 个between场景
2025-07-29 10:41:52,324 - INFO - 字幕序号 #1279 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 10:41:52,324 - INFO - 选择第一个overlap场景作为起点: scene_id=1624
2025-07-29 10:41:52,324 - INFO - 添加起点场景: scene_id=1624, 时长=4.08秒, 累计时长=4.08秒
2025-07-29 10:41:52,324 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:52,324 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:41:52,324 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:52,324 - INFO - ========== 当前模式：为字幕 #67 生成 1 套场景方案 ==========
2025-07-29 10:41:52,324 - INFO - 开始查找字幕序号 [1272, 1279] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:52,325 - INFO - 找到related_overlap场景: scene_id=1623, 字幕#1272
2025-07-29 10:41:52,325 - INFO - 找到related_overlap场景: scene_id=1624, 字幕#1279
2025-07-29 10:41:52,327 - INFO - 找到related_between场景: scene_id=1625, 字幕#1279
2025-07-29 10:41:52,327 - INFO - 找到related_between场景: scene_id=1626, 字幕#1279
2025-07-29 10:41:52,327 - INFO - 字幕 #1272 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:52,327 - INFO - 字幕 #1279 找到 1 个overlap场景, 2 个between场景
2025-07-29 10:41:52,327 - INFO - 共收集 2 个未使用的overlap场景和 2 个未使用的between场景
2025-07-29 10:41:52,327 - INFO - 开始生成方案 #1
2025-07-29 10:41:52,327 - INFO - 方案 #1: 为字幕#1272选择初始化overlap场景id=1623
2025-07-29 10:41:52,327 - INFO - 方案 #1: 为字幕#1279选择初始化overlap场景id=1624
2025-07-29 10:41:52,327 - INFO - 方案 #1: 初始选择后，当前总时长=13.76秒
2025-07-29 10:41:52,328 - INFO - 方案 #1: 额外between选择后，当前总时长=13.76秒
2025-07-29 10:41:52,328 - INFO - 方案 #1: 场景总时长(13.76秒)大于音频时长(3.95秒)，需要裁剪
2025-07-29 10:41:52,328 - INFO - 调整前总时长: 13.76秒, 目标时长: 3.95秒
2025-07-29 10:41:52,328 - INFO - 需要裁剪 9.81秒
2025-07-29 10:41:52,328 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:52,328 - INFO - 裁剪场景ID=1623：从9.68秒裁剪至2.90秒
2025-07-29 10:41:52,328 - INFO - 裁剪场景ID=1624：从4.08秒裁剪至2.90秒
2025-07-29 10:41:52,328 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 1.86秒
2025-07-29 10:41:52,328 - INFO - 调整后总时长: 5.81秒，与目标时长差异: 1.86秒
2025-07-29 10:41:52,328 - INFO - 方案 #1 调整/填充后最终总时长: 5.81秒
2025-07-29 10:41:52,328 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:52,328 - INFO - ========== 当前模式：字幕 #67 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:52,328 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:52,328 - INFO - ========== 新模式：字幕 #67 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:52,328 - INFO - 
----- 处理字幕 #67 的方案 #1 -----
2025-07-29 10:41:52,328 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-07-29 10:41:52,329 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5bhr6a_m
2025-07-29 10:41:52,329 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1623.mp4 (确认存在: True)
2025-07-29 10:41:52,329 - INFO - 添加场景ID=1623，时长=9.68秒，累计时长=9.68秒
2025-07-29 10:41:52,329 - INFO - 场景总时长(9.68秒)已达到音频时长(3.95秒)的1.5倍，停止添加场景
2025-07-29 10:41:52,330 - INFO - 准备合并 1 个场景文件，总时长约 9.68秒
2025-07-29 10:41:52,330 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1623.mp4'

2025-07-29 10:41:52,330 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5bhr6a_m\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5bhr6a_m\temp_combined.mp4
2025-07-29 10:41:52,492 - INFO - 合并后的视频时长: 9.70秒，目标音频时长: 3.95秒
2025-07-29 10:41:52,492 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5bhr6a_m\temp_combined.mp4 -ss 0 -to 3.947 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-07-29 10:41:52,802 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:52,802 - INFO - 目标音频时长: 3.95秒
2025-07-29 10:41:52,802 - INFO - 实际视频时长: 3.98秒
2025-07-29 10:41:52,802 - INFO - 时长差异: 0.04秒 (0.91%)
2025-07-29 10:41:52,802 - INFO - ==========================================
2025-07-29 10:41:52,802 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:52,802 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-07-29 10:41:52,804 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5bhr6a_m
2025-07-29 10:41:52,862 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:52,862 - INFO -   - 音频时长: 3.95秒
2025-07-29 10:41:52,862 - INFO -   - 视频时长: 3.98秒
2025-07-29 10:41:52,862 - INFO -   - 时长差异: 0.04秒 (0.91%)
2025-07-29 10:41:52,862 - INFO - 
----- 处理字幕 #67 的方案 #2 -----
2025-07-29 10:41:52,862 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-07-29 10:41:52,863 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiok80axq
2025-07-29 10:41:52,863 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1624.mp4 (确认存在: True)
2025-07-29 10:41:52,863 - INFO - 添加场景ID=1624，时长=4.08秒，累计时长=4.08秒
2025-07-29 10:41:52,864 - INFO - 准备合并 1 个场景文件，总时长约 4.08秒
2025-07-29 10:41:52,864 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1624.mp4'

2025-07-29 10:41:52,864 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpiok80axq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpiok80axq\temp_combined.mp4
2025-07-29 10:41:53,014 - INFO - 合并后的视频时长: 4.10秒，目标音频时长: 3.95秒
2025-07-29 10:41:53,014 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpiok80axq\temp_combined.mp4 -ss 0 -to 3.947 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-07-29 10:41:53,333 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:53,333 - INFO - 目标音频时长: 3.95秒
2025-07-29 10:41:53,333 - INFO - 实际视频时长: 3.98秒
2025-07-29 10:41:53,333 - INFO - 时长差异: 0.04秒 (0.91%)
2025-07-29 10:41:53,333 - INFO - ==========================================
2025-07-29 10:41:53,333 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:53,333 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-07-29 10:41:53,334 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiok80axq
2025-07-29 10:41:53,387 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:53,387 - INFO -   - 音频时长: 3.95秒
2025-07-29 10:41:53,387 - INFO -   - 视频时长: 3.98秒
2025-07-29 10:41:53,387 - INFO -   - 时长差异: 0.04秒 (0.91%)
2025-07-29 10:41:53,387 - INFO - 
----- 处理字幕 #67 的方案 #3 -----
2025-07-29 10:41:53,387 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-07-29 10:41:53,388 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpk4ms3inl
2025-07-29 10:41:53,389 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1623.mp4 (确认存在: True)
2025-07-29 10:41:53,389 - INFO - 添加场景ID=1623，时长=9.68秒，累计时长=9.68秒
2025-07-29 10:41:53,389 - INFO - 场景总时长(9.68秒)已达到音频时长(3.95秒)的1.5倍，停止添加场景
2025-07-29 10:41:53,389 - INFO - 准备合并 1 个场景文件，总时长约 9.68秒
2025-07-29 10:41:53,389 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1623.mp4'

2025-07-29 10:41:53,389 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpk4ms3inl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpk4ms3inl\temp_combined.mp4
2025-07-29 10:41:53,542 - INFO - 合并后的视频时长: 9.70秒，目标音频时长: 3.95秒
2025-07-29 10:41:53,542 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpk4ms3inl\temp_combined.mp4 -ss 0 -to 3.947 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-07-29 10:41:53,863 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:53,864 - INFO - 目标音频时长: 3.95秒
2025-07-29 10:41:53,864 - INFO - 实际视频时长: 3.98秒
2025-07-29 10:41:53,864 - INFO - 时长差异: 0.04秒 (0.91%)
2025-07-29 10:41:53,864 - INFO - ==========================================
2025-07-29 10:41:53,864 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:53,864 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-07-29 10:41:53,865 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpk4ms3inl
2025-07-29 10:41:53,929 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:53,929 - INFO -   - 音频时长: 3.95秒
2025-07-29 10:41:53,929 - INFO -   - 视频时长: 3.98秒
2025-07-29 10:41:53,929 - INFO -   - 时长差异: 0.04秒 (0.91%)
2025-07-29 10:41:53,929 - INFO - 
字幕 #67 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:53,929 - INFO - 生成的视频文件:
2025-07-29 10:41:53,929 - INFO -   1. F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-07-29 10:41:53,929 - INFO -   2. F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-07-29 10:41:53,929 - INFO -   3. F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-07-29 10:41:53,929 - INFO - ========== 字幕 #67 处理结束 ==========

