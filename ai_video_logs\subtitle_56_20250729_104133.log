2025-07-29 10:41:33,836 - INFO - ========== 字幕 #56 处理开始 ==========
2025-07-29 10:41:33,836 - INFO - 字幕内容: 祖母安慰她，行得正就不怕流言蜚语。
2025-07-29 10:41:33,836 - INFO - 字幕序号: [319, 322]
2025-07-29 10:41:33,836 - INFO - 音频文件详情:
2025-07-29 10:41:33,836 - INFO -   - 路径: output\56.wav
2025-07-29 10:41:33,836 - INFO -   - 时长: 3.42秒
2025-07-29 10:41:33,837 - INFO -   - 验证音频时长: 3.42秒
2025-07-29 10:41:33,837 - INFO - 字幕时间戳信息:
2025-07-29 10:41:33,837 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:33,837 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:33,837 - INFO -   - 根据生成的音频时长(3.42秒)已调整字幕时间戳
2025-07-29 10:41:33,837 - INFO - ========== 新模式：为字幕 #56 生成4套场景方案 ==========
2025-07-29 10:41:33,837 - INFO - 字幕序号列表: [319, 322]
2025-07-29 10:41:33,837 - INFO - 
--- 生成方案 #1：基于字幕序号 #319 ---
2025-07-29 10:41:33,837 - INFO - 开始为单个字幕序号 #319 匹配场景，目标时长: 3.42秒
2025-07-29 10:41:33,837 - INFO - 开始查找字幕序号 [319] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:33,838 - INFO - 找到related_overlap场景: scene_id=456, 字幕#319
2025-07-29 10:41:33,841 - INFO - 字幕 #319 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:33,841 - INFO - 字幕序号 #319 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:33,841 - INFO - 选择第一个overlap场景作为起点: scene_id=456
2025-07-29 10:41:33,841 - INFO - 添加起点场景: scene_id=456, 时长=5.76秒, 累计时长=5.76秒
2025-07-29 10:41:33,841 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:33,842 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:41:33,842 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:41:33,842 - INFO - 
--- 生成方案 #2：基于字幕序号 #322 ---
2025-07-29 10:41:33,842 - INFO - 开始为单个字幕序号 #322 匹配场景，目标时长: 3.42秒
2025-07-29 10:41:33,842 - INFO - 开始查找字幕序号 [322] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:33,842 - INFO - 找到related_overlap场景: scene_id=457, 字幕#322
2025-07-29 10:41:33,842 - INFO - 找到related_overlap场景: scene_id=458, 字幕#322
2025-07-29 10:41:33,844 - INFO - 字幕 #322 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:33,844 - INFO - 字幕序号 #322 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:33,844 - INFO - 选择第一个overlap场景作为起点: scene_id=457
2025-07-29 10:41:33,844 - INFO - 添加起点场景: scene_id=457, 时长=2.20秒, 累计时长=2.20秒
2025-07-29 10:41:33,844 - INFO - 起点场景时长不足，需要延伸填充 1.21秒
2025-07-29 10:41:33,844 - INFO - 起点场景在原始列表中的索引: 456
2025-07-29 10:41:33,844 - INFO - 延伸添加场景: scene_id=458 (裁剪至 1.21秒)
2025-07-29 10:41:33,845 - INFO - 累计时长: 3.42秒
2025-07-29 10:41:33,845 - INFO - 字幕序号 #322 场景匹配完成，共选择 2 个场景，总时长: 3.42秒
2025-07-29 10:41:33,845 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:33,845 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:33,845 - INFO - ========== 当前模式：为字幕 #56 生成 1 套场景方案 ==========
2025-07-29 10:41:33,845 - INFO - 开始查找字幕序号 [319, 322] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:33,845 - INFO - 找到related_overlap场景: scene_id=456, 字幕#319
2025-07-29 10:41:33,845 - INFO - 找到related_overlap场景: scene_id=457, 字幕#322
2025-07-29 10:41:33,845 - INFO - 找到related_overlap场景: scene_id=458, 字幕#322
2025-07-29 10:41:33,847 - INFO - 字幕 #319 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:33,847 - INFO - 字幕 #322 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:33,848 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:33,848 - INFO - 开始生成方案 #1
2025-07-29 10:41:33,848 - INFO - 方案 #1: 为字幕#319选择初始化overlap场景id=456
2025-07-29 10:41:33,848 - INFO - 方案 #1: 为字幕#322选择初始化overlap场景id=458
2025-07-29 10:41:33,848 - INFO - 方案 #1: 初始选择后，当前总时长=7.64秒
2025-07-29 10:41:33,848 - INFO - 方案 #1: 额外between选择后，当前总时长=7.64秒
2025-07-29 10:41:33,848 - INFO - 方案 #1: 场景总时长(7.64秒)大于音频时长(3.42秒)，需要裁剪
2025-07-29 10:41:33,848 - INFO - 调整前总时长: 7.64秒, 目标时长: 3.42秒
2025-07-29 10:41:33,848 - INFO - 需要裁剪 4.22秒
2025-07-29 10:41:33,848 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:33,848 - INFO - 裁剪场景ID=456：从5.76秒裁剪至1.73秒
2025-07-29 10:41:33,848 - INFO - 裁剪场景ID=458：从1.88秒裁剪至1.73秒
2025-07-29 10:41:33,848 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.04秒
2025-07-29 10:41:33,848 - INFO - 调整后总时长: 3.46秒，与目标时长差异: 0.04秒
2025-07-29 10:41:33,848 - INFO - 方案 #1 调整/填充后最终总时长: 3.46秒
2025-07-29 10:41:33,848 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:33,848 - INFO - ========== 当前模式：字幕 #56 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:33,848 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:33,848 - INFO - ========== 新模式：字幕 #56 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:33,848 - INFO - 
----- 处理字幕 #56 的方案 #1 -----
2025-07-29 10:41:33,848 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-29 10:41:33,849 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8kg1xjs7
2025-07-29 10:41:33,850 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\456.mp4 (确认存在: True)
2025-07-29 10:41:33,850 - INFO - 添加场景ID=456，时长=5.76秒，累计时长=5.76秒
2025-07-29 10:41:33,850 - INFO - 场景总时长(5.76秒)已达到音频时长(3.42秒)的1.5倍，停止添加场景
2025-07-29 10:41:33,850 - INFO - 准备合并 1 个场景文件，总时长约 5.76秒
2025-07-29 10:41:33,850 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/456.mp4'

2025-07-29 10:41:33,850 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8kg1xjs7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8kg1xjs7\temp_combined.mp4
2025-07-29 10:41:34,003 - INFO - 合并后的视频时长: 5.78秒，目标音频时长: 3.42秒
2025-07-29 10:41:34,003 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8kg1xjs7\temp_combined.mp4 -ss 0 -to 3.415 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-29 10:41:34,319 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:34,320 - INFO - 目标音频时长: 3.42秒
2025-07-29 10:41:34,320 - INFO - 实际视频时长: 3.46秒
2025-07-29 10:41:34,320 - INFO - 时长差异: 0.05秒 (1.41%)
2025-07-29 10:41:34,320 - INFO - ==========================================
2025-07-29 10:41:34,320 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:34,320 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-29 10:41:34,321 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8kg1xjs7
2025-07-29 10:41:34,376 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:34,376 - INFO -   - 音频时长: 3.42秒
2025-07-29 10:41:34,376 - INFO -   - 视频时长: 3.46秒
2025-07-29 10:41:34,376 - INFO -   - 时长差异: 0.05秒 (1.41%)
2025-07-29 10:41:34,377 - INFO - 
----- 处理字幕 #56 的方案 #2 -----
2025-07-29 10:41:34,377 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-29 10:41:34,377 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxfjx230t
2025-07-29 10:41:34,377 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\457.mp4 (确认存在: True)
2025-07-29 10:41:34,377 - INFO - 添加场景ID=457，时长=2.20秒，累计时长=2.20秒
2025-07-29 10:41:34,377 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\458.mp4 (确认存在: True)
2025-07-29 10:41:34,377 - INFO - 添加场景ID=458，时长=1.88秒，累计时长=4.08秒
2025-07-29 10:41:34,378 - INFO - 准备合并 2 个场景文件，总时长约 4.08秒
2025-07-29 10:41:34,378 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/457.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/458.mp4'

2025-07-29 10:41:34,378 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxfjx230t\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxfjx230t\temp_combined.mp4
2025-07-29 10:41:34,534 - INFO - 合并后的视频时长: 4.13秒，目标音频时长: 3.42秒
2025-07-29 10:41:34,534 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxfjx230t\temp_combined.mp4 -ss 0 -to 3.415 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-29 10:41:34,823 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:34,823 - INFO - 目标音频时长: 3.42秒
2025-07-29 10:41:34,823 - INFO - 实际视频时长: 3.46秒
2025-07-29 10:41:34,823 - INFO - 时长差异: 0.05秒 (1.41%)
2025-07-29 10:41:34,823 - INFO - ==========================================
2025-07-29 10:41:34,823 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:34,823 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-29 10:41:34,824 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxfjx230t
2025-07-29 10:41:34,884 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:34,884 - INFO -   - 音频时长: 3.42秒
2025-07-29 10:41:34,884 - INFO -   - 视频时长: 3.46秒
2025-07-29 10:41:34,884 - INFO -   - 时长差异: 0.05秒 (1.41%)
2025-07-29 10:41:34,884 - INFO - 
----- 处理字幕 #56 的方案 #3 -----
2025-07-29 10:41:34,884 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\56_3.mp4
2025-07-29 10:41:34,884 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6ey6xv1i
2025-07-29 10:41:34,885 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\456.mp4 (确认存在: True)
2025-07-29 10:41:34,885 - INFO - 添加场景ID=456，时长=5.76秒，累计时长=5.76秒
2025-07-29 10:41:34,885 - INFO - 场景总时长(5.76秒)已达到音频时长(3.42秒)的1.5倍，停止添加场景
2025-07-29 10:41:34,885 - INFO - 准备合并 1 个场景文件，总时长约 5.76秒
2025-07-29 10:41:34,885 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/456.mp4'

2025-07-29 10:41:34,886 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6ey6xv1i\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6ey6xv1i\temp_combined.mp4
2025-07-29 10:41:35,033 - INFO - 合并后的视频时长: 5.78秒，目标音频时长: 3.42秒
2025-07-29 10:41:35,033 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6ey6xv1i\temp_combined.mp4 -ss 0 -to 3.415 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\56_3.mp4
2025-07-29 10:41:35,313 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:35,313 - INFO - 目标音频时长: 3.42秒
2025-07-29 10:41:35,313 - INFO - 实际视频时长: 3.46秒
2025-07-29 10:41:35,313 - INFO - 时长差异: 0.05秒 (1.41%)
2025-07-29 10:41:35,313 - INFO - ==========================================
2025-07-29 10:41:35,313 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:35,313 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\56_3.mp4
2025-07-29 10:41:35,314 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6ey6xv1i
2025-07-29 10:41:35,378 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:35,378 - INFO -   - 音频时长: 3.42秒
2025-07-29 10:41:35,378 - INFO -   - 视频时长: 3.46秒
2025-07-29 10:41:35,378 - INFO -   - 时长差异: 0.05秒 (1.41%)
2025-07-29 10:41:35,378 - INFO - 
字幕 #56 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:35,379 - INFO - 生成的视频文件:
2025-07-29 10:41:35,379 - INFO -   1. F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-07-29 10:41:35,379 - INFO -   2. F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-07-29 10:41:35,379 - INFO -   3. F:/github/aicut_auto/newcut_ai\56_3.mp4
2025-07-29 10:41:35,379 - INFO - ========== 字幕 #56 处理结束 ==========

