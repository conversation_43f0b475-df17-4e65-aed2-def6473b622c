2025-07-29 10:41:19,672 - INFO - ========== 字幕 #48 处理开始 ==========
2025-07-29 10:41:19,672 - INFO - 字幕内容: 九姨娘矢口否认，只承认自己透露了她的行踪。
2025-07-29 10:41:19,672 - INFO - 字幕序号: [277, 281]
2025-07-29 10:41:19,673 - INFO - 音频文件详情:
2025-07-29 10:41:19,673 - INFO -   - 路径: output\48.wav
2025-07-29 10:41:19,673 - INFO -   - 时长: 3.44秒
2025-07-29 10:41:19,673 - INFO -   - 验证音频时长: 3.44秒
2025-07-29 10:41:19,674 - INFO - 字幕时间戳信息:
2025-07-29 10:41:19,674 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:19,674 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:19,674 - INFO -   - 根据生成的音频时长(3.44秒)已调整字幕时间戳
2025-07-29 10:41:19,674 - INFO - ========== 新模式：为字幕 #48 生成4套场景方案 ==========
2025-07-29 10:41:19,674 - INFO - 字幕序号列表: [277, 281]
2025-07-29 10:41:19,674 - INFO - 
--- 生成方案 #1：基于字幕序号 #277 ---
2025-07-29 10:41:19,674 - INFO - 开始为单个字幕序号 #277 匹配场景，目标时长: 3.44秒
2025-07-29 10:41:19,674 - INFO - 开始查找字幕序号 [277] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:19,675 - INFO - 找到related_overlap场景: scene_id=412, 字幕#277
2025-07-29 10:41:19,678 - INFO - 字幕 #277 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:19,678 - INFO - 字幕序号 #277 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:19,678 - INFO - 选择第一个overlap场景作为起点: scene_id=412
2025-07-29 10:41:19,678 - INFO - 添加起点场景: scene_id=412, 时长=1.60秒, 累计时长=1.60秒
2025-07-29 10:41:19,678 - INFO - 起点场景时长不足，需要延伸填充 1.84秒
2025-07-29 10:41:19,678 - INFO - 起点场景在原始列表中的索引: 411
2025-07-29 10:41:19,678 - INFO - 延伸添加场景: scene_id=413 (完整时长 1.04秒)
2025-07-29 10:41:19,678 - INFO - 累计时长: 2.64秒
2025-07-29 10:41:19,679 - INFO - 延伸添加场景: scene_id=414 (裁剪至 0.80秒)
2025-07-29 10:41:19,679 - INFO - 累计时长: 3.44秒
2025-07-29 10:41:19,679 - INFO - 字幕序号 #277 场景匹配完成，共选择 3 个场景，总时长: 3.44秒
2025-07-29 10:41:19,679 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:19,679 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:19,679 - INFO - 
--- 生成方案 #2：基于字幕序号 #281 ---
2025-07-29 10:41:19,679 - INFO - 开始为单个字幕序号 #281 匹配场景，目标时长: 3.44秒
2025-07-29 10:41:19,679 - INFO - 开始查找字幕序号 [281] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:19,679 - INFO - 找到related_overlap场景: scene_id=416, 字幕#281
2025-07-29 10:41:19,679 - INFO - 找到related_overlap场景: scene_id=417, 字幕#281
2025-07-29 10:41:19,682 - INFO - 字幕 #281 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:19,682 - INFO - 字幕序号 #281 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:19,682 - INFO - 选择第一个overlap场景作为起点: scene_id=416
2025-07-29 10:41:19,682 - INFO - 添加起点场景: scene_id=416, 时长=1.72秒, 累计时长=1.72秒
2025-07-29 10:41:19,682 - INFO - 起点场景时长不足，需要延伸填充 1.72秒
2025-07-29 10:41:19,682 - INFO - 起点场景在原始列表中的索引: 415
2025-07-29 10:41:19,682 - INFO - 延伸添加场景: scene_id=417 (裁剪至 1.72秒)
2025-07-29 10:41:19,682 - INFO - 累计时长: 3.44秒
2025-07-29 10:41:19,682 - INFO - 字幕序号 #281 场景匹配完成，共选择 2 个场景，总时长: 3.44秒
2025-07-29 10:41:19,682 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:19,682 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:19,682 - INFO - ========== 当前模式：为字幕 #48 生成 1 套场景方案 ==========
2025-07-29 10:41:19,682 - INFO - 开始查找字幕序号 [277, 281] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:19,682 - INFO - 找到related_overlap场景: scene_id=412, 字幕#277
2025-07-29 10:41:19,682 - INFO - 找到related_overlap场景: scene_id=416, 字幕#281
2025-07-29 10:41:19,682 - INFO - 找到related_overlap场景: scene_id=417, 字幕#281
2025-07-29 10:41:19,684 - INFO - 字幕 #277 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:19,684 - INFO - 字幕 #281 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:19,684 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:19,684 - INFO - 开始生成方案 #1
2025-07-29 10:41:19,684 - INFO - 方案 #1: 为字幕#277选择初始化overlap场景id=412
2025-07-29 10:41:19,684 - INFO - 方案 #1: 为字幕#281选择初始化overlap场景id=416
2025-07-29 10:41:19,684 - INFO - 方案 #1: 初始选择后，当前总时长=3.32秒
2025-07-29 10:41:19,684 - INFO - 方案 #1: 额外添加overlap场景id=417, 当前总时长=5.12秒
2025-07-29 10:41:19,684 - INFO - 方案 #1: 额外between选择后，当前总时长=5.12秒
2025-07-29 10:41:19,685 - INFO - 方案 #1: 场景总时长(5.12秒)大于音频时长(3.44秒)，需要裁剪
2025-07-29 10:41:19,685 - INFO - 调整前总时长: 5.12秒, 目标时长: 3.44秒
2025-07-29 10:41:19,685 - INFO - 需要裁剪 1.68秒
2025-07-29 10:41:19,685 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:19,685 - INFO - 裁剪场景ID=417：从1.80秒裁剪至1.00秒
2025-07-29 10:41:19,685 - INFO - 裁剪场景ID=416：从1.72秒裁剪至1.00秒
2025-07-29 10:41:19,685 - INFO - 裁剪场景ID=412：从1.60秒裁剪至1.44秒
2025-07-29 10:41:19,685 - INFO - 调整后总时长: 3.44秒，与目标时长差异: 0.00秒
2025-07-29 10:41:19,685 - INFO - 方案 #1 调整/填充后最终总时长: 3.44秒
2025-07-29 10:41:19,685 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:19,685 - INFO - ========== 当前模式：字幕 #48 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:19,685 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:19,685 - INFO - ========== 新模式：字幕 #48 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:19,685 - INFO - 
----- 处理字幕 #48 的方案 #1 -----
2025-07-29 10:41:19,685 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-29 10:41:19,686 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbu65p7_a
2025-07-29 10:41:19,686 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\412.mp4 (确认存在: True)
2025-07-29 10:41:19,686 - INFO - 添加场景ID=412，时长=1.60秒，累计时长=1.60秒
2025-07-29 10:41:19,686 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\413.mp4 (确认存在: True)
2025-07-29 10:41:19,686 - INFO - 添加场景ID=413，时长=1.04秒，累计时长=2.64秒
2025-07-29 10:41:19,686 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\414.mp4 (确认存在: True)
2025-07-29 10:41:19,686 - INFO - 添加场景ID=414，时长=1.04秒，累计时长=3.68秒
2025-07-29 10:41:19,688 - INFO - 准备合并 3 个场景文件，总时长约 3.68秒
2025-07-29 10:41:19,688 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/412.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/413.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/414.mp4'

2025-07-29 10:41:19,688 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbu65p7_a\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbu65p7_a\temp_combined.mp4
2025-07-29 10:41:19,866 - INFO - 合并后的视频时长: 3.75秒，目标音频时长: 3.44秒
2025-07-29 10:41:19,866 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbu65p7_a\temp_combined.mp4 -ss 0 -to 3.441 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-29 10:41:20,239 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:20,239 - INFO - 目标音频时长: 3.44秒
2025-07-29 10:41:20,239 - INFO - 实际视频时长: 3.50秒
2025-07-29 10:41:20,239 - INFO - 时长差异: 0.06秒 (1.80%)
2025-07-29 10:41:20,239 - INFO - ==========================================
2025-07-29 10:41:20,239 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:20,239 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-29 10:41:20,240 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbu65p7_a
2025-07-29 10:41:20,293 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:20,293 - INFO -   - 音频时长: 3.44秒
2025-07-29 10:41:20,293 - INFO -   - 视频时长: 3.50秒
2025-07-29 10:41:20,293 - INFO -   - 时长差异: 0.06秒 (1.80%)
2025-07-29 10:41:20,293 - INFO - 
----- 处理字幕 #48 的方案 #2 -----
2025-07-29 10:41:20,294 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-07-29 10:41:20,294 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzz6gtdge
2025-07-29 10:41:20,295 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\416.mp4 (确认存在: True)
2025-07-29 10:41:20,295 - INFO - 添加场景ID=416，时长=1.72秒，累计时长=1.72秒
2025-07-29 10:41:20,295 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\417.mp4 (确认存在: True)
2025-07-29 10:41:20,295 - INFO - 添加场景ID=417，时长=1.80秒，累计时长=3.52秒
2025-07-29 10:41:20,295 - INFO - 准备合并 2 个场景文件，总时长约 3.52秒
2025-07-29 10:41:20,295 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/416.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/417.mp4'

2025-07-29 10:41:20,296 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzz6gtdge\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzz6gtdge\temp_combined.mp4
2025-07-29 10:41:20,456 - INFO - 合并后的视频时长: 3.57秒，目标音频时长: 3.44秒
2025-07-29 10:41:20,456 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzz6gtdge\temp_combined.mp4 -ss 0 -to 3.441 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-07-29 10:41:20,821 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:20,821 - INFO - 目标音频时长: 3.44秒
2025-07-29 10:41:20,821 - INFO - 实际视频时长: 3.50秒
2025-07-29 10:41:20,821 - INFO - 时长差异: 0.06秒 (1.80%)
2025-07-29 10:41:20,821 - INFO - ==========================================
2025-07-29 10:41:20,821 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:20,821 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-07-29 10:41:20,822 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzz6gtdge
2025-07-29 10:41:20,880 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:20,880 - INFO -   - 音频时长: 3.44秒
2025-07-29 10:41:20,880 - INFO -   - 视频时长: 3.50秒
2025-07-29 10:41:20,880 - INFO -   - 时长差异: 0.06秒 (1.80%)
2025-07-29 10:41:20,880 - INFO - 
----- 处理字幕 #48 的方案 #3 -----
2025-07-29 10:41:20,880 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\48_3.mp4
2025-07-29 10:41:20,881 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv8ayi2db
2025-07-29 10:41:20,881 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\412.mp4 (确认存在: True)
2025-07-29 10:41:20,881 - INFO - 添加场景ID=412，时长=1.60秒，累计时长=1.60秒
2025-07-29 10:41:20,881 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\416.mp4 (确认存在: True)
2025-07-29 10:41:20,881 - INFO - 添加场景ID=416，时长=1.72秒，累计时长=3.32秒
2025-07-29 10:41:20,882 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\417.mp4 (确认存在: True)
2025-07-29 10:41:20,882 - INFO - 添加场景ID=417，时长=1.80秒，累计时长=5.12秒
2025-07-29 10:41:20,882 - INFO - 准备合并 3 个场景文件，总时长约 5.12秒
2025-07-29 10:41:20,882 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/412.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/416.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/417.mp4'

2025-07-29 10:41:20,882 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpv8ayi2db\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpv8ayi2db\temp_combined.mp4
2025-07-29 10:41:21,046 - INFO - 合并后的视频时长: 5.19秒，目标音频时长: 3.44秒
2025-07-29 10:41:21,046 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpv8ayi2db\temp_combined.mp4 -ss 0 -to 3.441 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\48_3.mp4
