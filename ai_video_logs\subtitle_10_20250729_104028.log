2025-07-29 10:40:28,201 - INFO - ========== 字幕 #10 处理开始 ==========
2025-07-29 10:40:28,201 - INFO - 字幕内容: 祖母怒斥哥哥身为长孙，却不知天高地厚。
2025-07-29 10:40:28,201 - INFO - 字幕序号: [41, 44]
2025-07-29 10:40:28,202 - INFO - 音频文件详情:
2025-07-29 10:40:28,202 - INFO -   - 路径: output\10.wav
2025-07-29 10:40:28,202 - INFO -   - 时长: 4.17秒
2025-07-29 10:40:28,202 - INFO -   - 验证音频时长: 4.17秒
2025-07-29 10:40:28,202 - INFO - 字幕时间戳信息:
2025-07-29 10:40:28,202 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:28,202 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:28,202 - INFO -   - 根据生成的音频时长(4.17秒)已调整字幕时间戳
2025-07-29 10:40:28,202 - INFO - ========== 新模式：为字幕 #10 生成4套场景方案 ==========
2025-07-29 10:40:28,202 - INFO - 字幕序号列表: [41, 44]
2025-07-29 10:40:28,202 - INFO - 
--- 生成方案 #1：基于字幕序号 #41 ---
2025-07-29 10:40:28,202 - INFO - 开始为单个字幕序号 #41 匹配场景，目标时长: 4.17秒
2025-07-29 10:40:28,202 - INFO - 开始查找字幕序号 [41] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:28,202 - INFO - 找到related_overlap场景: scene_id=57, 字幕#41
2025-07-29 10:40:28,202 - INFO - 找到related_overlap场景: scene_id=58, 字幕#41
2025-07-29 10:40:28,204 - INFO - 字幕 #41 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:28,204 - INFO - 字幕序号 #41 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:28,204 - INFO - 选择第一个overlap场景作为起点: scene_id=57
2025-07-29 10:40:28,204 - INFO - 添加起点场景: scene_id=57, 时长=1.76秒, 累计时长=1.76秒
2025-07-29 10:40:28,204 - INFO - 起点场景时长不足，需要延伸填充 2.41秒
2025-07-29 10:40:28,204 - INFO - 起点场景在原始列表中的索引: 56
2025-07-29 10:40:28,204 - INFO - 延伸添加场景: scene_id=58 (完整时长 0.84秒)
2025-07-29 10:40:28,204 - INFO - 累计时长: 2.60秒
2025-07-29 10:40:28,204 - INFO - 延伸添加场景: scene_id=59 (裁剪至 1.57秒)
2025-07-29 10:40:28,204 - INFO - 累计时长: 4.17秒
2025-07-29 10:40:28,204 - INFO - 字幕序号 #41 场景匹配完成，共选择 3 个场景，总时长: 4.17秒
2025-07-29 10:40:28,204 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:40:28,204 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:40:28,204 - INFO - 
--- 生成方案 #2：基于字幕序号 #44 ---
2025-07-29 10:40:28,204 - INFO - 开始为单个字幕序号 #44 匹配场景，目标时长: 4.17秒
2025-07-29 10:40:28,204 - INFO - 开始查找字幕序号 [44] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:28,204 - INFO - 找到related_overlap场景: scene_id=60, 字幕#44
2025-07-29 10:40:28,205 - INFO - 字幕 #44 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:28,205 - INFO - 字幕序号 #44 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:28,205 - INFO - 选择第一个overlap场景作为起点: scene_id=60
2025-07-29 10:40:28,205 - INFO - 添加起点场景: scene_id=60, 时长=2.48秒, 累计时长=2.48秒
2025-07-29 10:40:28,205 - INFO - 起点场景时长不足，需要延伸填充 1.69秒
2025-07-29 10:40:28,205 - INFO - 起点场景在原始列表中的索引: 59
2025-07-29 10:40:28,205 - INFO - 延伸添加场景: scene_id=61 (裁剪至 1.69秒)
2025-07-29 10:40:28,205 - INFO - 累计时长: 4.17秒
2025-07-29 10:40:28,205 - INFO - 字幕序号 #44 场景匹配完成，共选择 2 个场景，总时长: 4.17秒
2025-07-29 10:40:28,205 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:28,205 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:28,205 - INFO - ========== 当前模式：为字幕 #10 生成 1 套场景方案 ==========
2025-07-29 10:40:28,205 - INFO - 开始查找字幕序号 [41, 44] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:28,205 - INFO - 找到related_overlap场景: scene_id=57, 字幕#41
2025-07-29 10:40:28,205 - INFO - 找到related_overlap场景: scene_id=58, 字幕#41
2025-07-29 10:40:28,205 - INFO - 找到related_overlap场景: scene_id=60, 字幕#44
2025-07-29 10:40:28,206 - INFO - 字幕 #41 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:28,206 - INFO - 字幕 #44 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:28,206 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:40:28,206 - INFO - 开始生成方案 #1
2025-07-29 10:40:28,206 - INFO - 方案 #1: 为字幕#41选择初始化overlap场景id=57
2025-07-29 10:40:28,206 - INFO - 方案 #1: 为字幕#44选择初始化overlap场景id=60
2025-07-29 10:40:28,206 - INFO - 方案 #1: 初始选择后，当前总时长=4.24秒
2025-07-29 10:40:28,206 - INFO - 方案 #1: 额外between选择后，当前总时长=4.24秒
2025-07-29 10:40:28,206 - INFO - 方案 #1: 场景总时长(4.24秒)大于音频时长(4.17秒)，需要裁剪
2025-07-29 10:40:28,206 - INFO - 调整前总时长: 4.24秒, 目标时长: 4.17秒
2025-07-29 10:40:28,206 - INFO - 需要裁剪 0.07秒
2025-07-29 10:40:28,206 - INFO - 裁剪最长场景ID=60：从2.48秒裁剪至2.41秒
2025-07-29 10:40:28,206 - INFO - 调整后总时长: 4.17秒，与目标时长差异: 0.00秒
2025-07-29 10:40:28,207 - INFO - 方案 #1 调整/填充后最终总时长: 4.17秒
2025-07-29 10:40:28,207 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:28,207 - INFO - ========== 当前模式：字幕 #10 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:28,207 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:28,207 - INFO - ========== 新模式：字幕 #10 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:28,207 - INFO - 
----- 处理字幕 #10 的方案 #1 -----
2025-07-29 10:40:28,207 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-29 10:40:28,207 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcorjliph
2025-07-29 10:40:28,207 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\57.mp4 (确认存在: True)
2025-07-29 10:40:28,207 - INFO - 添加场景ID=57，时长=1.76秒，累计时长=1.76秒
2025-07-29 10:40:28,207 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\58.mp4 (确认存在: True)
2025-07-29 10:40:28,207 - INFO - 添加场景ID=58，时长=0.84秒，累计时长=2.60秒
2025-07-29 10:40:28,207 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\59.mp4 (确认存在: True)
2025-07-29 10:40:28,207 - INFO - 添加场景ID=59，时长=1.72秒，累计时长=4.32秒
2025-07-29 10:40:28,207 - INFO - 准备合并 3 个场景文件，总时长约 4.32秒
2025-07-29 10:40:28,207 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/57.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/58.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/59.mp4'

2025-07-29 10:40:28,209 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcorjliph\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcorjliph\temp_combined.mp4
2025-07-29 10:40:28,357 - INFO - 合并后的视频时长: 4.39秒，目标音频时长: 4.17秒
2025-07-29 10:40:28,357 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcorjliph\temp_combined.mp4 -ss 0 -to 4.167 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-29 10:40:28,647 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:28,647 - INFO - 目标音频时长: 4.17秒
2025-07-29 10:40:28,647 - INFO - 实际视频时长: 4.22秒
2025-07-29 10:40:28,647 - INFO - 时长差异: 0.06秒 (1.34%)
2025-07-29 10:40:28,647 - INFO - ==========================================
2025-07-29 10:40:28,647 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:28,647 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-29 10:40:28,649 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcorjliph
2025-07-29 10:40:28,690 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:28,690 - INFO -   - 音频时长: 4.17秒
2025-07-29 10:40:28,690 - INFO -   - 视频时长: 4.22秒
2025-07-29 10:40:28,690 - INFO -   - 时长差异: 0.06秒 (1.34%)
2025-07-29 10:40:28,690 - INFO - 
----- 处理字幕 #10 的方案 #2 -----
2025-07-29 10:40:28,690 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-29 10:40:28,692 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpurmnqb_e
2025-07-29 10:40:28,692 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\60.mp4 (确认存在: True)
2025-07-29 10:40:28,692 - INFO - 添加场景ID=60，时长=2.48秒，累计时长=2.48秒
2025-07-29 10:40:28,692 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\61.mp4 (确认存在: True)
2025-07-29 10:40:28,692 - INFO - 添加场景ID=61，时长=2.24秒，累计时长=4.72秒
2025-07-29 10:40:28,692 - INFO - 准备合并 2 个场景文件，总时长约 4.72秒
2025-07-29 10:40:28,692 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/60.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/61.mp4'

2025-07-29 10:40:28,693 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpurmnqb_e\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpurmnqb_e\temp_combined.mp4
2025-07-29 10:40:28,821 - INFO - 合并后的视频时长: 4.77秒，目标音频时长: 4.17秒
2025-07-29 10:40:28,821 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpurmnqb_e\temp_combined.mp4 -ss 0 -to 4.167 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-29 10:40:29,096 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:29,096 - INFO - 目标音频时长: 4.17秒
2025-07-29 10:40:29,096 - INFO - 实际视频时长: 4.22秒
2025-07-29 10:40:29,096 - INFO - 时长差异: 0.06秒 (1.34%)
2025-07-29 10:40:29,097 - INFO - ==========================================
2025-07-29 10:40:29,097 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:29,097 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-29 10:40:29,098 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpurmnqb_e
2025-07-29 10:40:29,156 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:29,156 - INFO -   - 音频时长: 4.17秒
2025-07-29 10:40:29,156 - INFO -   - 视频时长: 4.22秒
2025-07-29 10:40:29,156 - INFO -   - 时长差异: 0.06秒 (1.34%)
2025-07-29 10:40:29,156 - INFO - 
----- 处理字幕 #10 的方案 #3 -----
2025-07-29 10:40:29,156 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-07-29 10:40:29,156 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkim_o320
2025-07-29 10:40:29,157 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\57.mp4 (确认存在: True)
2025-07-29 10:40:29,157 - INFO - 添加场景ID=57，时长=1.76秒，累计时长=1.76秒
2025-07-29 10:40:29,157 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\60.mp4 (确认存在: True)
2025-07-29 10:40:29,157 - INFO - 添加场景ID=60，时长=2.48秒，累计时长=4.24秒
2025-07-29 10:40:29,157 - INFO - 准备合并 2 个场景文件，总时长约 4.24秒
2025-07-29 10:40:29,157 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/57.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/60.mp4'

2025-07-29 10:40:29,157 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkim_o320\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkim_o320\temp_combined.mp4
2025-07-29 10:40:29,268 - INFO - 合并后的视频时长: 4.29秒，目标音频时长: 4.17秒
2025-07-29 10:40:29,268 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkim_o320\temp_combined.mp4 -ss 0 -to 4.167 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-07-29 10:40:29,541 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:29,542 - INFO - 目标音频时长: 4.17秒
2025-07-29 10:40:29,542 - INFO - 实际视频时长: 4.22秒
2025-07-29 10:40:29,542 - INFO - 时长差异: 0.06秒 (1.34%)
2025-07-29 10:40:29,542 - INFO - ==========================================
2025-07-29 10:40:29,542 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:29,542 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-07-29 10:40:29,542 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkim_o320
2025-07-29 10:40:29,584 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:29,584 - INFO -   - 音频时长: 4.17秒
2025-07-29 10:40:29,584 - INFO -   - 视频时长: 4.22秒
2025-07-29 10:40:29,584 - INFO -   - 时长差异: 0.06秒 (1.34%)
2025-07-29 10:40:29,584 - INFO - 
字幕 #10 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:29,584 - INFO - 生成的视频文件:
2025-07-29 10:40:29,584 - INFO -   1. F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-29 10:40:29,584 - INFO -   2. F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-07-29 10:40:29,584 - INFO -   3. F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-07-29 10:40:29,584 - INFO - ========== 字幕 #10 处理结束 ==========

