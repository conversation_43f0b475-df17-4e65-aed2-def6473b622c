2025-07-29 10:40:16,305 - INFO - ========== 字幕 #1 处理开始 ==========
2025-07-29 10:40:16,305 - INFO - 字幕内容: 女人要见将军，却被告知他现在不方便。
2025-07-29 10:40:16,305 - INFO - 字幕序号: [1, 3]
2025-07-29 10:40:16,305 - INFO - 音频文件详情:
2025-07-29 10:40:16,305 - INFO -   - 路径: output\1.wav
2025-07-29 10:40:16,305 - INFO -   - 时长: 2.38秒
2025-07-29 10:40:16,305 - INFO -   - 验证音频时长: 2.38秒
2025-07-29 10:40:16,305 - INFO - 字幕时间戳信息:
2025-07-29 10:40:16,305 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:16,315 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:16,315 - INFO -   - 根据生成的音频时长(2.38秒)已调整字幕时间戳
2025-07-29 10:40:16,315 - INFO - ========== 新模式：为字幕 #1 生成4套场景方案 ==========
2025-07-29 10:40:16,315 - INFO - 字幕序号列表: [1, 3]
2025-07-29 10:40:16,315 - INFO - 
--- 生成方案 #1：基于字幕序号 #1 ---
2025-07-29 10:40:16,315 - INFO - 开始为单个字幕序号 #1 匹配场景，目标时长: 2.38秒
2025-07-29 10:40:16,315 - INFO - 开始查找字幕序号 [1] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:16,315 - INFO - 找到related_between场景: scene_id=14, 字幕#1
2025-07-29 10:40:16,316 - INFO - 字幕 #1 找到 0 个overlap场景, 1 个between场景
2025-07-29 10:40:16,316 - INFO - 字幕序号 #1 找到 0 个可用overlap场景, 1 个可用between场景
2025-07-29 10:40:16,316 - INFO - 没有overlap场景，选择第一个between场景作为起点: scene_id=14
2025-07-29 10:40:16,316 - INFO - 添加起点场景: scene_id=14, 时长=1.28秒, 累计时长=1.28秒
2025-07-29 10:40:16,316 - INFO - 起点场景时长不足，需要延伸填充 1.10秒
2025-07-29 10:40:16,316 - INFO - 起点场景在原始列表中的索引: 13
2025-07-29 10:40:16,316 - INFO - 延伸添加场景: scene_id=15 (裁剪至 1.10秒)
2025-07-29 10:40:16,316 - INFO - 累计时长: 2.38秒
2025-07-29 10:40:16,316 - INFO - 字幕序号 #1 场景匹配完成，共选择 2 个场景，总时长: 2.38秒
2025-07-29 10:40:16,316 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:16,316 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:16,316 - INFO - 
--- 生成方案 #2：基于字幕序号 #3 ---
2025-07-29 10:40:16,316 - INFO - 开始为单个字幕序号 #3 匹配场景，目标时长: 2.38秒
2025-07-29 10:40:16,316 - INFO - 开始查找字幕序号 [3] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:16,316 - INFO - 找到related_overlap场景: scene_id=17, 字幕#3
2025-07-29 10:40:16,316 - INFO - 找到related_overlap场景: scene_id=18, 字幕#3
2025-07-29 10:40:16,317 - INFO - 字幕 #3 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:16,317 - INFO - 字幕序号 #3 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:16,317 - INFO - 选择第一个overlap场景作为起点: scene_id=17
2025-07-29 10:40:16,317 - INFO - 添加起点场景: scene_id=17, 时长=1.72秒, 累计时长=1.72秒
2025-07-29 10:40:16,317 - INFO - 起点场景时长不足，需要延伸填充 0.66秒
2025-07-29 10:40:16,317 - INFO - 起点场景在原始列表中的索引: 16
2025-07-29 10:40:16,317 - INFO - 延伸添加场景: scene_id=18 (裁剪至 0.66秒)
2025-07-29 10:40:16,317 - INFO - 累计时长: 2.38秒
2025-07-29 10:40:16,317 - INFO - 字幕序号 #3 场景匹配完成，共选择 2 个场景，总时长: 2.38秒
2025-07-29 10:40:16,317 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:16,317 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:16,317 - INFO - ========== 当前模式：为字幕 #1 生成 1 套场景方案 ==========
2025-07-29 10:40:16,317 - INFO - 开始查找字幕序号 [1, 3] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:16,317 - INFO - 找到related_overlap场景: scene_id=17, 字幕#3
2025-07-29 10:40:16,318 - INFO - 找到related_overlap场景: scene_id=18, 字幕#3
2025-07-29 10:40:16,318 - INFO - 找到related_between场景: scene_id=14, 字幕#1
2025-07-29 10:40:16,319 - INFO - 字幕 #1 找到 0 个overlap场景, 1 个between场景
2025-07-29 10:40:16,319 - INFO - 字幕 #3 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:16,319 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 10:40:16,319 - INFO - 开始生成方案 #1
2025-07-29 10:40:16,319 - INFO - 方案 #1: 为字幕#3选择初始化overlap场景id=18
2025-07-29 10:40:16,319 - INFO - 方案 #1: 初始选择后，当前总时长=2.04秒
2025-07-29 10:40:16,319 - INFO - 方案 #1: 额外添加overlap场景id=17, 当前总时长=3.76秒
2025-07-29 10:40:16,319 - INFO - 方案 #1: 为字幕#1选择初始化between场景id=14
2025-07-29 10:40:16,319 - INFO - 方案 #1: 额外between选择后，当前总时长=5.04秒
2025-07-29 10:40:16,319 - INFO - 方案 #1: 场景总时长(5.04秒)大于音频时长(2.38秒)，需要裁剪
2025-07-29 10:40:16,319 - INFO - 调整前总时长: 5.04秒, 目标时长: 2.38秒
2025-07-29 10:40:16,319 - INFO - 需要裁剪 2.66秒
2025-07-29 10:40:16,319 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:40:16,319 - INFO - 裁剪场景ID=18：从2.04秒裁剪至1.00秒
2025-07-29 10:40:16,319 - INFO - 裁剪场景ID=17：从1.72秒裁剪至1.00秒
2025-07-29 10:40:16,319 - INFO - 裁剪场景ID=14：从1.28秒裁剪至1.00秒
2025-07-29 10:40:16,319 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.62秒
2025-07-29 10:40:16,319 - INFO - 调整后总时长: 3.00秒，与目标时长差异: 0.62秒
2025-07-29 10:40:16,319 - INFO - 方案 #1 调整/填充后最终总时长: 3.00秒
2025-07-29 10:40:16,319 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:16,319 - INFO - ========== 当前模式：字幕 #1 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:16,319 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:16,319 - INFO - ========== 新模式：字幕 #1 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:16,319 - INFO - 
----- 处理字幕 #1 的方案 #1 -----
2025-07-29 10:40:16,319 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-29 10:40:16,320 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp85lxux3_
2025-07-29 10:40:16,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\14.mp4 (确认存在: True)
2025-07-29 10:40:16,320 - INFO - 添加场景ID=14，时长=1.28秒，累计时长=1.28秒
2025-07-29 10:40:16,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\15.mp4 (确认存在: True)
2025-07-29 10:40:16,320 - INFO - 添加场景ID=15，时长=1.72秒，累计时长=3.00秒
2025-07-29 10:40:16,320 - INFO - 准备合并 2 个场景文件，总时长约 3.00秒
2025-07-29 10:40:16,320 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/14.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/15.mp4'

2025-07-29 10:40:16,321 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp85lxux3_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp85lxux3_\temp_combined.mp4
2025-07-29 10:40:16,426 - INFO - 合并后的视频时长: 3.05秒，目标音频时长: 2.38秒
2025-07-29 10:40:16,426 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp85lxux3_\temp_combined.mp4 -ss 0 -to 2.382 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-29 10:40:16,714 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:16,714 - INFO - 目标音频时长: 2.38秒
2025-07-29 10:40:16,714 - INFO - 实际视频时长: 2.42秒
2025-07-29 10:40:16,714 - INFO - 时长差异: 0.04秒 (1.72%)
2025-07-29 10:40:16,714 - INFO - ==========================================
2025-07-29 10:40:16,714 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:16,714 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-29 10:40:16,714 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp85lxux3_
2025-07-29 10:40:16,766 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:16,766 - INFO -   - 音频时长: 2.38秒
2025-07-29 10:40:16,766 - INFO -   - 视频时长: 2.42秒
2025-07-29 10:40:16,766 - INFO -   - 时长差异: 0.04秒 (1.72%)
2025-07-29 10:40:16,766 - INFO - 
----- 处理字幕 #1 的方案 #2 -----
2025-07-29 10:40:16,766 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-07-29 10:40:16,766 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkn7xlpxn
2025-07-29 10:40:16,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\17.mp4 (确认存在: True)
2025-07-29 10:40:16,767 - INFO - 添加场景ID=17，时长=1.72秒，累计时长=1.72秒
2025-07-29 10:40:16,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\18.mp4 (确认存在: True)
2025-07-29 10:40:16,767 - INFO - 添加场景ID=18，时长=2.04秒，累计时长=3.76秒
2025-07-29 10:40:16,767 - INFO - 场景总时长(3.76秒)已达到音频时长(2.38秒)的1.5倍，停止添加场景
2025-07-29 10:40:16,767 - INFO - 准备合并 2 个场景文件，总时长约 3.76秒
2025-07-29 10:40:16,767 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/17.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/18.mp4'

2025-07-29 10:40:16,767 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkn7xlpxn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkn7xlpxn\temp_combined.mp4
2025-07-29 10:40:16,880 - INFO - 合并后的视频时长: 3.81秒，目标音频时长: 2.38秒
2025-07-29 10:40:16,880 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkn7xlpxn\temp_combined.mp4 -ss 0 -to 2.382 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-07-29 10:40:17,118 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:17,118 - INFO - 目标音频时长: 2.38秒
2025-07-29 10:40:17,118 - INFO - 实际视频时长: 2.42秒
2025-07-29 10:40:17,118 - INFO - 时长差异: 0.04秒 (1.72%)
2025-07-29 10:40:17,118 - INFO - ==========================================
2025-07-29 10:40:17,118 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:17,118 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-07-29 10:40:17,120 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkn7xlpxn
2025-07-29 10:40:17,163 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:17,163 - INFO -   - 音频时长: 2.38秒
2025-07-29 10:40:17,163 - INFO -   - 视频时长: 2.42秒
2025-07-29 10:40:17,163 - INFO -   - 时长差异: 0.04秒 (1.72%)
2025-07-29 10:40:17,163 - INFO - 
----- 处理字幕 #1 的方案 #3 -----
2025-07-29 10:40:17,163 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-07-29 10:40:17,163 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdmn5l_fs
2025-07-29 10:40:17,164 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\18.mp4 (确认存在: True)
2025-07-29 10:40:17,164 - INFO - 添加场景ID=18，时长=2.04秒，累计时长=2.04秒
2025-07-29 10:40:17,164 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\17.mp4 (确认存在: True)
2025-07-29 10:40:17,164 - INFO - 添加场景ID=17，时长=1.72秒，累计时长=3.76秒
2025-07-29 10:40:17,164 - INFO - 场景总时长(3.76秒)已达到音频时长(2.38秒)的1.5倍，停止添加场景
2025-07-29 10:40:17,164 - INFO - 准备合并 2 个场景文件，总时长约 3.76秒
2025-07-29 10:40:17,164 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/18.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/17.mp4'

2025-07-29 10:40:17,164 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpdmn5l_fs\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpdmn5l_fs\temp_combined.mp4
2025-07-29 10:40:17,284 - INFO - 合并后的视频时长: 3.81秒，目标音频时长: 2.38秒
2025-07-29 10:40:17,284 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpdmn5l_fs\temp_combined.mp4 -ss 0 -to 2.382 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-07-29 10:40:17,513 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:17,513 - INFO - 目标音频时长: 2.38秒
2025-07-29 10:40:17,513 - INFO - 实际视频时长: 2.42秒
2025-07-29 10:40:17,513 - INFO - 时长差异: 0.04秒 (1.72%)
2025-07-29 10:40:17,514 - INFO - ==========================================
2025-07-29 10:40:17,514 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:17,514 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-07-29 10:40:17,514 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdmn5l_fs
2025-07-29 10:40:17,564 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:17,564 - INFO -   - 音频时长: 2.38秒
2025-07-29 10:40:17,564 - INFO -   - 视频时长: 2.42秒
2025-07-29 10:40:17,564 - INFO -   - 时长差异: 0.04秒 (1.72%)
2025-07-29 10:40:17,564 - INFO - 
字幕 #1 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:17,564 - INFO - 生成的视频文件:
2025-07-29 10:40:17,564 - INFO -   1. F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-29 10:40:17,564 - INFO -   2. F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-07-29 10:40:17,564 - INFO -   3. F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-07-29 10:40:17,564 - INFO - ========== 字幕 #1 处理结束 ==========

