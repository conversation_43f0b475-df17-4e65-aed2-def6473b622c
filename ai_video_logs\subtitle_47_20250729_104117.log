2025-07-29 10:41:17,843 - INFO - ========== 字幕 #47 处理开始 ==========
2025-07-29 10:41:17,843 - INFO - 字幕内容: 女人当面对质，称刘青书已招供是姨娘主谋。
2025-07-29 10:41:17,843 - INFO - 字幕序号: [271, 276]
2025-07-29 10:41:17,844 - INFO - 音频文件详情:
2025-07-29 10:41:17,844 - INFO -   - 路径: output\47.wav
2025-07-29 10:41:17,844 - INFO -   - 时长: 4.08秒
2025-07-29 10:41:17,844 - INFO -   - 验证音频时长: 4.08秒
2025-07-29 10:41:17,844 - INFO - 字幕时间戳信息:
2025-07-29 10:41:17,844 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:17,845 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:17,845 - INFO -   - 根据生成的音频时长(4.08秒)已调整字幕时间戳
2025-07-29 10:41:17,845 - INFO - ========== 新模式：为字幕 #47 生成4套场景方案 ==========
2025-07-29 10:41:17,845 - INFO - 字幕序号列表: [271, 276]
2025-07-29 10:41:17,845 - INFO - 
--- 生成方案 #1：基于字幕序号 #271 ---
2025-07-29 10:41:17,845 - INFO - 开始为单个字幕序号 #271 匹配场景，目标时长: 4.08秒
2025-07-29 10:41:17,845 - INFO - 开始查找字幕序号 [271] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:17,845 - INFO - 找到related_overlap场景: scene_id=405, 字幕#271
2025-07-29 10:41:17,851 - INFO - 字幕 #271 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:17,851 - INFO - 字幕序号 #271 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:17,851 - INFO - 选择第一个overlap场景作为起点: scene_id=405
2025-07-29 10:41:17,851 - INFO - 添加起点场景: scene_id=405, 时长=1.36秒, 累计时长=1.36秒
2025-07-29 10:41:17,851 - INFO - 起点场景时长不足，需要延伸填充 2.72秒
2025-07-29 10:41:17,851 - INFO - 起点场景在原始列表中的索引: 404
2025-07-29 10:41:17,851 - INFO - 延伸添加场景: scene_id=406 (完整时长 1.44秒)
2025-07-29 10:41:17,851 - INFO - 累计时长: 2.80秒
2025-07-29 10:41:17,851 - INFO - 延伸添加场景: scene_id=407 (裁剪至 1.28秒)
2025-07-29 10:41:17,851 - INFO - 累计时长: 4.08秒
2025-07-29 10:41:17,851 - INFO - 字幕序号 #271 场景匹配完成，共选择 3 个场景，总时长: 4.08秒
2025-07-29 10:41:17,851 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:17,851 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:17,851 - INFO - 
--- 生成方案 #2：基于字幕序号 #276 ---
2025-07-29 10:41:17,851 - INFO - 开始为单个字幕序号 #276 匹配场景，目标时长: 4.08秒
2025-07-29 10:41:17,851 - INFO - 开始查找字幕序号 [276] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:17,852 - INFO - 找到related_overlap场景: scene_id=411, 字幕#276
2025-07-29 10:41:17,852 - INFO - 找到related_overlap场景: scene_id=412, 字幕#276
2025-07-29 10:41:17,854 - INFO - 字幕 #276 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:17,854 - INFO - 字幕序号 #276 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:17,854 - INFO - 选择第一个overlap场景作为起点: scene_id=411
2025-07-29 10:41:17,854 - INFO - 添加起点场景: scene_id=411, 时长=0.76秒, 累计时长=0.76秒
2025-07-29 10:41:17,854 - INFO - 起点场景时长不足，需要延伸填充 3.32秒
2025-07-29 10:41:17,854 - INFO - 起点场景在原始列表中的索引: 410
2025-07-29 10:41:17,854 - INFO - 延伸添加场景: scene_id=412 (完整时长 1.60秒)
2025-07-29 10:41:17,854 - INFO - 累计时长: 2.36秒
2025-07-29 10:41:17,854 - INFO - 延伸添加场景: scene_id=413 (完整时长 1.04秒)
2025-07-29 10:41:17,854 - INFO - 累计时长: 3.40秒
2025-07-29 10:41:17,854 - INFO - 延伸添加场景: scene_id=414 (裁剪至 0.68秒)
2025-07-29 10:41:17,854 - INFO - 累计时长: 4.08秒
2025-07-29 10:41:17,854 - INFO - 字幕序号 #276 场景匹配完成，共选择 4 个场景，总时长: 4.08秒
2025-07-29 10:41:17,854 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 10:41:17,854 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:17,854 - INFO - ========== 当前模式：为字幕 #47 生成 1 套场景方案 ==========
2025-07-29 10:41:17,854 - INFO - 开始查找字幕序号 [271, 276] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:17,854 - INFO - 找到related_overlap场景: scene_id=405, 字幕#271
2025-07-29 10:41:17,854 - INFO - 找到related_overlap场景: scene_id=411, 字幕#276
2025-07-29 10:41:17,854 - INFO - 找到related_overlap场景: scene_id=412, 字幕#276
2025-07-29 10:41:17,856 - INFO - 字幕 #271 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:17,856 - INFO - 字幕 #276 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:17,856 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:17,856 - INFO - 开始生成方案 #1
2025-07-29 10:41:17,856 - INFO - 方案 #1: 为字幕#271选择初始化overlap场景id=405
2025-07-29 10:41:17,856 - INFO - 方案 #1: 为字幕#276选择初始化overlap场景id=411
2025-07-29 10:41:17,856 - INFO - 方案 #1: 初始选择后，当前总时长=2.12秒
2025-07-29 10:41:17,856 - INFO - 方案 #1: 额外添加overlap场景id=412, 当前总时长=3.72秒
2025-07-29 10:41:17,856 - INFO - 方案 #1: 额外between选择后，当前总时长=3.72秒
2025-07-29 10:41:17,856 - INFO - 方案 #1: 场景总时长(3.72秒)小于音频时长(4.08秒)，需要延伸填充
2025-07-29 10:41:17,856 - INFO - 方案 #1: 最后一个场景ID: 412
2025-07-29 10:41:17,856 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 411
2025-07-29 10:41:17,856 - INFO - 方案 #1: 需要填充时长: 0.37秒
2025-07-29 10:41:17,856 - INFO - 方案 #1: 追加场景 scene_id=413 (裁剪至 0.37秒)
2025-07-29 10:41:17,856 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 10:41:17,856 - INFO - 方案 #1 调整/填充后最终总时长: 4.08秒
2025-07-29 10:41:17,856 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:17,856 - INFO - ========== 当前模式：字幕 #47 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:17,856 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:17,857 - INFO - ========== 新模式：字幕 #47 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:17,857 - INFO - 
----- 处理字幕 #47 的方案 #1 -----
2025-07-29 10:41:17,857 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-29 10:41:17,857 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpasrcz41v
2025-07-29 10:41:17,858 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\405.mp4 (确认存在: True)
2025-07-29 10:41:17,858 - INFO - 添加场景ID=405，时长=1.36秒，累计时长=1.36秒
2025-07-29 10:41:17,858 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\406.mp4 (确认存在: True)
2025-07-29 10:41:17,858 - INFO - 添加场景ID=406，时长=1.44秒，累计时长=2.80秒
2025-07-29 10:41:17,858 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\407.mp4 (确认存在: True)
2025-07-29 10:41:17,858 - INFO - 添加场景ID=407，时长=1.48秒，累计时长=4.28秒
2025-07-29 10:41:17,859 - INFO - 准备合并 3 个场景文件，总时长约 4.28秒
2025-07-29 10:41:17,859 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/405.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/406.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/407.mp4'

2025-07-29 10:41:17,859 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpasrcz41v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpasrcz41v\temp_combined.mp4
2025-07-29 10:41:18,016 - INFO - 合并后的视频时长: 4.35秒，目标音频时长: 4.08秒
2025-07-29 10:41:18,016 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpasrcz41v\temp_combined.mp4 -ss 0 -to 4.082 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-29 10:41:18,388 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:18,388 - INFO - 目标音频时长: 4.08秒
2025-07-29 10:41:18,388 - INFO - 实际视频时长: 4.14秒
2025-07-29 10:41:18,388 - INFO - 时长差异: 0.06秒 (1.49%)
2025-07-29 10:41:18,388 - INFO - ==========================================
2025-07-29 10:41:18,388 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:18,388 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-29 10:41:18,389 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpasrcz41v
2025-07-29 10:41:18,453 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:18,453 - INFO -   - 音频时长: 4.08秒
2025-07-29 10:41:18,453 - INFO -   - 视频时长: 4.14秒
2025-07-29 10:41:18,453 - INFO -   - 时长差异: 0.06秒 (1.49%)
2025-07-29 10:41:18,453 - INFO - 
----- 处理字幕 #47 的方案 #2 -----
2025-07-29 10:41:18,453 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-29 10:41:18,454 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3au_ersj
2025-07-29 10:41:18,455 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\411.mp4 (确认存在: True)
2025-07-29 10:41:18,455 - INFO - 添加场景ID=411，时长=0.76秒，累计时长=0.76秒
2025-07-29 10:41:18,455 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\412.mp4 (确认存在: True)
2025-07-29 10:41:18,455 - INFO - 添加场景ID=412，时长=1.60秒，累计时长=2.36秒
2025-07-29 10:41:18,455 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\413.mp4 (确认存在: True)
2025-07-29 10:41:18,455 - INFO - 添加场景ID=413，时长=1.04秒，累计时长=3.40秒
2025-07-29 10:41:18,455 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\414.mp4 (确认存在: True)
2025-07-29 10:41:18,455 - INFO - 添加场景ID=414，时长=1.04秒，累计时长=4.44秒
2025-07-29 10:41:18,455 - INFO - 准备合并 4 个场景文件，总时长约 4.44秒
2025-07-29 10:41:18,455 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/411.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/412.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/413.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/414.mp4'

2025-07-29 10:41:18,456 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3au_ersj\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3au_ersj\temp_combined.mp4
2025-07-29 10:41:18,627 - INFO - 合并后的视频时长: 4.53秒，目标音频时长: 4.08秒
2025-07-29 10:41:18,627 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3au_ersj\temp_combined.mp4 -ss 0 -to 4.082 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-29 10:41:18,974 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:18,974 - INFO - 目标音频时长: 4.08秒
2025-07-29 10:41:18,974 - INFO - 实际视频时长: 4.14秒
2025-07-29 10:41:18,974 - INFO - 时长差异: 0.06秒 (1.49%)
2025-07-29 10:41:18,974 - INFO - ==========================================
2025-07-29 10:41:18,974 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:18,974 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-29 10:41:18,975 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3au_ersj
2025-07-29 10:41:19,028 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:19,028 - INFO -   - 音频时长: 4.08秒
2025-07-29 10:41:19,028 - INFO -   - 视频时长: 4.14秒
2025-07-29 10:41:19,028 - INFO -   - 时长差异: 0.06秒 (1.49%)
2025-07-29 10:41:19,029 - INFO - 
----- 处理字幕 #47 的方案 #3 -----
2025-07-29 10:41:19,029 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\47_3.mp4
2025-07-29 10:41:19,029 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6d_g6cgf
2025-07-29 10:41:19,030 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\405.mp4 (确认存在: True)
2025-07-29 10:41:19,030 - INFO - 添加场景ID=405，时长=1.36秒，累计时长=1.36秒
2025-07-29 10:41:19,030 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\411.mp4 (确认存在: True)
2025-07-29 10:41:19,030 - INFO - 添加场景ID=411，时长=0.76秒，累计时长=2.12秒
2025-07-29 10:41:19,030 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\412.mp4 (确认存在: True)
2025-07-29 10:41:19,030 - INFO - 添加场景ID=412，时长=1.60秒，累计时长=3.72秒
2025-07-29 10:41:19,030 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\413.mp4 (确认存在: True)
2025-07-29 10:41:19,030 - INFO - 添加场景ID=413，时长=1.04秒，累计时长=4.76秒
2025-07-29 10:41:19,030 - INFO - 准备合并 4 个场景文件，总时长约 4.76秒
2025-07-29 10:41:19,031 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/405.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/411.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/412.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/413.mp4'

2025-07-29 10:41:19,031 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6d_g6cgf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6d_g6cgf\temp_combined.mp4
2025-07-29 10:41:19,246 - INFO - 合并后的视频时长: 4.85秒，目标音频时长: 4.08秒
2025-07-29 10:41:19,246 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6d_g6cgf\temp_combined.mp4 -ss 0 -to 4.082 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\47_3.mp4
2025-07-29 10:41:19,592 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:19,592 - INFO - 目标音频时长: 4.08秒
2025-07-29 10:41:19,592 - INFO - 实际视频时长: 4.14秒
2025-07-29 10:41:19,592 - INFO - 时长差异: 0.06秒 (1.49%)
2025-07-29 10:41:19,592 - INFO - ==========================================
2025-07-29 10:41:19,592 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:19,592 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\47_3.mp4
2025-07-29 10:41:19,593 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6d_g6cgf
2025-07-29 10:41:19,660 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:19,660 - INFO -   - 音频时长: 4.08秒
2025-07-29 10:41:19,660 - INFO -   - 视频时长: 4.14秒
2025-07-29 10:41:19,660 - INFO -   - 时长差异: 0.06秒 (1.49%)
2025-07-29 10:41:19,660 - INFO - 
字幕 #47 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:19,660 - INFO - 生成的视频文件:
2025-07-29 10:41:19,660 - INFO -   1. F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-29 10:41:19,661 - INFO -   2. F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-07-29 10:41:19,661 - INFO -   3. F:/github/aicut_auto/newcut_ai\47_3.mp4
2025-07-29 10:41:19,661 - INFO - ========== 字幕 #47 处理结束 ==========

