2025-07-29 10:40:48,882 - INFO - ========== 字幕 #26 处理开始 ==========
2025-07-29 10:40:48,882 - INFO - 字幕内容: 哥哥解释因九姨娘不适才迟到，反怪她不懂事。
2025-07-29 10:40:48,882 - INFO - 字幕序号: [140, 145]
2025-07-29 10:40:48,882 - INFO - 音频文件详情:
2025-07-29 10:40:48,882 - INFO -   - 路径: output\26.wav
2025-07-29 10:40:48,882 - INFO -   - 时长: 4.58秒
2025-07-29 10:40:48,883 - INFO -   - 验证音频时长: 4.58秒
2025-07-29 10:40:48,883 - INFO - 字幕时间戳信息:
2025-07-29 10:40:48,883 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:48,883 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:48,883 - INFO -   - 根据生成的音频时长(4.58秒)已调整字幕时间戳
2025-07-29 10:40:48,883 - INFO - ========== 新模式：为字幕 #26 生成4套场景方案 ==========
2025-07-29 10:40:48,883 - INFO - 字幕序号列表: [140, 145]
2025-07-29 10:40:48,883 - INFO - 
--- 生成方案 #1：基于字幕序号 #140 ---
2025-07-29 10:40:48,883 - INFO - 开始为单个字幕序号 #140 匹配场景，目标时长: 4.58秒
2025-07-29 10:40:48,883 - INFO - 开始查找字幕序号 [140] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:48,883 - INFO - 找到related_overlap场景: scene_id=204, 字幕#140
2025-07-29 10:40:48,884 - INFO - 字幕 #140 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:48,884 - INFO - 字幕序号 #140 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:48,885 - INFO - 选择第一个overlap场景作为起点: scene_id=204
2025-07-29 10:40:48,885 - INFO - 添加起点场景: scene_id=204, 时长=4.52秒, 累计时长=4.52秒
2025-07-29 10:40:48,885 - INFO - 起点场景时长不足，需要延伸填充 0.06秒
2025-07-29 10:40:48,885 - INFO - 起点场景在原始列表中的索引: 203
2025-07-29 10:40:48,885 - INFO - 延伸添加场景: scene_id=205 (裁剪至 0.06秒)
2025-07-29 10:40:48,885 - INFO - 累计时长: 4.58秒
2025-07-29 10:40:48,885 - INFO - 字幕序号 #140 场景匹配完成，共选择 2 个场景，总时长: 4.58秒
2025-07-29 10:40:48,885 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:48,885 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:48,885 - INFO - 
--- 生成方案 #2：基于字幕序号 #145 ---
2025-07-29 10:40:48,885 - INFO - 开始为单个字幕序号 #145 匹配场景，目标时长: 4.58秒
2025-07-29 10:40:48,885 - INFO - 开始查找字幕序号 [145] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:48,885 - INFO - 找到related_overlap场景: scene_id=206, 字幕#145
2025-07-29 10:40:48,886 - INFO - 字幕 #145 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:48,886 - INFO - 字幕序号 #145 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:48,886 - INFO - 选择第一个overlap场景作为起点: scene_id=206
2025-07-29 10:40:48,886 - INFO - 添加起点场景: scene_id=206, 时长=3.68秒, 累计时长=3.68秒
2025-07-29 10:40:48,886 - INFO - 起点场景时长不足，需要延伸填充 0.90秒
2025-07-29 10:40:48,886 - INFO - 起点场景在原始列表中的索引: 205
2025-07-29 10:40:48,886 - INFO - 延伸添加场景: scene_id=207 (裁剪至 0.90秒)
2025-07-29 10:40:48,886 - INFO - 累计时长: 4.58秒
2025-07-29 10:40:48,886 - INFO - 字幕序号 #145 场景匹配完成，共选择 2 个场景，总时长: 4.58秒
2025-07-29 10:40:48,886 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:48,886 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:48,886 - INFO - ========== 当前模式：为字幕 #26 生成 1 套场景方案 ==========
2025-07-29 10:40:48,886 - INFO - 开始查找字幕序号 [140, 145] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:48,886 - INFO - 找到related_overlap场景: scene_id=204, 字幕#140
2025-07-29 10:40:48,886 - INFO - 找到related_overlap场景: scene_id=206, 字幕#145
2025-07-29 10:40:48,887 - INFO - 字幕 #140 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:48,887 - INFO - 字幕 #145 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:48,887 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:40:48,887 - INFO - 开始生成方案 #1
2025-07-29 10:40:48,887 - INFO - 方案 #1: 为字幕#140选择初始化overlap场景id=204
2025-07-29 10:40:48,887 - INFO - 方案 #1: 为字幕#145选择初始化overlap场景id=206
2025-07-29 10:40:48,887 - INFO - 方案 #1: 初始选择后，当前总时长=8.20秒
2025-07-29 10:40:48,887 - INFO - 方案 #1: 额外between选择后，当前总时长=8.20秒
2025-07-29 10:40:48,887 - INFO - 方案 #1: 场景总时长(8.20秒)大于音频时长(4.58秒)，需要裁剪
2025-07-29 10:40:48,887 - INFO - 调整前总时长: 8.20秒, 目标时长: 4.58秒
2025-07-29 10:40:48,887 - INFO - 需要裁剪 3.62秒
2025-07-29 10:40:48,887 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:40:48,887 - INFO - 裁剪场景ID=204：从4.52秒裁剪至1.36秒
2025-07-29 10:40:48,887 - INFO - 裁剪场景ID=206：从3.68秒裁剪至3.22秒
2025-07-29 10:40:48,887 - INFO - 调整后总时长: 4.58秒，与目标时长差异: 0.00秒
2025-07-29 10:40:48,887 - INFO - 方案 #1 调整/填充后最终总时长: 4.58秒
2025-07-29 10:40:48,887 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:48,887 - INFO - ========== 当前模式：字幕 #26 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:48,887 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:48,887 - INFO - ========== 新模式：字幕 #26 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:48,887 - INFO - 
----- 处理字幕 #26 的方案 #1 -----
2025-07-29 10:40:48,887 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-29 10:40:48,887 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpo4ysjhz4
2025-07-29 10:40:48,888 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\204.mp4 (确认存在: True)
2025-07-29 10:40:48,888 - INFO - 添加场景ID=204，时长=4.52秒，累计时长=4.52秒
2025-07-29 10:40:48,888 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\205.mp4 (确认存在: True)
2025-07-29 10:40:48,888 - INFO - 添加场景ID=205，时长=3.16秒，累计时长=7.68秒
2025-07-29 10:40:48,888 - INFO - 场景总时长(7.68秒)已达到音频时长(4.58秒)的1.5倍，停止添加场景
2025-07-29 10:40:48,888 - INFO - 准备合并 2 个场景文件，总时长约 7.68秒
2025-07-29 10:40:48,888 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/204.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/205.mp4'

2025-07-29 10:40:48,888 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpo4ysjhz4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpo4ysjhz4\temp_combined.mp4
2025-07-29 10:40:49,016 - INFO - 合并后的视频时长: 7.73秒，目标音频时长: 4.58秒
2025-07-29 10:40:49,016 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpo4ysjhz4\temp_combined.mp4 -ss 0 -to 4.575 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-29 10:40:49,327 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:49,327 - INFO - 目标音频时长: 4.58秒
2025-07-29 10:40:49,327 - INFO - 实际视频时长: 4.62秒
2025-07-29 10:40:49,327 - INFO - 时长差异: 0.05秒 (1.05%)
2025-07-29 10:40:49,327 - INFO - ==========================================
2025-07-29 10:40:49,327 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:49,327 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-29 10:40:49,328 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpo4ysjhz4
2025-07-29 10:40:49,373 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:49,373 - INFO -   - 音频时长: 4.58秒
2025-07-29 10:40:49,373 - INFO -   - 视频时长: 4.62秒
2025-07-29 10:40:49,373 - INFO -   - 时长差异: 0.05秒 (1.05%)
2025-07-29 10:40:49,373 - INFO - 
----- 处理字幕 #26 的方案 #2 -----
2025-07-29 10:40:49,373 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-29 10:40:49,373 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdp2k_ccb
2025-07-29 10:40:49,374 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\206.mp4 (确认存在: True)
2025-07-29 10:40:49,374 - INFO - 添加场景ID=206，时长=3.68秒，累计时长=3.68秒
2025-07-29 10:40:49,374 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\207.mp4 (确认存在: True)
2025-07-29 10:40:49,374 - INFO - 添加场景ID=207，时长=1.24秒，累计时长=4.92秒
2025-07-29 10:40:49,374 - INFO - 准备合并 2 个场景文件，总时长约 4.92秒
2025-07-29 10:40:49,374 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/206.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/207.mp4'

2025-07-29 10:40:49,374 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpdp2k_ccb\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpdp2k_ccb\temp_combined.mp4
2025-07-29 10:40:49,510 - INFO - 合并后的视频时长: 4.97秒，目标音频时长: 4.58秒
2025-07-29 10:40:49,510 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpdp2k_ccb\temp_combined.mp4 -ss 0 -to 4.575 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-29 10:40:49,776 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:49,776 - INFO - 目标音频时长: 4.58秒
2025-07-29 10:40:49,776 - INFO - 实际视频时长: 4.62秒
2025-07-29 10:40:49,776 - INFO - 时长差异: 0.05秒 (1.05%)
2025-07-29 10:40:49,776 - INFO - ==========================================
2025-07-29 10:40:49,776 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:49,776 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-29 10:40:49,777 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdp2k_ccb
2025-07-29 10:40:49,829 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:49,829 - INFO -   - 音频时长: 4.58秒
2025-07-29 10:40:49,829 - INFO -   - 视频时长: 4.62秒
2025-07-29 10:40:49,829 - INFO -   - 时长差异: 0.05秒 (1.05%)
2025-07-29 10:40:49,829 - INFO - 
----- 处理字幕 #26 的方案 #3 -----
2025-07-29 10:40:49,830 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-07-29 10:40:49,830 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9i_dfqqh
2025-07-29 10:40:49,830 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\204.mp4 (确认存在: True)
2025-07-29 10:40:49,830 - INFO - 添加场景ID=204，时长=4.52秒，累计时长=4.52秒
2025-07-29 10:40:49,830 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\206.mp4 (确认存在: True)
2025-07-29 10:40:49,830 - INFO - 添加场景ID=206，时长=3.68秒，累计时长=8.20秒
2025-07-29 10:40:49,830 - INFO - 场景总时长(8.20秒)已达到音频时长(4.58秒)的1.5倍，停止添加场景
2025-07-29 10:40:49,831 - INFO - 准备合并 2 个场景文件，总时长约 8.20秒
2025-07-29 10:40:49,831 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/204.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/206.mp4'

2025-07-29 10:40:49,831 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9i_dfqqh\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9i_dfqqh\temp_combined.mp4
2025-07-29 10:40:49,956 - INFO - 合并后的视频时长: 8.25秒，目标音频时长: 4.58秒
2025-07-29 10:40:49,956 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9i_dfqqh\temp_combined.mp4 -ss 0 -to 4.575 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-07-29 10:40:50,260 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:50,260 - INFO - 目标音频时长: 4.58秒
2025-07-29 10:40:50,260 - INFO - 实际视频时长: 4.62秒
2025-07-29 10:40:50,260 - INFO - 时长差异: 0.05秒 (1.05%)
2025-07-29 10:40:50,261 - INFO - ==========================================
2025-07-29 10:40:50,261 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:50,261 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-07-29 10:40:50,261 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9i_dfqqh
2025-07-29 10:40:50,304 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:50,304 - INFO -   - 音频时长: 4.58秒
2025-07-29 10:40:50,304 - INFO -   - 视频时长: 4.62秒
2025-07-29 10:40:50,304 - INFO -   - 时长差异: 0.05秒 (1.05%)
2025-07-29 10:40:50,304 - INFO - 
字幕 #26 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:50,304 - INFO - 生成的视频文件:
2025-07-29 10:40:50,304 - INFO -   1. F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-07-29 10:40:50,304 - INFO -   2. F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-07-29 10:40:50,305 - INFO -   3. F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-07-29 10:40:50,305 - INFO - ========== 字幕 #26 处理结束 ==========

