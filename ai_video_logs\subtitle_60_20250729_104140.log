2025-07-29 10:41:40,666 - INFO - ========== 字幕 #60 处理开始 ==========
2025-07-29 10:41:40,666 - INFO - 字幕内容: 哥哥以情谊相逼，求他假娶妹妹，风头过后就和离。
2025-07-29 10:41:40,666 - INFO - 字幕序号: [1234, 1238]
2025-07-29 10:41:40,666 - INFO - 音频文件详情:
2025-07-29 10:41:40,666 - INFO -   - 路径: output\60.wav
2025-07-29 10:41:40,667 - INFO -   - 时长: 4.92秒
2025-07-29 10:41:40,667 - INFO -   - 验证音频时长: 4.92秒
2025-07-29 10:41:40,667 - INFO - 字幕时间戳信息:
2025-07-29 10:41:40,667 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:40,667 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:40,667 - INFO -   - 根据生成的音频时长(4.92秒)已调整字幕时间戳
2025-07-29 10:41:40,668 - INFO - ========== 新模式：为字幕 #60 生成4套场景方案 ==========
2025-07-29 10:41:40,668 - INFO - 字幕序号列表: [1234, 1238]
2025-07-29 10:41:40,668 - INFO - 
--- 生成方案 #1：基于字幕序号 #1234 ---
2025-07-29 10:41:40,668 - INFO - 开始为单个字幕序号 #1234 匹配场景，目标时长: 4.92秒
2025-07-29 10:41:40,668 - INFO - 开始查找字幕序号 [1234] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:40,669 - INFO - 找到related_overlap场景: scene_id=1595, 字幕#1234
2025-07-29 10:41:40,673 - INFO - 字幕 #1234 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:40,673 - INFO - 字幕序号 #1234 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:40,673 - INFO - 选择第一个overlap场景作为起点: scene_id=1595
2025-07-29 10:41:40,673 - INFO - 添加起点场景: scene_id=1595, 时长=4.88秒, 累计时长=4.88秒
2025-07-29 10:41:40,673 - INFO - 起点场景时长不足，需要延伸填充 0.04秒
2025-07-29 10:41:40,673 - INFO - 起点场景在原始列表中的索引: 1594
2025-07-29 10:41:40,673 - INFO - 延伸添加场景: scene_id=1596 (裁剪至 0.04秒)
2025-07-29 10:41:40,673 - INFO - 累计时长: 4.92秒
2025-07-29 10:41:40,673 - INFO - 字幕序号 #1234 场景匹配完成，共选择 2 个场景，总时长: 4.92秒
2025-07-29 10:41:40,673 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:41:40,673 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:41:40,673 - INFO - 
--- 生成方案 #2：基于字幕序号 #1238 ---
2025-07-29 10:41:40,673 - INFO - 开始为单个字幕序号 #1238 匹配场景，目标时长: 4.92秒
2025-07-29 10:41:40,673 - INFO - 开始查找字幕序号 [1238] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:40,675 - INFO - 找到related_overlap场景: scene_id=1597, 字幕#1238
2025-07-29 10:41:40,677 - INFO - 字幕 #1238 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:40,677 - INFO - 字幕序号 #1238 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:40,677 - INFO - 选择第一个overlap场景作为起点: scene_id=1597
2025-07-29 10:41:40,677 - INFO - 添加起点场景: scene_id=1597, 时长=1.88秒, 累计时长=1.88秒
2025-07-29 10:41:40,677 - INFO - 起点场景时长不足，需要延伸填充 3.04秒
2025-07-29 10:41:40,677 - INFO - 起点场景在原始列表中的索引: 1596
2025-07-29 10:41:40,677 - INFO - 延伸添加场景: scene_id=1598 (完整时长 1.20秒)
2025-07-29 10:41:40,677 - INFO - 累计时长: 3.08秒
2025-07-29 10:41:40,677 - INFO - 延伸添加场景: scene_id=1599 (完整时长 0.64秒)
2025-07-29 10:41:40,677 - INFO - 累计时长: 3.72秒
2025-07-29 10:41:40,677 - INFO - 延伸添加场景: scene_id=1600 (裁剪至 1.21秒)
2025-07-29 10:41:40,677 - INFO - 累计时长: 4.92秒
2025-07-29 10:41:40,677 - INFO - 字幕序号 #1238 场景匹配完成，共选择 4 个场景，总时长: 4.92秒
2025-07-29 10:41:40,679 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-07-29 10:41:40,679 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:40,679 - INFO - ========== 当前模式：为字幕 #60 生成 1 套场景方案 ==========
2025-07-29 10:41:40,679 - INFO - 开始查找字幕序号 [1234, 1238] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:40,679 - INFO - 找到related_overlap场景: scene_id=1595, 字幕#1234
2025-07-29 10:41:40,679 - INFO - 找到related_overlap场景: scene_id=1597, 字幕#1238
2025-07-29 10:41:40,682 - INFO - 字幕 #1234 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:40,682 - INFO - 字幕 #1238 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:40,682 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:40,682 - INFO - 开始生成方案 #1
2025-07-29 10:41:40,682 - INFO - 方案 #1: 为字幕#1234选择初始化overlap场景id=1595
2025-07-29 10:41:40,682 - INFO - 方案 #1: 为字幕#1238选择初始化overlap场景id=1597
2025-07-29 10:41:40,682 - INFO - 方案 #1: 初始选择后，当前总时长=6.76秒
2025-07-29 10:41:40,682 - INFO - 方案 #1: 额外between选择后，当前总时长=6.76秒
2025-07-29 10:41:40,682 - INFO - 方案 #1: 场景总时长(6.76秒)大于音频时长(4.92秒)，需要裁剪
2025-07-29 10:41:40,682 - INFO - 调整前总时长: 6.76秒, 目标时长: 4.92秒
2025-07-29 10:41:40,682 - INFO - 需要裁剪 1.84秒
2025-07-29 10:41:40,682 - INFO - 裁剪最长场景ID=1595：从4.88秒裁剪至3.04秒
2025-07-29 10:41:40,682 - INFO - 调整后总时长: 4.92秒，与目标时长差异: 0.00秒
2025-07-29 10:41:40,683 - INFO - 方案 #1 调整/填充后最终总时长: 4.92秒
2025-07-29 10:41:40,683 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:40,683 - INFO - ========== 当前模式：字幕 #60 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:40,683 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:40,683 - INFO - ========== 新模式：字幕 #60 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:40,683 - INFO - 
----- 处理字幕 #60 的方案 #1 -----
2025-07-29 10:41:40,683 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-29 10:41:40,683 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvf3s_seh
2025-07-29 10:41:40,684 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1595.mp4 (确认存在: True)
2025-07-29 10:41:40,684 - INFO - 添加场景ID=1595，时长=4.88秒，累计时长=4.88秒
2025-07-29 10:41:40,684 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1596.mp4 (确认存在: True)
2025-07-29 10:41:40,684 - INFO - 添加场景ID=1596，时长=1.28秒，累计时长=6.16秒
2025-07-29 10:41:40,684 - INFO - 准备合并 2 个场景文件，总时长约 6.16秒
2025-07-29 10:41:40,685 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1595.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1596.mp4'

2025-07-29 10:41:40,685 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpvf3s_seh\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpvf3s_seh\temp_combined.mp4
2025-07-29 10:41:40,843 - INFO - 合并后的视频时长: 6.21秒，目标音频时长: 4.92秒
2025-07-29 10:41:40,843 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpvf3s_seh\temp_combined.mp4 -ss 0 -to 4.923 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-29 10:41:41,229 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:41,229 - INFO - 目标音频时长: 4.92秒
2025-07-29 10:41:41,229 - INFO - 实际视频时长: 4.98秒
2025-07-29 10:41:41,229 - INFO - 时长差异: 0.06秒 (1.22%)
2025-07-29 10:41:41,229 - INFO - ==========================================
2025-07-29 10:41:41,229 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:41,229 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-29 10:41:41,230 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvf3s_seh
2025-07-29 10:41:41,291 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:41,291 - INFO -   - 音频时长: 4.92秒
2025-07-29 10:41:41,291 - INFO -   - 视频时长: 4.98秒
2025-07-29 10:41:41,291 - INFO -   - 时长差异: 0.06秒 (1.22%)
2025-07-29 10:41:41,291 - INFO - 
----- 处理字幕 #60 的方案 #2 -----
2025-07-29 10:41:41,291 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-29 10:41:41,292 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbft0bnan
2025-07-29 10:41:41,292 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1597.mp4 (确认存在: True)
2025-07-29 10:41:41,292 - INFO - 添加场景ID=1597，时长=1.88秒，累计时长=1.88秒
2025-07-29 10:41:41,293 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1598.mp4 (确认存在: True)
2025-07-29 10:41:41,293 - INFO - 添加场景ID=1598，时长=1.20秒，累计时长=3.08秒
2025-07-29 10:41:41,293 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1599.mp4 (确认存在: True)
2025-07-29 10:41:41,293 - INFO - 添加场景ID=1599，时长=0.64秒，累计时长=3.72秒
2025-07-29 10:41:41,293 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1600.mp4 (确认存在: True)
2025-07-29 10:41:41,293 - INFO - 添加场景ID=1600，时长=2.24秒，累计时长=5.96秒
2025-07-29 10:41:41,293 - INFO - 准备合并 4 个场景文件，总时长约 5.96秒
2025-07-29 10:41:41,293 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1597.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1598.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1599.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1600.mp4'

2025-07-29 10:41:41,294 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbft0bnan\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbft0bnan\temp_combined.mp4
2025-07-29 10:41:41,473 - INFO - 合并后的视频时长: 6.05秒，目标音频时长: 4.92秒
2025-07-29 10:41:41,473 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbft0bnan\temp_combined.mp4 -ss 0 -to 4.923 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-29 10:41:41,826 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:41,826 - INFO - 目标音频时长: 4.92秒
2025-07-29 10:41:41,827 - INFO - 实际视频时长: 4.98秒
2025-07-29 10:41:41,827 - INFO - 时长差异: 0.06秒 (1.22%)
2025-07-29 10:41:41,827 - INFO - ==========================================
2025-07-29 10:41:41,827 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:41,827 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-29 10:41:41,828 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbft0bnan
2025-07-29 10:41:41,893 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:41,893 - INFO -   - 音频时长: 4.92秒
2025-07-29 10:41:41,893 - INFO -   - 视频时长: 4.98秒
2025-07-29 10:41:41,893 - INFO -   - 时长差异: 0.06秒 (1.22%)
2025-07-29 10:41:41,893 - INFO - 
----- 处理字幕 #60 的方案 #3 -----
2025-07-29 10:41:41,894 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-07-29 10:41:41,894 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp86xam8qf
2025-07-29 10:41:41,895 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1595.mp4 (确认存在: True)
2025-07-29 10:41:41,895 - INFO - 添加场景ID=1595，时长=4.88秒，累计时长=4.88秒
2025-07-29 10:41:41,895 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1597.mp4 (确认存在: True)
2025-07-29 10:41:41,895 - INFO - 添加场景ID=1597，时长=1.88秒，累计时长=6.76秒
2025-07-29 10:41:41,895 - INFO - 准备合并 2 个场景文件，总时长约 6.76秒
2025-07-29 10:41:41,896 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1595.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1597.mp4'

2025-07-29 10:41:41,896 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp86xam8qf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp86xam8qf\temp_combined.mp4
2025-07-29 10:41:42,052 - INFO - 合并后的视频时长: 6.81秒，目标音频时长: 4.92秒
2025-07-29 10:41:42,052 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp86xam8qf\temp_combined.mp4 -ss 0 -to 4.923 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-07-29 10:41:42,421 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:42,421 - INFO - 目标音频时长: 4.92秒
2025-07-29 10:41:42,421 - INFO - 实际视频时长: 4.98秒
2025-07-29 10:41:42,421 - INFO - 时长差异: 0.06秒 (1.22%)
2025-07-29 10:41:42,421 - INFO - ==========================================
2025-07-29 10:41:42,421 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:42,421 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-07-29 10:41:42,422 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp86xam8qf
2025-07-29 10:41:42,486 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:42,486 - INFO -   - 音频时长: 4.92秒
2025-07-29 10:41:42,486 - INFO -   - 视频时长: 4.98秒
2025-07-29 10:41:42,486 - INFO -   - 时长差异: 0.06秒 (1.22%)
2025-07-29 10:41:42,486 - INFO - 
字幕 #60 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:42,486 - INFO - 生成的视频文件:
2025-07-29 10:41:42,486 - INFO -   1. F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-07-29 10:41:42,486 - INFO -   2. F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-07-29 10:41:42,486 - INFO -   3. F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-07-29 10:41:42,486 - INFO - ========== 字幕 #60 处理结束 ==========

