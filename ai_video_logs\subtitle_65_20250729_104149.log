2025-07-29 10:41:49,167 - INFO - ========== 字幕 #65 处理开始 ==========
2025-07-29 10:41:49,167 - INFO - 字幕内容: 他质问哥哥是否想让她孤独终老，哥哥却毫不在意。
2025-07-29 10:41:49,167 - INFO - 字幕序号: [1262, 1265]
2025-07-29 10:41:49,168 - INFO - 音频文件详情:
2025-07-29 10:41:49,168 - INFO -   - 路径: output\65.wav
2025-07-29 10:41:49,168 - INFO -   - 时长: 3.34秒
2025-07-29 10:41:49,168 - INFO -   - 验证音频时长: 3.34秒
2025-07-29 10:41:49,168 - INFO - 字幕时间戳信息:
2025-07-29 10:41:49,168 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:49,169 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:49,169 - INFO -   - 根据生成的音频时长(3.34秒)已调整字幕时间戳
2025-07-29 10:41:49,169 - INFO - ========== 新模式：为字幕 #65 生成4套场景方案 ==========
2025-07-29 10:41:49,169 - INFO - 字幕序号列表: [1262, 1265]
2025-07-29 10:41:49,169 - INFO - 
--- 生成方案 #1：基于字幕序号 #1262 ---
2025-07-29 10:41:49,169 - INFO - 开始为单个字幕序号 #1262 匹配场景，目标时长: 3.34秒
2025-07-29 10:41:49,169 - INFO - 开始查找字幕序号 [1262] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:49,170 - INFO - 找到related_overlap场景: scene_id=1614, 字幕#1262
2025-07-29 10:41:49,170 - INFO - 找到related_overlap场景: scene_id=1615, 字幕#1262
2025-07-29 10:41:49,172 - INFO - 字幕 #1262 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:49,172 - INFO - 字幕序号 #1262 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:49,172 - INFO - 选择第一个overlap场景作为起点: scene_id=1614
2025-07-29 10:41:49,172 - INFO - 添加起点场景: scene_id=1614, 时长=1.88秒, 累计时长=1.88秒
2025-07-29 10:41:49,172 - INFO - 起点场景时长不足，需要延伸填充 1.46秒
2025-07-29 10:41:49,173 - INFO - 起点场景在原始列表中的索引: 1613
2025-07-29 10:41:49,173 - INFO - 延伸添加场景: scene_id=1615 (完整时长 0.88秒)
2025-07-29 10:41:49,173 - INFO - 累计时长: 2.76秒
2025-07-29 10:41:49,173 - INFO - 延伸添加场景: scene_id=1616 (裁剪至 0.58秒)
2025-07-29 10:41:49,173 - INFO - 累计时长: 3.34秒
2025-07-29 10:41:49,173 - INFO - 字幕序号 #1262 场景匹配完成，共选择 3 个场景，总时长: 3.34秒
2025-07-29 10:41:49,173 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:49,173 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:49,173 - INFO - 
--- 生成方案 #2：基于字幕序号 #1265 ---
2025-07-29 10:41:49,173 - INFO - 开始为单个字幕序号 #1265 匹配场景，目标时长: 3.34秒
2025-07-29 10:41:49,173 - INFO - 开始查找字幕序号 [1265] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:49,174 - INFO - 找到related_overlap场景: scene_id=1617, 字幕#1265
2025-07-29 10:41:49,175 - INFO - 字幕 #1265 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:49,175 - INFO - 字幕序号 #1265 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:49,175 - INFO - 选择第一个overlap场景作为起点: scene_id=1617
2025-07-29 10:41:49,175 - INFO - 添加起点场景: scene_id=1617, 时长=3.32秒, 累计时长=3.32秒
2025-07-29 10:41:49,175 - INFO - 起点场景时长不足，需要延伸填充 0.02秒
2025-07-29 10:41:49,175 - INFO - 起点场景在原始列表中的索引: 1616
2025-07-29 10:41:49,175 - INFO - 延伸添加场景: scene_id=1618 (裁剪至 0.02秒)
2025-07-29 10:41:49,175 - INFO - 累计时长: 3.34秒
2025-07-29 10:41:49,175 - INFO - 字幕序号 #1265 场景匹配完成，共选择 2 个场景，总时长: 3.34秒
2025-07-29 10:41:49,175 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:49,175 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:49,176 - INFO - ========== 当前模式：为字幕 #65 生成 1 套场景方案 ==========
2025-07-29 10:41:49,176 - INFO - 开始查找字幕序号 [1262, 1265] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:49,176 - INFO - 找到related_overlap场景: scene_id=1614, 字幕#1262
2025-07-29 10:41:49,176 - INFO - 找到related_overlap场景: scene_id=1615, 字幕#1262
2025-07-29 10:41:49,176 - INFO - 找到related_overlap场景: scene_id=1617, 字幕#1265
2025-07-29 10:41:49,177 - INFO - 字幕 #1262 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:49,177 - INFO - 字幕 #1265 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:49,177 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:49,178 - INFO - 开始生成方案 #1
2025-07-29 10:41:49,178 - INFO - 方案 #1: 为字幕#1262选择初始化overlap场景id=1614
2025-07-29 10:41:49,178 - INFO - 方案 #1: 为字幕#1265选择初始化overlap场景id=1617
2025-07-29 10:41:49,178 - INFO - 方案 #1: 初始选择后，当前总时长=5.20秒
2025-07-29 10:41:49,178 - INFO - 方案 #1: 额外between选择后，当前总时长=5.20秒
2025-07-29 10:41:49,178 - INFO - 方案 #1: 场景总时长(5.20秒)大于音频时长(3.34秒)，需要裁剪
2025-07-29 10:41:49,178 - INFO - 调整前总时长: 5.20秒, 目标时长: 3.34秒
2025-07-29 10:41:49,178 - INFO - 需要裁剪 1.86秒
2025-07-29 10:41:49,178 - INFO - 裁剪最长场景ID=1617：从3.32秒裁剪至1.46秒
2025-07-29 10:41:49,178 - INFO - 调整后总时长: 3.34秒，与目标时长差异: 0.00秒
2025-07-29 10:41:49,178 - INFO - 方案 #1 调整/填充后最终总时长: 3.34秒
2025-07-29 10:41:49,178 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:49,178 - INFO - ========== 当前模式：字幕 #65 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:49,178 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:49,178 - INFO - ========== 新模式：字幕 #65 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:49,178 - INFO - 
----- 处理字幕 #65 的方案 #1 -----
2025-07-29 10:41:49,178 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-07-29 10:41:49,179 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpevu58jcy
2025-07-29 10:41:49,179 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1614.mp4 (确认存在: True)
2025-07-29 10:41:49,179 - INFO - 添加场景ID=1614，时长=1.88秒，累计时长=1.88秒
2025-07-29 10:41:49,180 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1615.mp4 (确认存在: True)
2025-07-29 10:41:49,180 - INFO - 添加场景ID=1615，时长=0.88秒，累计时长=2.76秒
2025-07-29 10:41:49,180 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1616.mp4 (确认存在: True)
2025-07-29 10:41:49,180 - INFO - 添加场景ID=1616，时长=1.88秒，累计时长=4.64秒
2025-07-29 10:41:49,180 - INFO - 准备合并 3 个场景文件，总时长约 4.64秒
2025-07-29 10:41:49,180 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1614.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1615.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1616.mp4'

2025-07-29 10:41:49,180 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpevu58jcy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpevu58jcy\temp_combined.mp4
2025-07-29 10:41:49,366 - INFO - 合并后的视频时长: 4.71秒，目标音频时长: 3.34秒
2025-07-29 10:41:49,366 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpevu58jcy\temp_combined.mp4 -ss 0 -to 3.338 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-07-29 10:41:49,654 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:49,654 - INFO - 目标音频时长: 3.34秒
2025-07-29 10:41:49,654 - INFO - 实际视频时长: 3.38秒
2025-07-29 10:41:49,654 - INFO - 时长差异: 0.04秒 (1.35%)
2025-07-29 10:41:49,654 - INFO - ==========================================
2025-07-29 10:41:49,654 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:49,654 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-07-29 10:41:49,655 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpevu58jcy
2025-07-29 10:41:49,718 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:49,718 - INFO -   - 音频时长: 3.34秒
2025-07-29 10:41:49,718 - INFO -   - 视频时长: 3.38秒
2025-07-29 10:41:49,718 - INFO -   - 时长差异: 0.04秒 (1.35%)
2025-07-29 10:41:49,718 - INFO - 
----- 处理字幕 #65 的方案 #2 -----
2025-07-29 10:41:49,718 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-07-29 10:41:49,718 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjep74kcn
2025-07-29 10:41:49,718 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1617.mp4 (确认存在: True)
2025-07-29 10:41:49,718 - INFO - 添加场景ID=1617，时长=3.32秒，累计时长=3.32秒
2025-07-29 10:41:49,719 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1618.mp4 (确认存在: True)
2025-07-29 10:41:49,719 - INFO - 添加场景ID=1618，时长=4.12秒，累计时长=7.44秒
2025-07-29 10:41:49,719 - INFO - 场景总时长(7.44秒)已达到音频时长(3.34秒)的1.5倍，停止添加场景
2025-07-29 10:41:49,719 - INFO - 准备合并 2 个场景文件，总时长约 7.44秒
2025-07-29 10:41:49,719 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1617.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1618.mp4'

2025-07-29 10:41:49,719 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjep74kcn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjep74kcn\temp_combined.mp4
2025-07-29 10:41:49,883 - INFO - 合并后的视频时长: 7.49秒，目标音频时长: 3.34秒
2025-07-29 10:41:49,883 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjep74kcn\temp_combined.mp4 -ss 0 -to 3.338 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-07-29 10:41:50,161 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:50,162 - INFO - 目标音频时长: 3.34秒
2025-07-29 10:41:50,162 - INFO - 实际视频时长: 3.38秒
2025-07-29 10:41:50,162 - INFO - 时长差异: 0.04秒 (1.35%)
2025-07-29 10:41:50,162 - INFO - ==========================================
2025-07-29 10:41:50,162 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:50,162 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-07-29 10:41:50,163 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjep74kcn
2025-07-29 10:41:50,219 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:50,219 - INFO -   - 音频时长: 3.34秒
2025-07-29 10:41:50,219 - INFO -   - 视频时长: 3.38秒
2025-07-29 10:41:50,219 - INFO -   - 时长差异: 0.04秒 (1.35%)
2025-07-29 10:41:50,219 - INFO - 
----- 处理字幕 #65 的方案 #3 -----
2025-07-29 10:41:50,219 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-07-29 10:41:50,220 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprxtzan5c
2025-07-29 10:41:50,221 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1614.mp4 (确认存在: True)
2025-07-29 10:41:50,221 - INFO - 添加场景ID=1614，时长=1.88秒，累计时长=1.88秒
2025-07-29 10:41:50,221 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1617.mp4 (确认存在: True)
2025-07-29 10:41:50,221 - INFO - 添加场景ID=1617，时长=3.32秒，累计时长=5.20秒
2025-07-29 10:41:50,221 - INFO - 场景总时长(5.20秒)已达到音频时长(3.34秒)的1.5倍，停止添加场景
2025-07-29 10:41:50,221 - INFO - 准备合并 2 个场景文件，总时长约 5.20秒
2025-07-29 10:41:50,221 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1614.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1617.mp4'

2025-07-29 10:41:50,222 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmprxtzan5c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmprxtzan5c\temp_combined.mp4
2025-07-29 10:41:50,392 - INFO - 合并后的视频时长: 5.25秒，目标音频时长: 3.34秒
2025-07-29 10:41:50,392 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmprxtzan5c\temp_combined.mp4 -ss 0 -to 3.338 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-07-29 10:41:50,681 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:50,681 - INFO - 目标音频时长: 3.34秒
2025-07-29 10:41:50,681 - INFO - 实际视频时长: 3.38秒
2025-07-29 10:41:50,681 - INFO - 时长差异: 0.04秒 (1.35%)
2025-07-29 10:41:50,681 - INFO - ==========================================
2025-07-29 10:41:50,681 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:50,681 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-07-29 10:41:50,682 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprxtzan5c
2025-07-29 10:41:50,748 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:50,748 - INFO -   - 音频时长: 3.34秒
2025-07-29 10:41:50,748 - INFO -   - 视频时长: 3.38秒
2025-07-29 10:41:50,748 - INFO -   - 时长差异: 0.04秒 (1.35%)
2025-07-29 10:41:50,748 - INFO - 
字幕 #65 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:50,748 - INFO - 生成的视频文件:
2025-07-29 10:41:50,748 - INFO -   1. F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-07-29 10:41:50,748 - INFO -   2. F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-07-29 10:41:50,748 - INFO -   3. F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-07-29 10:41:50,748 - INFO - ========== 字幕 #65 处理结束 ==========

