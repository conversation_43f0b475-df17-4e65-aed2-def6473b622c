2025-07-29 10:41:24,959 - INFO - ========== 字幕 #51 处理开始 ==========
2025-07-29 10:41:24,959 - INFO - 字幕内容: 她说府医可证，并哭求哥哥救他们的孩子。
2025-07-29 10:41:24,959 - INFO - 字幕序号: [291, 295]
2025-07-29 10:41:24,960 - INFO - 音频文件详情:
2025-07-29 10:41:24,960 - INFO -   - 路径: output\51.wav
2025-07-29 10:41:24,960 - INFO -   - 时长: 3.49秒
2025-07-29 10:41:24,961 - INFO -   - 验证音频时长: 3.49秒
2025-07-29 10:41:24,961 - INFO - 字幕时间戳信息:
2025-07-29 10:41:24,961 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:24,963 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:24,963 - INFO -   - 根据生成的音频时长(3.49秒)已调整字幕时间戳
2025-07-29 10:41:24,963 - INFO - ========== 新模式：为字幕 #51 生成4套场景方案 ==========
2025-07-29 10:41:24,963 - INFO - 字幕序号列表: [291, 295]
2025-07-29 10:41:24,963 - INFO - 
--- 生成方案 #1：基于字幕序号 #291 ---
2025-07-29 10:41:24,963 - INFO - 开始为单个字幕序号 #291 匹配场景，目标时长: 3.49秒
2025-07-29 10:41:24,963 - INFO - 开始查找字幕序号 [291] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:24,963 - INFO - 找到related_overlap场景: scene_id=434, 字幕#291
2025-07-29 10:41:24,963 - INFO - 找到related_overlap场景: scene_id=435, 字幕#291
2025-07-29 10:41:24,966 - INFO - 找到related_between场景: scene_id=430, 字幕#291
2025-07-29 10:41:24,966 - INFO - 找到related_between场景: scene_id=431, 字幕#291
2025-07-29 10:41:24,966 - INFO - 找到related_between场景: scene_id=432, 字幕#291
2025-07-29 10:41:24,967 - INFO - 找到related_between场景: scene_id=433, 字幕#291
2025-07-29 10:41:24,968 - INFO - 字幕 #291 找到 2 个overlap场景, 4 个between场景
2025-07-29 10:41:24,968 - INFO - 字幕序号 #291 找到 2 个可用overlap场景, 4 个可用between场景
2025-07-29 10:41:24,968 - INFO - 选择第一个overlap场景作为起点: scene_id=434
2025-07-29 10:41:24,968 - INFO - 添加起点场景: scene_id=434, 时长=1.00秒, 累计时长=1.00秒
2025-07-29 10:41:24,968 - INFO - 起点场景时长不足，需要延伸填充 2.49秒
2025-07-29 10:41:24,968 - INFO - 起点场景在原始列表中的索引: 433
2025-07-29 10:41:24,968 - INFO - 延伸添加场景: scene_id=435 (完整时长 1.60秒)
2025-07-29 10:41:24,968 - INFO - 累计时长: 2.60秒
2025-07-29 10:41:24,968 - INFO - 延伸添加场景: scene_id=436 (裁剪至 0.89秒)
2025-07-29 10:41:24,968 - INFO - 累计时长: 3.49秒
2025-07-29 10:41:24,968 - INFO - 字幕序号 #291 场景匹配完成，共选择 3 个场景，总时长: 3.49秒
2025-07-29 10:41:24,968 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:24,968 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:24,968 - INFO - 
--- 生成方案 #2：基于字幕序号 #295 ---
2025-07-29 10:41:24,969 - INFO - 开始为单个字幕序号 #295 匹配场景，目标时长: 3.49秒
2025-07-29 10:41:24,969 - INFO - 开始查找字幕序号 [295] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:24,969 - INFO - 找到related_overlap场景: scene_id=436, 字幕#295
2025-07-29 10:41:24,970 - INFO - 找到related_between场景: scene_id=437, 字幕#295
2025-07-29 10:41:24,971 - INFO - 字幕 #295 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:24,971 - INFO - 字幕序号 #295 找到 0 个可用overlap场景, 1 个可用between场景
2025-07-29 10:41:24,971 - INFO - 没有overlap场景，选择第一个between场景作为起点: scene_id=437
2025-07-29 10:41:24,971 - INFO - 添加起点场景: scene_id=437, 时长=1.24秒, 累计时长=1.24秒
2025-07-29 10:41:24,971 - INFO - 起点场景时长不足，需要延伸填充 2.25秒
2025-07-29 10:41:24,971 - INFO - 起点场景在原始列表中的索引: 436
2025-07-29 10:41:24,971 - INFO - 延伸添加场景: scene_id=438 (裁剪至 2.25秒)
2025-07-29 10:41:24,971 - INFO - 累计时长: 3.49秒
2025-07-29 10:41:24,971 - INFO - 字幕序号 #295 场景匹配完成，共选择 2 个场景，总时长: 3.49秒
2025-07-29 10:41:24,971 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:41:24,971 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:24,971 - INFO - ========== 当前模式：为字幕 #51 生成 1 套场景方案 ==========
2025-07-29 10:41:24,971 - INFO - 开始查找字幕序号 [291, 295] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:24,972 - INFO - 找到related_overlap场景: scene_id=434, 字幕#291
2025-07-29 10:41:24,972 - INFO - 找到related_overlap场景: scene_id=435, 字幕#291
2025-07-29 10:41:24,972 - INFO - 找到related_overlap场景: scene_id=436, 字幕#295
2025-07-29 10:41:24,973 - INFO - 找到related_between场景: scene_id=430, 字幕#291
2025-07-29 10:41:24,973 - INFO - 找到related_between场景: scene_id=431, 字幕#291
2025-07-29 10:41:24,973 - INFO - 找到related_between场景: scene_id=432, 字幕#291
2025-07-29 10:41:24,973 - INFO - 找到related_between场景: scene_id=433, 字幕#291
2025-07-29 10:41:24,973 - INFO - 找到related_between场景: scene_id=437, 字幕#295
2025-07-29 10:41:24,974 - INFO - 字幕 #291 找到 2 个overlap场景, 4 个between场景
2025-07-29 10:41:24,974 - INFO - 字幕 #295 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:24,974 - INFO - 共收集 3 个未使用的overlap场景和 5 个未使用的between场景
2025-07-29 10:41:24,974 - INFO - 开始生成方案 #1
2025-07-29 10:41:24,974 - INFO - 方案 #1: 为字幕#291选择初始化overlap场景id=434
2025-07-29 10:41:24,974 - INFO - 方案 #1: 为字幕#295选择初始化overlap场景id=436
2025-07-29 10:41:24,974 - INFO - 方案 #1: 初始选择后，当前总时长=5.04秒
2025-07-29 10:41:24,974 - INFO - 方案 #1: 额外between选择后，当前总时长=5.04秒
2025-07-29 10:41:24,974 - INFO - 方案 #1: 场景总时长(5.04秒)大于音频时长(3.49秒)，需要裁剪
2025-07-29 10:41:24,974 - INFO - 调整前总时长: 5.04秒, 目标时长: 3.49秒
2025-07-29 10:41:24,974 - INFO - 需要裁剪 1.55秒
2025-07-29 10:41:24,974 - INFO - 裁剪最长场景ID=436：从4.04秒裁剪至2.49秒
2025-07-29 10:41:24,974 - INFO - 调整后总时长: 3.49秒，与目标时长差异: 0.00秒
2025-07-29 10:41:24,974 - INFO - 方案 #1 调整/填充后最终总时长: 3.49秒
2025-07-29 10:41:24,974 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:24,974 - INFO - ========== 当前模式：字幕 #51 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:24,974 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:24,974 - INFO - ========== 新模式：字幕 #51 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:24,974 - INFO - 
----- 处理字幕 #51 的方案 #1 -----
2025-07-29 10:41:24,975 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-07-29 10:41:24,975 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc8r6ocmy
2025-07-29 10:41:24,976 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\434.mp4 (确认存在: True)
2025-07-29 10:41:24,976 - INFO - 添加场景ID=434，时长=1.00秒，累计时长=1.00秒
2025-07-29 10:41:24,976 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\435.mp4 (确认存在: True)
2025-07-29 10:41:24,976 - INFO - 添加场景ID=435，时长=1.60秒，累计时长=2.60秒
2025-07-29 10:41:24,976 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\436.mp4 (确认存在: True)
2025-07-29 10:41:24,976 - INFO - 添加场景ID=436，时长=4.04秒，累计时长=6.64秒
2025-07-29 10:41:24,976 - INFO - 场景总时长(6.64秒)已达到音频时长(3.49秒)的1.5倍，停止添加场景
2025-07-29 10:41:24,976 - INFO - 准备合并 3 个场景文件，总时长约 6.64秒
2025-07-29 10:41:24,976 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/434.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/435.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/436.mp4'

2025-07-29 10:41:24,976 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpc8r6ocmy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpc8r6ocmy\temp_combined.mp4
2025-07-29 10:41:25,165 - INFO - 合并后的视频时长: 6.71秒，目标音频时长: 3.49秒
2025-07-29 10:41:25,165 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpc8r6ocmy\temp_combined.mp4 -ss 0 -to 3.486 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-07-29 10:41:25,541 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:25,541 - INFO - 目标音频时长: 3.49秒
2025-07-29 10:41:25,541 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:41:25,541 - INFO - 时长差异: 0.06秒 (1.64%)
2025-07-29 10:41:25,541 - INFO - ==========================================
2025-07-29 10:41:25,541 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:25,541 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-07-29 10:41:25,542 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc8r6ocmy
2025-07-29 10:41:25,605 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:25,605 - INFO -   - 音频时长: 3.49秒
2025-07-29 10:41:25,605 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:41:25,605 - INFO -   - 时长差异: 0.06秒 (1.64%)
2025-07-29 10:41:25,605 - INFO - 
----- 处理字幕 #51 的方案 #2 -----
2025-07-29 10:41:25,605 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-07-29 10:41:25,605 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnw9lzsk1
2025-07-29 10:41:25,606 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\437.mp4 (确认存在: True)
2025-07-29 10:41:25,606 - INFO - 添加场景ID=437，时长=1.24秒，累计时长=1.24秒
2025-07-29 10:41:25,606 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\438.mp4 (确认存在: True)
2025-07-29 10:41:25,606 - INFO - 添加场景ID=438，时长=2.40秒，累计时长=3.64秒
2025-07-29 10:41:25,606 - INFO - 准备合并 2 个场景文件，总时长约 3.64秒
2025-07-29 10:41:25,606 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/437.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/438.mp4'

2025-07-29 10:41:25,606 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnw9lzsk1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnw9lzsk1\temp_combined.mp4
2025-07-29 10:41:25,756 - INFO - 合并后的视频时长: 3.69秒，目标音频时长: 3.49秒
2025-07-29 10:41:25,756 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnw9lzsk1\temp_combined.mp4 -ss 0 -to 3.486 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-07-29 10:41:26,106 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:26,106 - INFO - 目标音频时长: 3.49秒
2025-07-29 10:41:26,106 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:41:26,106 - INFO - 时长差异: 0.06秒 (1.64%)
2025-07-29 10:41:26,106 - INFO - ==========================================
2025-07-29 10:41:26,107 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:26,107 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-07-29 10:41:26,108 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnw9lzsk1
2025-07-29 10:41:26,170 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:26,170 - INFO -   - 音频时长: 3.49秒
2025-07-29 10:41:26,170 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:41:26,170 - INFO -   - 时长差异: 0.06秒 (1.64%)
2025-07-29 10:41:26,170 - INFO - 
----- 处理字幕 #51 的方案 #3 -----
2025-07-29 10:41:26,170 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-07-29 10:41:26,171 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplsqrewmo
2025-07-29 10:41:26,171 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\434.mp4 (确认存在: True)
2025-07-29 10:41:26,171 - INFO - 添加场景ID=434，时长=1.00秒，累计时长=1.00秒
2025-07-29 10:41:26,172 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\436.mp4 (确认存在: True)
2025-07-29 10:41:26,172 - INFO - 添加场景ID=436，时长=4.04秒，累计时长=5.04秒
2025-07-29 10:41:26,172 - INFO - 准备合并 2 个场景文件，总时长约 5.04秒
2025-07-29 10:41:26,172 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/434.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/436.mp4'

2025-07-29 10:41:26,172 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplsqrewmo\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplsqrewmo\temp_combined.mp4
2025-07-29 10:41:26,333 - INFO - 合并后的视频时长: 5.09秒，目标音频时长: 3.49秒
2025-07-29 10:41:26,333 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplsqrewmo\temp_combined.mp4 -ss 0 -to 3.486 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-07-29 10:41:26,729 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:26,729 - INFO - 目标音频时长: 3.49秒
2025-07-29 10:41:26,729 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:41:26,729 - INFO - 时长差异: 0.06秒 (1.64%)
2025-07-29 10:41:26,729 - INFO - ==========================================
2025-07-29 10:41:26,729 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:26,729 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-07-29 10:41:26,730 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplsqrewmo
2025-07-29 10:41:26,799 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:26,799 - INFO -   - 音频时长: 3.49秒
2025-07-29 10:41:26,799 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:41:26,799 - INFO -   - 时长差异: 0.06秒 (1.64%)
2025-07-29 10:41:26,799 - INFO - 
字幕 #51 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:26,799 - INFO - 生成的视频文件:
2025-07-29 10:41:26,799 - INFO -   1. F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-07-29 10:41:26,799 - INFO -   2. F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-07-29 10:41:26,799 - INFO -   3. F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-07-29 10:41:26,799 - INFO - ========== 字幕 #51 处理结束 ==========

