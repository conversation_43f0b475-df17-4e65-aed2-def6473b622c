2025-07-29 10:41:50,748 - INFO - ========== 字幕 #66 处理开始 ==========
2025-07-29 10:41:50,748 - INFO - 字幕内容: 摄政王最终答应了这桩荒唐交易，并要求保证。
2025-07-29 10:41:50,748 - INFO - 字幕序号: [1266, 1271]
2025-07-29 10:41:50,749 - INFO - 音频文件详情:
2025-07-29 10:41:50,749 - INFO -   - 路径: output\66.wav
2025-07-29 10:41:50,749 - INFO -   - 时长: 3.19秒
2025-07-29 10:41:50,749 - INFO -   - 验证音频时长: 3.19秒
2025-07-29 10:41:50,749 - INFO - 字幕时间戳信息:
2025-07-29 10:41:50,749 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:50,749 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:50,749 - INFO -   - 根据生成的音频时长(3.19秒)已调整字幕时间戳
2025-07-29 10:41:50,749 - INFO - ========== 新模式：为字幕 #66 生成4套场景方案 ==========
2025-07-29 10:41:50,749 - INFO - 字幕序号列表: [1266, 1271]
2025-07-29 10:41:50,749 - INFO - 
--- 生成方案 #1：基于字幕序号 #1266 ---
2025-07-29 10:41:50,749 - INFO - 开始为单个字幕序号 #1266 匹配场景，目标时长: 3.19秒
2025-07-29 10:41:50,749 - INFO - 开始查找字幕序号 [1266] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:50,750 - INFO - 找到related_overlap场景: scene_id=1618, 字幕#1266
2025-07-29 10:41:50,751 - INFO - 字幕 #1266 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:50,751 - INFO - 字幕序号 #1266 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:50,751 - INFO - 选择第一个overlap场景作为起点: scene_id=1618
2025-07-29 10:41:50,751 - INFO - 添加起点场景: scene_id=1618, 时长=4.12秒, 累计时长=4.12秒
2025-07-29 10:41:50,751 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:50,751 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:41:50,751 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:41:50,751 - INFO - 
--- 生成方案 #2：基于字幕序号 #1271 ---
2025-07-29 10:41:50,751 - INFO - 开始为单个字幕序号 #1271 匹配场景，目标时长: 3.19秒
2025-07-29 10:41:50,751 - INFO - 开始查找字幕序号 [1271] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:50,751 - INFO - 找到related_overlap场景: scene_id=1622, 字幕#1271
2025-07-29 10:41:50,751 - INFO - 找到related_overlap场景: scene_id=1623, 字幕#1271
2025-07-29 10:41:50,752 - INFO - 字幕 #1271 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:50,752 - INFO - 字幕序号 #1271 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:50,752 - INFO - 选择第一个overlap场景作为起点: scene_id=1622
2025-07-29 10:41:50,752 - INFO - 添加起点场景: scene_id=1622, 时长=4.08秒, 累计时长=4.08秒
2025-07-29 10:41:50,752 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:41:50,752 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-07-29 10:41:50,752 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:50,752 - INFO - ========== 当前模式：为字幕 #66 生成 1 套场景方案 ==========
2025-07-29 10:41:50,752 - INFO - 开始查找字幕序号 [1266, 1271] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:50,752 - INFO - 找到related_overlap场景: scene_id=1618, 字幕#1266
2025-07-29 10:41:50,752 - INFO - 找到related_overlap场景: scene_id=1622, 字幕#1271
2025-07-29 10:41:50,752 - INFO - 找到related_overlap场景: scene_id=1623, 字幕#1271
2025-07-29 10:41:50,753 - INFO - 字幕 #1266 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:50,753 - INFO - 字幕 #1271 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:41:50,754 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:41:50,754 - INFO - 开始生成方案 #1
2025-07-29 10:41:50,754 - INFO - 方案 #1: 为字幕#1266选择初始化overlap场景id=1618
2025-07-29 10:41:50,754 - INFO - 方案 #1: 为字幕#1271选择初始化overlap场景id=1623
2025-07-29 10:41:50,754 - INFO - 方案 #1: 初始选择后，当前总时长=13.80秒
2025-07-29 10:41:50,754 - INFO - 方案 #1: 额外between选择后，当前总时长=13.80秒
2025-07-29 10:41:50,754 - INFO - 方案 #1: 场景总时长(13.80秒)大于音频时长(3.19秒)，需要裁剪
2025-07-29 10:41:50,754 - INFO - 调整前总时长: 13.80秒, 目标时长: 3.19秒
2025-07-29 10:41:50,754 - INFO - 需要裁剪 10.61秒
2025-07-29 10:41:50,754 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:41:50,754 - INFO - 裁剪场景ID=1623：从9.68秒裁剪至2.90秒
2025-07-29 10:41:50,754 - INFO - 裁剪场景ID=1618：从4.12秒裁剪至2.90秒
2025-07-29 10:41:50,754 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 2.62秒
2025-07-29 10:41:50,754 - INFO - 调整后总时长: 5.81秒，与目标时长差异: 2.62秒
2025-07-29 10:41:50,754 - INFO - 方案 #1 调整/填充后最终总时长: 5.81秒
2025-07-29 10:41:50,754 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:50,754 - INFO - ========== 当前模式：字幕 #66 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:50,754 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:50,754 - INFO - ========== 新模式：字幕 #66 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:50,754 - INFO - 
----- 处理字幕 #66 的方案 #1 -----
2025-07-29 10:41:50,755 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-07-29 10:41:50,755 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_cm5t785
2025-07-29 10:41:50,756 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1618.mp4 (确认存在: True)
2025-07-29 10:41:50,756 - INFO - 添加场景ID=1618，时长=4.12秒，累计时长=4.12秒
2025-07-29 10:41:50,756 - INFO - 准备合并 1 个场景文件，总时长约 4.12秒
2025-07-29 10:41:50,756 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1618.mp4'

2025-07-29 10:41:50,757 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_cm5t785\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_cm5t785\temp_combined.mp4
2025-07-29 10:41:50,900 - INFO - 合并后的视频时长: 4.14秒，目标音频时长: 3.19秒
2025-07-29 10:41:50,900 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_cm5t785\temp_combined.mp4 -ss 0 -to 3.191 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-07-29 10:41:51,208 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:51,208 - INFO - 目标音频时长: 3.19秒
2025-07-29 10:41:51,208 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:41:51,208 - INFO - 时长差异: 0.03秒 (1.00%)
2025-07-29 10:41:51,208 - INFO - ==========================================
2025-07-29 10:41:51,208 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:51,208 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-07-29 10:41:51,209 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_cm5t785
2025-07-29 10:41:51,272 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:51,272 - INFO -   - 音频时长: 3.19秒
2025-07-29 10:41:51,272 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:41:51,273 - INFO -   - 时长差异: 0.03秒 (1.00%)
2025-07-29 10:41:51,273 - INFO - 
----- 处理字幕 #66 的方案 #2 -----
2025-07-29 10:41:51,273 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-07-29 10:41:51,273 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuc34tup1
2025-07-29 10:41:51,274 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1622.mp4 (确认存在: True)
2025-07-29 10:41:51,274 - INFO - 添加场景ID=1622，时长=4.08秒，累计时长=4.08秒
2025-07-29 10:41:51,274 - INFO - 准备合并 1 个场景文件，总时长约 4.08秒
2025-07-29 10:41:51,274 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1622.mp4'

2025-07-29 10:41:51,274 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuc34tup1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuc34tup1\temp_combined.mp4
2025-07-29 10:41:51,415 - INFO - 合并后的视频时长: 4.10秒，目标音频时长: 3.19秒
2025-07-29 10:41:51,415 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuc34tup1\temp_combined.mp4 -ss 0 -to 3.191 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-07-29 10:41:51,732 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:51,732 - INFO - 目标音频时长: 3.19秒
2025-07-29 10:41:51,732 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:41:51,732 - INFO - 时长差异: 0.03秒 (1.00%)
2025-07-29 10:41:51,732 - INFO - ==========================================
2025-07-29 10:41:51,732 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:51,732 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-07-29 10:41:51,733 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuc34tup1
2025-07-29 10:41:51,787 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:51,787 - INFO -   - 音频时长: 3.19秒
2025-07-29 10:41:51,788 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:41:51,788 - INFO -   - 时长差异: 0.03秒 (1.00%)
2025-07-29 10:41:51,788 - INFO - 
----- 处理字幕 #66 的方案 #3 -----
2025-07-29 10:41:51,788 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-07-29 10:41:51,788 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl7h1pxap
2025-07-29 10:41:51,789 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1618.mp4 (确认存在: True)
2025-07-29 10:41:51,789 - INFO - 添加场景ID=1618，时长=4.12秒，累计时长=4.12秒
2025-07-29 10:41:51,789 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1623.mp4 (确认存在: True)
2025-07-29 10:41:51,789 - INFO - 添加场景ID=1623，时长=9.68秒，累计时长=13.80秒
2025-07-29 10:41:51,789 - INFO - 场景总时长(13.80秒)已达到音频时长(3.19秒)的1.5倍，停止添加场景
2025-07-29 10:41:51,789 - INFO - 准备合并 2 个场景文件，总时长约 13.80秒
2025-07-29 10:41:51,789 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1618.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1623.mp4'

2025-07-29 10:41:51,790 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpl7h1pxap\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpl7h1pxap\temp_combined.mp4
2025-07-29 10:41:51,972 - INFO - 合并后的视频时长: 13.85秒，目标音频时长: 3.19秒
2025-07-29 10:41:51,972 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpl7h1pxap\temp_combined.mp4 -ss 0 -to 3.191 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-07-29 10:41:52,251 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:52,251 - INFO - 目标音频时长: 3.19秒
2025-07-29 10:41:52,251 - INFO - 实际视频时长: 3.22秒
2025-07-29 10:41:52,251 - INFO - 时长差异: 0.03秒 (1.00%)
2025-07-29 10:41:52,251 - INFO - ==========================================
2025-07-29 10:41:52,251 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:52,251 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-07-29 10:41:52,252 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl7h1pxap
2025-07-29 10:41:52,315 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:52,315 - INFO -   - 音频时长: 3.19秒
2025-07-29 10:41:52,315 - INFO -   - 视频时长: 3.22秒
2025-07-29 10:41:52,315 - INFO -   - 时长差异: 0.03秒 (1.00%)
2025-07-29 10:41:52,315 - INFO - 
字幕 #66 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:52,315 - INFO - 生成的视频文件:
2025-07-29 10:41:52,315 - INFO -   1. F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-07-29 10:41:52,315 - INFO -   2. F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-07-29 10:41:52,315 - INFO -   3. F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-07-29 10:41:52,315 - INFO - ========== 字幕 #66 处理结束 ==========

