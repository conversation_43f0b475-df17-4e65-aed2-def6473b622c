2025-07-29 10:40:55,452 - INFO - ========== 字幕 #31 处理开始 ==========
2025-07-29 10:40:55,452 - INFO - 字幕内容: 九姨娘却背地里通知刘青书，让他先下手为强。
2025-07-29 10:40:55,452 - INFO - 字幕序号: [172, 176]
2025-07-29 10:40:55,453 - INFO - 音频文件详情:
2025-07-29 10:40:55,453 - INFO -   - 路径: output\31.wav
2025-07-29 10:40:55,453 - INFO -   - 时长: 3.72秒
2025-07-29 10:40:55,453 - INFO -   - 验证音频时长: 3.72秒
2025-07-29 10:40:55,453 - INFO - 字幕时间戳信息:
2025-07-29 10:40:55,453 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:55,453 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:55,453 - INFO -   - 根据生成的音频时长(3.72秒)已调整字幕时间戳
2025-07-29 10:40:55,453 - INFO - ========== 新模式：为字幕 #31 生成4套场景方案 ==========
2025-07-29 10:40:55,453 - INFO - 字幕序号列表: [172, 176]
2025-07-29 10:40:55,453 - INFO - 
--- 生成方案 #1：基于字幕序号 #172 ---
2025-07-29 10:40:55,453 - INFO - 开始为单个字幕序号 #172 匹配场景，目标时长: 3.72秒
2025-07-29 10:40:55,453 - INFO - 开始查找字幕序号 [172] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:55,453 - INFO - 找到related_overlap场景: scene_id=248, 字幕#172
2025-07-29 10:40:55,454 - INFO - 字幕 #172 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:55,454 - INFO - 字幕序号 #172 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:55,455 - INFO - 选择第一个overlap场景作为起点: scene_id=248
2025-07-29 10:40:55,455 - INFO - 添加起点场景: scene_id=248, 时长=3.80秒, 累计时长=3.80秒
2025-07-29 10:40:55,455 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:40:55,455 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:40:55,455 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:40:55,455 - INFO - 
--- 生成方案 #2：基于字幕序号 #176 ---
2025-07-29 10:40:55,455 - INFO - 开始为单个字幕序号 #176 匹配场景，目标时长: 3.72秒
2025-07-29 10:40:55,455 - INFO - 开始查找字幕序号 [176] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:55,455 - INFO - 找到related_overlap场景: scene_id=251, 字幕#176
2025-07-29 10:40:55,455 - INFO - 找到related_between场景: scene_id=252, 字幕#176
2025-07-29 10:40:55,455 - INFO - 找到related_between场景: scene_id=253, 字幕#176
2025-07-29 10:40:55,456 - INFO - 字幕 #176 找到 1 个overlap场景, 2 个between场景
2025-07-29 10:40:55,456 - INFO - 字幕序号 #176 找到 1 个可用overlap场景, 2 个可用between场景
2025-07-29 10:40:55,456 - INFO - 选择第一个overlap场景作为起点: scene_id=251
2025-07-29 10:40:55,456 - INFO - 添加起点场景: scene_id=251, 时长=0.72秒, 累计时长=0.72秒
2025-07-29 10:40:55,456 - INFO - 起点场景时长不足，需要延伸填充 3.00秒
2025-07-29 10:40:55,456 - INFO - 起点场景在原始列表中的索引: 250
2025-07-29 10:40:55,456 - INFO - 延伸添加场景: scene_id=252 (完整时长 1.80秒)
2025-07-29 10:40:55,456 - INFO - 累计时长: 2.52秒
2025-07-29 10:40:55,456 - INFO - 延伸添加场景: scene_id=253 (裁剪至 1.20秒)
2025-07-29 10:40:55,456 - INFO - 累计时长: 3.72秒
2025-07-29 10:40:55,456 - INFO - 字幕序号 #176 场景匹配完成，共选择 3 个场景，总时长: 3.72秒
2025-07-29 10:40:55,456 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:55,456 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:55,456 - INFO - ========== 当前模式：为字幕 #31 生成 1 套场景方案 ==========
2025-07-29 10:40:55,456 - INFO - 开始查找字幕序号 [172, 176] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:55,456 - INFO - 找到related_overlap场景: scene_id=248, 字幕#172
2025-07-29 10:40:55,456 - INFO - 找到related_overlap场景: scene_id=251, 字幕#176
2025-07-29 10:40:55,457 - INFO - 找到related_between场景: scene_id=252, 字幕#176
2025-07-29 10:40:55,457 - INFO - 找到related_between场景: scene_id=253, 字幕#176
2025-07-29 10:40:55,457 - INFO - 字幕 #172 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:55,457 - INFO - 字幕 #176 找到 1 个overlap场景, 2 个between场景
2025-07-29 10:40:55,457 - INFO - 共收集 2 个未使用的overlap场景和 2 个未使用的between场景
2025-07-29 10:40:55,457 - INFO - 开始生成方案 #1
2025-07-29 10:40:55,457 - INFO - 方案 #1: 为字幕#172选择初始化overlap场景id=248
2025-07-29 10:40:55,457 - INFO - 方案 #1: 为字幕#176选择初始化overlap场景id=251
2025-07-29 10:40:55,457 - INFO - 方案 #1: 初始选择后，当前总时长=4.52秒
2025-07-29 10:40:55,457 - INFO - 方案 #1: 额外between选择后，当前总时长=4.52秒
2025-07-29 10:40:55,457 - INFO - 方案 #1: 场景总时长(4.52秒)大于音频时长(3.72秒)，需要裁剪
2025-07-29 10:40:55,457 - INFO - 调整前总时长: 4.52秒, 目标时长: 3.72秒
2025-07-29 10:40:55,457 - INFO - 需要裁剪 0.80秒
2025-07-29 10:40:55,457 - INFO - 裁剪最长场景ID=248：从3.80秒裁剪至3.00秒
2025-07-29 10:40:55,457 - INFO - 调整后总时长: 3.72秒，与目标时长差异: 0.00秒
2025-07-29 10:40:55,457 - INFO - 方案 #1 调整/填充后最终总时长: 3.72秒
2025-07-29 10:40:55,457 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:55,457 - INFO - ========== 当前模式：字幕 #31 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:55,457 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:55,457 - INFO - ========== 新模式：字幕 #31 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:55,457 - INFO - 
----- 处理字幕 #31 的方案 #1 -----
2025-07-29 10:40:55,457 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-29 10:40:55,457 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppitfh6f0
2025-07-29 10:40:55,458 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\248.mp4 (确认存在: True)
2025-07-29 10:40:55,458 - INFO - 添加场景ID=248，时长=3.80秒，累计时长=3.80秒
2025-07-29 10:40:55,458 - INFO - 准备合并 1 个场景文件，总时长约 3.80秒
2025-07-29 10:40:55,458 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/248.mp4'

2025-07-29 10:40:55,458 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppitfh6f0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppitfh6f0\temp_combined.mp4
2025-07-29 10:40:55,573 - INFO - 合并后的视频时长: 3.82秒，目标音频时长: 3.72秒
2025-07-29 10:40:55,573 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppitfh6f0\temp_combined.mp4 -ss 0 -to 3.724 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-29 10:40:55,845 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:55,845 - INFO - 目标音频时长: 3.72秒
2025-07-29 10:40:55,845 - INFO - 实际视频时长: 3.78秒
2025-07-29 10:40:55,845 - INFO - 时长差异: 0.06秒 (1.58%)
2025-07-29 10:40:55,845 - INFO - ==========================================
2025-07-29 10:40:55,845 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:55,845 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-29 10:40:55,845 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppitfh6f0
2025-07-29 10:40:55,890 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:55,890 - INFO -   - 音频时长: 3.72秒
2025-07-29 10:40:55,890 - INFO -   - 视频时长: 3.78秒
2025-07-29 10:40:55,890 - INFO -   - 时长差异: 0.06秒 (1.58%)
2025-07-29 10:40:55,890 - INFO - 
----- 处理字幕 #31 的方案 #2 -----
2025-07-29 10:40:55,890 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-29 10:40:55,891 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpell9rhar
2025-07-29 10:40:55,891 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\251.mp4 (确认存在: True)
2025-07-29 10:40:55,891 - INFO - 添加场景ID=251，时长=0.72秒，累计时长=0.72秒
2025-07-29 10:40:55,891 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\252.mp4 (确认存在: True)
2025-07-29 10:40:55,891 - INFO - 添加场景ID=252，时长=1.80秒，累计时长=2.52秒
2025-07-29 10:40:55,891 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\253.mp4 (确认存在: True)
2025-07-29 10:40:55,891 - INFO - 添加场景ID=253，时长=1.96秒，累计时长=4.48秒
2025-07-29 10:40:55,891 - INFO - 准备合并 3 个场景文件，总时长约 4.48秒
2025-07-29 10:40:55,892 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/251.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/252.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/253.mp4'

2025-07-29 10:40:55,892 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpell9rhar\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpell9rhar\temp_combined.mp4
2025-07-29 10:40:56,045 - INFO - 合并后的视频时长: 4.55秒，目标音频时长: 3.72秒
2025-07-29 10:40:56,045 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpell9rhar\temp_combined.mp4 -ss 0 -to 3.724 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-29 10:40:56,315 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:56,315 - INFO - 目标音频时长: 3.72秒
2025-07-29 10:40:56,315 - INFO - 实际视频时长: 3.78秒
2025-07-29 10:40:56,315 - INFO - 时长差异: 0.06秒 (1.58%)
2025-07-29 10:40:56,315 - INFO - ==========================================
2025-07-29 10:40:56,315 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:56,315 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-29 10:40:56,316 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpell9rhar
2025-07-29 10:40:56,359 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:56,359 - INFO -   - 音频时长: 3.72秒
2025-07-29 10:40:56,359 - INFO -   - 视频时长: 3.78秒
2025-07-29 10:40:56,359 - INFO -   - 时长差异: 0.06秒 (1.58%)
2025-07-29 10:40:56,359 - INFO - 
----- 处理字幕 #31 的方案 #3 -----
2025-07-29 10:40:56,360 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-29 10:40:56,360 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6dzoagni
2025-07-29 10:40:56,360 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\248.mp4 (确认存在: True)
2025-07-29 10:40:56,360 - INFO - 添加场景ID=248，时长=3.80秒，累计时长=3.80秒
2025-07-29 10:40:56,360 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\251.mp4 (确认存在: True)
2025-07-29 10:40:56,360 - INFO - 添加场景ID=251，时长=0.72秒，累计时长=4.52秒
2025-07-29 10:40:56,361 - INFO - 准备合并 2 个场景文件，总时长约 4.52秒
2025-07-29 10:40:56,361 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/248.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/251.mp4'

2025-07-29 10:40:56,361 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6dzoagni\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6dzoagni\temp_combined.mp4
2025-07-29 10:40:56,473 - INFO - 合并后的视频时长: 4.57秒，目标音频时长: 3.72秒
2025-07-29 10:40:56,473 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6dzoagni\temp_combined.mp4 -ss 0 -to 3.724 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-29 10:40:56,736 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:56,736 - INFO - 目标音频时长: 3.72秒
2025-07-29 10:40:56,736 - INFO - 实际视频时长: 3.78秒
2025-07-29 10:40:56,736 - INFO - 时长差异: 0.06秒 (1.58%)
2025-07-29 10:40:56,736 - INFO - ==========================================
2025-07-29 10:40:56,736 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:56,736 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-29 10:40:56,736 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6dzoagni
2025-07-29 10:40:56,780 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:56,780 - INFO -   - 音频时长: 3.72秒
2025-07-29 10:40:56,780 - INFO -   - 视频时长: 3.78秒
2025-07-29 10:40:56,780 - INFO -   - 时长差异: 0.06秒 (1.58%)
2025-07-29 10:40:56,780 - INFO - 
字幕 #31 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:56,780 - INFO - 生成的视频文件:
2025-07-29 10:40:56,780 - INFO -   1. F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-29 10:40:56,780 - INFO -   2. F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-29 10:40:56,780 - INFO -   3. F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-29 10:40:56,780 - INFO - ========== 字幕 #31 处理结束 ==========

