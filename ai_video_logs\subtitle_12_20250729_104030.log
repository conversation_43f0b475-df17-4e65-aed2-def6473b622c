2025-07-29 10:40:30,902 - INFO - ========== 字幕 #12 处理开始 ==========
2025-07-29 10:40:30,902 - INFO - 字幕内容: 丫鬟担心摄政王凶残，她却因哥哥要去而坚持陪同。
2025-07-29 10:40:30,902 - INFO - 字幕序号: [50, 55]
2025-07-29 10:40:30,902 - INFO - 音频文件详情:
2025-07-29 10:40:30,902 - INFO -   - 路径: output\12.wav
2025-07-29 10:40:30,902 - INFO -   - 时长: 3.84秒
2025-07-29 10:40:30,903 - INFO -   - 验证音频时长: 3.84秒
2025-07-29 10:40:30,903 - INFO - 字幕时间戳信息:
2025-07-29 10:40:30,903 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:30,903 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:30,903 - INFO -   - 根据生成的音频时长(3.84秒)已调整字幕时间戳
2025-07-29 10:40:30,903 - INFO - ========== 新模式：为字幕 #12 生成4套场景方案 ==========
2025-07-29 10:40:30,903 - INFO - 字幕序号列表: [50, 55]
2025-07-29 10:40:30,903 - INFO - 
--- 生成方案 #1：基于字幕序号 #50 ---
2025-07-29 10:40:30,903 - INFO - 开始为单个字幕序号 #50 匹配场景，目标时长: 3.84秒
2025-07-29 10:40:30,903 - INFO - 开始查找字幕序号 [50] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:30,904 - INFO - 找到related_overlap场景: scene_id=65, 字幕#50
2025-07-29 10:40:30,905 - INFO - 字幕 #50 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:30,905 - INFO - 字幕序号 #50 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:30,905 - INFO - 选择第一个overlap场景作为起点: scene_id=65
2025-07-29 10:40:30,905 - INFO - 添加起点场景: scene_id=65, 时长=1.12秒, 累计时长=1.12秒
2025-07-29 10:40:30,905 - INFO - 起点场景时长不足，需要延伸填充 2.72秒
2025-07-29 10:40:30,905 - INFO - 起点场景在原始列表中的索引: 64
2025-07-29 10:40:30,905 - INFO - 延伸添加场景: scene_id=66 (完整时长 0.92秒)
2025-07-29 10:40:30,905 - INFO - 累计时长: 2.04秒
2025-07-29 10:40:30,905 - INFO - 延伸添加场景: scene_id=67 (裁剪至 1.80秒)
2025-07-29 10:40:30,905 - INFO - 累计时长: 3.84秒
2025-07-29 10:40:30,905 - INFO - 字幕序号 #50 场景匹配完成，共选择 3 个场景，总时长: 3.84秒
2025-07-29 10:40:30,905 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:40:30,905 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:40:30,905 - INFO - 
--- 生成方案 #2：基于字幕序号 #55 ---
2025-07-29 10:40:30,905 - INFO - 开始为单个字幕序号 #55 匹配场景，目标时长: 3.84秒
2025-07-29 10:40:30,905 - INFO - 开始查找字幕序号 [55] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:30,905 - INFO - 找到related_overlap场景: scene_id=70, 字幕#55
2025-07-29 10:40:30,905 - INFO - 找到related_overlap场景: scene_id=71, 字幕#55
2025-07-29 10:40:30,906 - INFO - 字幕 #55 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:30,906 - INFO - 字幕序号 #55 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:30,906 - INFO - 选择第一个overlap场景作为起点: scene_id=70
2025-07-29 10:40:30,906 - INFO - 添加起点场景: scene_id=70, 时长=2.92秒, 累计时长=2.92秒
2025-07-29 10:40:30,906 - INFO - 起点场景时长不足，需要延伸填充 0.92秒
2025-07-29 10:40:30,906 - INFO - 起点场景在原始列表中的索引: 69
2025-07-29 10:40:30,906 - INFO - 延伸添加场景: scene_id=71 (裁剪至 0.92秒)
2025-07-29 10:40:30,906 - INFO - 累计时长: 3.84秒
2025-07-29 10:40:30,906 - INFO - 字幕序号 #55 场景匹配完成，共选择 2 个场景，总时长: 3.84秒
2025-07-29 10:40:30,906 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:30,906 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:30,906 - INFO - ========== 当前模式：为字幕 #12 生成 1 套场景方案 ==========
2025-07-29 10:40:30,906 - INFO - 开始查找字幕序号 [50, 55] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:30,906 - INFO - 找到related_overlap场景: scene_id=65, 字幕#50
2025-07-29 10:40:30,907 - INFO - 找到related_overlap场景: scene_id=70, 字幕#55
2025-07-29 10:40:30,907 - INFO - 找到related_overlap场景: scene_id=71, 字幕#55
2025-07-29 10:40:30,908 - INFO - 字幕 #50 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:30,908 - INFO - 字幕 #55 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:30,908 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:40:30,908 - INFO - 开始生成方案 #1
2025-07-29 10:40:30,908 - INFO - 方案 #1: 为字幕#50选择初始化overlap场景id=65
2025-07-29 10:40:30,908 - INFO - 方案 #1: 为字幕#55选择初始化overlap场景id=70
2025-07-29 10:40:30,908 - INFO - 方案 #1: 初始选择后，当前总时长=4.04秒
2025-07-29 10:40:30,908 - INFO - 方案 #1: 额外between选择后，当前总时长=4.04秒
2025-07-29 10:40:30,908 - INFO - 方案 #1: 场景总时长(4.04秒)大于音频时长(3.84秒)，需要裁剪
2025-07-29 10:40:30,908 - INFO - 调整前总时长: 4.04秒, 目标时长: 3.84秒
2025-07-29 10:40:30,908 - INFO - 需要裁剪 0.20秒
2025-07-29 10:40:30,908 - INFO - 裁剪最长场景ID=70：从2.92秒裁剪至2.72秒
2025-07-29 10:40:30,908 - INFO - 调整后总时长: 3.84秒，与目标时长差异: 0.00秒
2025-07-29 10:40:30,908 - INFO - 方案 #1 调整/填充后最终总时长: 3.84秒
2025-07-29 10:40:30,908 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:30,908 - INFO - ========== 当前模式：字幕 #12 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:30,908 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:30,908 - INFO - ========== 新模式：字幕 #12 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:30,908 - INFO - 
----- 处理字幕 #12 的方案 #1 -----
2025-07-29 10:40:30,908 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-29 10:40:30,908 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxueymxxf
2025-07-29 10:40:30,909 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\65.mp4 (确认存在: True)
2025-07-29 10:40:30,909 - INFO - 添加场景ID=65，时长=1.12秒，累计时长=1.12秒
2025-07-29 10:40:30,909 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\66.mp4 (确认存在: True)
2025-07-29 10:40:30,909 - INFO - 添加场景ID=66，时长=0.92秒，累计时长=2.04秒
2025-07-29 10:40:30,909 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\67.mp4 (确认存在: True)
2025-07-29 10:40:30,909 - INFO - 添加场景ID=67，时长=2.04秒，累计时长=4.08秒
2025-07-29 10:40:30,909 - INFO - 准备合并 3 个场景文件，总时长约 4.08秒
2025-07-29 10:40:30,909 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/65.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/66.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/67.mp4'

2025-07-29 10:40:30,909 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxueymxxf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxueymxxf\temp_combined.mp4
2025-07-29 10:40:31,059 - INFO - 合并后的视频时长: 4.15秒，目标音频时长: 3.84秒
2025-07-29 10:40:31,059 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxueymxxf\temp_combined.mp4 -ss 0 -to 3.84 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-29 10:40:31,337 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:31,337 - INFO - 目标音频时长: 3.84秒
2025-07-29 10:40:31,337 - INFO - 实际视频时长: 3.86秒
2025-07-29 10:40:31,339 - INFO - 时长差异: 0.02秒 (0.60%)
2025-07-29 10:40:31,339 - INFO - ==========================================
2025-07-29 10:40:31,339 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:31,339 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-29 10:40:31,339 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxueymxxf
2025-07-29 10:40:31,383 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:31,385 - INFO -   - 音频时长: 3.84秒
2025-07-29 10:40:31,385 - INFO -   - 视频时长: 3.86秒
2025-07-29 10:40:31,385 - INFO -   - 时长差异: 0.02秒 (0.60%)
2025-07-29 10:40:31,385 - INFO - 
----- 处理字幕 #12 的方案 #2 -----
2025-07-29 10:40:31,385 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-29 10:40:31,385 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpez3mye7m
2025-07-29 10:40:31,385 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\70.mp4 (确认存在: True)
2025-07-29 10:40:31,385 - INFO - 添加场景ID=70，时长=2.92秒，累计时长=2.92秒
2025-07-29 10:40:31,386 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\71.mp4 (确认存在: True)
2025-07-29 10:40:31,386 - INFO - 添加场景ID=71，时长=8.12秒，累计时长=11.04秒
2025-07-29 10:40:31,386 - INFO - 场景总时长(11.04秒)已达到音频时长(3.84秒)的1.5倍，停止添加场景
2025-07-29 10:40:31,386 - INFO - 准备合并 2 个场景文件，总时长约 11.04秒
2025-07-29 10:40:31,386 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/70.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/71.mp4'

2025-07-29 10:40:31,386 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpez3mye7m\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpez3mye7m\temp_combined.mp4
2025-07-29 10:40:31,515 - INFO - 合并后的视频时长: 11.09秒，目标音频时长: 3.84秒
2025-07-29 10:40:31,515 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpez3mye7m\temp_combined.mp4 -ss 0 -to 3.84 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-29 10:40:31,784 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:31,784 - INFO - 目标音频时长: 3.84秒
2025-07-29 10:40:31,784 - INFO - 实际视频时长: 3.86秒
2025-07-29 10:40:31,784 - INFO - 时长差异: 0.02秒 (0.60%)
2025-07-29 10:40:31,784 - INFO - ==========================================
2025-07-29 10:40:31,784 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:31,784 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-29 10:40:31,784 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpez3mye7m
2025-07-29 10:40:31,825 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:31,825 - INFO -   - 音频时长: 3.84秒
2025-07-29 10:40:31,825 - INFO -   - 视频时长: 3.86秒
2025-07-29 10:40:31,825 - INFO -   - 时长差异: 0.02秒 (0.60%)
2025-07-29 10:40:31,825 - INFO - 
----- 处理字幕 #12 的方案 #3 -----
2025-07-29 10:40:31,825 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-07-29 10:40:31,826 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuc_ywn4l
2025-07-29 10:40:31,826 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\65.mp4 (确认存在: True)
2025-07-29 10:40:31,826 - INFO - 添加场景ID=65，时长=1.12秒，累计时长=1.12秒
2025-07-29 10:40:31,826 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\70.mp4 (确认存在: True)
2025-07-29 10:40:31,826 - INFO - 添加场景ID=70，时长=2.92秒，累计时长=4.04秒
2025-07-29 10:40:31,827 - INFO - 准备合并 2 个场景文件，总时长约 4.04秒
2025-07-29 10:40:31,827 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/65.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/70.mp4'

2025-07-29 10:40:31,827 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuc_ywn4l\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuc_ywn4l\temp_combined.mp4
2025-07-29 10:40:31,936 - INFO - 合并后的视频时长: 4.09秒，目标音频时长: 3.84秒
2025-07-29 10:40:31,936 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuc_ywn4l\temp_combined.mp4 -ss 0 -to 3.84 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-07-29 10:40:32,189 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:32,189 - INFO - 目标音频时长: 3.84秒
2025-07-29 10:40:32,189 - INFO - 实际视频时长: 3.86秒
2025-07-29 10:40:32,189 - INFO - 时长差异: 0.02秒 (0.60%)
2025-07-29 10:40:32,189 - INFO - ==========================================
2025-07-29 10:40:32,189 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:32,189 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-07-29 10:40:32,190 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuc_ywn4l
2025-07-29 10:40:32,231 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:32,231 - INFO -   - 音频时长: 3.84秒
2025-07-29 10:40:32,231 - INFO -   - 视频时长: 3.86秒
2025-07-29 10:40:32,231 - INFO -   - 时长差异: 0.02秒 (0.60%)
2025-07-29 10:40:32,231 - INFO - 
字幕 #12 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:32,231 - INFO - 生成的视频文件:
2025-07-29 10:40:32,231 - INFO -   1. F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-29 10:40:32,231 - INFO -   2. F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-29 10:40:32,231 - INFO -   3. F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-07-29 10:40:32,231 - INFO - ========== 字幕 #12 处理结束 ==========

