2025-07-29 10:40:42,870 - INFO - ========== 字幕 #21 处理开始 ==========
2025-07-29 10:40:42,870 - INFO - 字幕内容: 她在寒风中苦等，丫鬟劝她先回，她却不肯。
2025-07-29 10:40:42,870 - INFO - 字幕序号: [106, 113]
2025-07-29 10:40:42,870 - INFO - 音频文件详情:
2025-07-29 10:40:42,870 - INFO -   - 路径: output\21.wav
2025-07-29 10:40:42,871 - INFO -   - 时长: 3.72秒
2025-07-29 10:40:42,871 - INFO -   - 验证音频时长: 3.72秒
2025-07-29 10:40:42,871 - INFO - 字幕时间戳信息:
2025-07-29 10:40:42,871 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:42,871 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:42,871 - INFO -   - 根据生成的音频时长(3.72秒)已调整字幕时间戳
2025-07-29 10:40:42,871 - INFO - ========== 新模式：为字幕 #21 生成4套场景方案 ==========
2025-07-29 10:40:42,871 - INFO - 字幕序号列表: [106, 113]
2025-07-29 10:40:42,871 - INFO - 
--- 生成方案 #1：基于字幕序号 #106 ---
2025-07-29 10:40:42,871 - INFO - 开始为单个字幕序号 #106 匹配场景，目标时长: 3.72秒
2025-07-29 10:40:42,871 - INFO - 开始查找字幕序号 [106] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:42,871 - INFO - 找到related_overlap场景: scene_id=157, 字幕#106
2025-07-29 10:40:42,871 - INFO - 找到related_overlap场景: scene_id=158, 字幕#106
2025-07-29 10:40:42,872 - INFO - 找到related_between场景: scene_id=156, 字幕#106
2025-07-29 10:40:42,873 - INFO - 字幕 #106 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:42,873 - INFO - 字幕序号 #106 找到 2 个可用overlap场景, 1 个可用between场景
2025-07-29 10:40:42,873 - INFO - 选择第一个overlap场景作为起点: scene_id=157
2025-07-29 10:40:42,873 - INFO - 添加起点场景: scene_id=157, 时长=2.36秒, 累计时长=2.36秒
2025-07-29 10:40:42,873 - INFO - 起点场景时长不足，需要延伸填充 1.36秒
2025-07-29 10:40:42,873 - INFO - 起点场景在原始列表中的索引: 156
2025-07-29 10:40:42,873 - INFO - 延伸添加场景: scene_id=158 (裁剪至 1.36秒)
2025-07-29 10:40:42,873 - INFO - 累计时长: 3.72秒
2025-07-29 10:40:42,873 - INFO - 字幕序号 #106 场景匹配完成，共选择 2 个场景，总时长: 3.72秒
2025-07-29 10:40:42,873 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:42,873 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:42,873 - INFO - 
--- 生成方案 #2：基于字幕序号 #113 ---
2025-07-29 10:40:42,873 - INFO - 开始为单个字幕序号 #113 匹配场景，目标时长: 3.72秒
2025-07-29 10:40:42,873 - INFO - 开始查找字幕序号 [113] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:42,874 - INFO - 字幕 #113 找到 0 个overlap场景, 0 个between场景
2025-07-29 10:40:42,874 - WARNING - 字幕 #113 没有找到任何匹配场景!
2025-07-29 10:40:42,874 - INFO - 字幕序号 #113 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:42,874 - ERROR - 字幕序号 #113 没有找到任何可用的匹配场景
2025-07-29 10:40:42,874 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 10:40:42,874 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 10:40:42,874 - INFO - ========== 当前模式：为字幕 #21 生成 1 套场景方案 ==========
2025-07-29 10:40:42,874 - INFO - 开始查找字幕序号 [106, 113] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:42,874 - INFO - 找到related_overlap场景: scene_id=157, 字幕#106
2025-07-29 10:40:42,874 - INFO - 找到related_overlap场景: scene_id=158, 字幕#106
2025-07-29 10:40:42,874 - INFO - 找到related_between场景: scene_id=156, 字幕#106
2025-07-29 10:40:42,875 - INFO - 字幕 #106 找到 2 个overlap场景, 1 个between场景
2025-07-29 10:40:42,875 - INFO - 字幕 #113 找到 0 个overlap场景, 0 个between场景
2025-07-29 10:40:42,875 - WARNING - 字幕 #113 没有找到任何匹配场景!
2025-07-29 10:40:42,875 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 10:40:42,875 - INFO - 开始生成方案 #1
2025-07-29 10:40:42,875 - INFO - 方案 #1: 为字幕#106选择初始化overlap场景id=158
2025-07-29 10:40:42,875 - INFO - 方案 #1: 初始选择后，当前总时长=2.48秒
2025-07-29 10:40:42,875 - INFO - 方案 #1: 额外添加overlap场景id=157, 当前总时长=4.84秒
2025-07-29 10:40:42,875 - INFO - 方案 #1: 额外between选择后，当前总时长=4.84秒
2025-07-29 10:40:42,875 - INFO - 方案 #1: 场景总时长(4.84秒)大于音频时长(3.72秒)，需要裁剪
2025-07-29 10:40:42,875 - INFO - 调整前总时长: 4.84秒, 目标时长: 3.72秒
2025-07-29 10:40:42,875 - INFO - 需要裁剪 1.12秒
2025-07-29 10:40:42,875 - INFO - 裁剪最长场景ID=158：从2.48秒裁剪至1.36秒
2025-07-29 10:40:42,875 - INFO - 调整后总时长: 3.72秒，与目标时长差异: 0.00秒
2025-07-29 10:40:42,875 - INFO - 方案 #1 调整/填充后最终总时长: 3.72秒
2025-07-29 10:40:42,875 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:42,875 - INFO - ========== 当前模式：字幕 #21 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:42,875 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 10:40:42,875 - INFO - ========== 新模式：字幕 #21 共生成 2 套有效场景方案 ==========
2025-07-29 10:40:42,875 - INFO - 
----- 处理字幕 #21 的方案 #1 -----
2025-07-29 10:40:42,875 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-29 10:40:42,876 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd250l83h
2025-07-29 10:40:42,876 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\157.mp4 (确认存在: True)
2025-07-29 10:40:42,876 - INFO - 添加场景ID=157，时长=2.36秒，累计时长=2.36秒
2025-07-29 10:40:42,876 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\158.mp4 (确认存在: True)
2025-07-29 10:40:42,876 - INFO - 添加场景ID=158，时长=2.48秒，累计时长=4.84秒
2025-07-29 10:40:42,876 - INFO - 准备合并 2 个场景文件，总时长约 4.84秒
2025-07-29 10:40:42,876 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/157.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/158.mp4'

2025-07-29 10:40:42,877 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpd250l83h\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpd250l83h\temp_combined.mp4
2025-07-29 10:40:42,996 - INFO - 合并后的视频时长: 4.88秒，目标音频时长: 3.72秒
2025-07-29 10:40:42,996 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpd250l83h\temp_combined.mp4 -ss 0 -to 3.72 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-29 10:40:43,244 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:43,244 - INFO - 目标音频时长: 3.72秒
2025-07-29 10:40:43,244 - INFO - 实际视频时长: 3.74秒
2025-07-29 10:40:43,244 - INFO - 时长差异: 0.02秒 (0.62%)
2025-07-29 10:40:43,244 - INFO - ==========================================
2025-07-29 10:40:43,244 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:43,245 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-29 10:40:43,245 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd250l83h
2025-07-29 10:40:43,292 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:43,292 - INFO -   - 音频时长: 3.72秒
2025-07-29 10:40:43,292 - INFO -   - 视频时长: 3.74秒
2025-07-29 10:40:43,292 - INFO -   - 时长差异: 0.02秒 (0.62%)
2025-07-29 10:40:43,292 - INFO - 
----- 处理字幕 #21 的方案 #2 -----
2025-07-29 10:40:43,292 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-07-29 10:40:43,292 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppttzel_r
2025-07-29 10:40:43,292 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\158.mp4 (确认存在: True)
2025-07-29 10:40:43,292 - INFO - 添加场景ID=158，时长=2.48秒，累计时长=2.48秒
2025-07-29 10:40:43,293 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\157.mp4 (确认存在: True)
2025-07-29 10:40:43,293 - INFO - 添加场景ID=157，时长=2.36秒，累计时长=4.84秒
2025-07-29 10:40:43,293 - INFO - 准备合并 2 个场景文件，总时长约 4.84秒
2025-07-29 10:40:43,293 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/158.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/157.mp4'

2025-07-29 10:40:43,293 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppttzel_r\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppttzel_r\temp_combined.mp4
2025-07-29 10:40:43,419 - INFO - 合并后的视频时长: 4.89秒，目标音频时长: 3.72秒
2025-07-29 10:40:43,419 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppttzel_r\temp_combined.mp4 -ss 0 -to 3.72 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-07-29 10:40:43,675 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:43,675 - INFO - 目标音频时长: 3.72秒
2025-07-29 10:40:43,675 - INFO - 实际视频时长: 3.74秒
2025-07-29 10:40:43,675 - INFO - 时长差异: 0.02秒 (0.62%)
2025-07-29 10:40:43,675 - INFO - ==========================================
2025-07-29 10:40:43,675 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:43,675 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-07-29 10:40:43,676 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppttzel_r
2025-07-29 10:40:43,716 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:43,716 - INFO -   - 音频时长: 3.72秒
2025-07-29 10:40:43,716 - INFO -   - 视频时长: 3.74秒
2025-07-29 10:40:43,716 - INFO -   - 时长差异: 0.02秒 (0.62%)
2025-07-29 10:40:43,716 - INFO - 
字幕 #21 处理完成，成功生成 2/2 套方案
2025-07-29 10:40:43,716 - INFO - 生成的视频文件:
2025-07-29 10:40:43,716 - INFO -   1. F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-07-29 10:40:43,716 - INFO -   2. F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-07-29 10:40:43,716 - INFO - ========== 字幕 #21 处理结束 ==========

