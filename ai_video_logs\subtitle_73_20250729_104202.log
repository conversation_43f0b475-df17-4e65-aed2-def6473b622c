2025-07-29 10:42:02,135 - INFO - ========== 字幕 #73 处理开始 ==========
2025-07-29 10:42:02,135 - INFO - 字幕内容: 婚后她才从丫鬟口中得知，一次次救她的不是哥哥，而是摄政王。
2025-07-29 10:42:02,135 - INFO - 字幕序号: [2104, 2119]
2025-07-29 10:42:02,136 - INFO - 音频文件详情:
2025-07-29 10:42:02,136 - INFO -   - 路径: output\73.wav
2025-07-29 10:42:02,136 - INFO -   - 时长: 4.28秒
2025-07-29 10:42:02,137 - INFO -   - 验证音频时长: 4.28秒
2025-07-29 10:42:02,137 - INFO - 字幕时间戳信息:
2025-07-29 10:42:02,137 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:42:02,137 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:42:02,137 - INFO -   - 根据生成的音频时长(4.28秒)已调整字幕时间戳
2025-07-29 10:42:02,137 - INFO - ========== 新模式：为字幕 #73 生成4套场景方案 ==========
2025-07-29 10:42:02,137 - INFO - 字幕序号列表: [2104, 2119]
2025-07-29 10:42:02,137 - INFO - 
--- 生成方案 #1：基于字幕序号 #2104 ---
2025-07-29 10:42:02,137 - INFO - 开始为单个字幕序号 #2104 匹配场景，目标时长: 4.28秒
2025-07-29 10:42:02,137 - INFO - 开始查找字幕序号 [2104] 对应的场景，共有 3443 个场景可选
2025-07-29 10:42:02,138 - INFO - 找到related_overlap场景: scene_id=2613, 字幕#2104
2025-07-29 10:42:02,141 - INFO - 字幕 #2104 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:42:02,141 - INFO - 字幕序号 #2104 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:42:02,141 - INFO - 选择第一个overlap场景作为起点: scene_id=2613
2025-07-29 10:42:02,141 - INFO - 添加起点场景: scene_id=2613, 时长=3.00秒, 累计时长=3.00秒
2025-07-29 10:42:02,141 - INFO - 起点场景时长不足，需要延伸填充 1.28秒
2025-07-29 10:42:02,141 - INFO - 起点场景在原始列表中的索引: 2612
2025-07-29 10:42:02,141 - INFO - 延伸添加场景: scene_id=2614 (裁剪至 1.28秒)
2025-07-29 10:42:02,141 - INFO - 累计时长: 4.28秒
2025-07-29 10:42:02,141 - INFO - 字幕序号 #2104 场景匹配完成，共选择 2 个场景，总时长: 4.28秒
2025-07-29 10:42:02,141 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:42:02,141 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:42:02,141 - INFO - 
--- 生成方案 #2：基于字幕序号 #2119 ---
2025-07-29 10:42:02,141 - INFO - 开始为单个字幕序号 #2119 匹配场景，目标时长: 4.28秒
2025-07-29 10:42:02,141 - INFO - 开始查找字幕序号 [2119] 对应的场景，共有 3443 个场景可选
2025-07-29 10:42:02,142 - INFO - 找到related_overlap场景: scene_id=2619, 字幕#2119
2025-07-29 10:42:02,143 - INFO - 字幕 #2119 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:42:02,143 - INFO - 字幕序号 #2119 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:42:02,143 - INFO - 选择第一个overlap场景作为起点: scene_id=2619
2025-07-29 10:42:02,144 - INFO - 添加起点场景: scene_id=2619, 时长=3.04秒, 累计时长=3.04秒
2025-07-29 10:42:02,144 - INFO - 起点场景时长不足，需要延伸填充 1.24秒
2025-07-29 10:42:02,144 - INFO - 起点场景在原始列表中的索引: 2618
2025-07-29 10:42:02,144 - INFO - 延伸添加场景: scene_id=2620 (裁剪至 1.24秒)
2025-07-29 10:42:02,144 - INFO - 累计时长: 4.28秒
2025-07-29 10:42:02,144 - INFO - 字幕序号 #2119 场景匹配完成，共选择 2 个场景，总时长: 4.28秒
2025-07-29 10:42:02,144 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:42:02,144 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:42:02,144 - INFO - ========== 当前模式：为字幕 #73 生成 1 套场景方案 ==========
2025-07-29 10:42:02,144 - INFO - 开始查找字幕序号 [2104, 2119] 对应的场景，共有 3443 个场景可选
2025-07-29 10:42:02,145 - INFO - 找到related_overlap场景: scene_id=2613, 字幕#2104
2025-07-29 10:42:02,145 - INFO - 找到related_overlap场景: scene_id=2619, 字幕#2119
2025-07-29 10:42:02,146 - INFO - 字幕 #2104 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:42:02,146 - INFO - 字幕 #2119 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:42:02,146 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:42:02,146 - INFO - 开始生成方案 #1
2025-07-29 10:42:02,147 - INFO - 方案 #1: 为字幕#2104选择初始化overlap场景id=2613
2025-07-29 10:42:02,147 - INFO - 方案 #1: 为字幕#2119选择初始化overlap场景id=2619
2025-07-29 10:42:02,147 - INFO - 方案 #1: 初始选择后，当前总时长=6.04秒
2025-07-29 10:42:02,147 - INFO - 方案 #1: 额外between选择后，当前总时长=6.04秒
2025-07-29 10:42:02,147 - INFO - 方案 #1: 场景总时长(6.04秒)大于音频时长(4.28秒)，需要裁剪
2025-07-29 10:42:02,147 - INFO - 调整前总时长: 6.04秒, 目标时长: 4.28秒
2025-07-29 10:42:02,147 - INFO - 需要裁剪 1.76秒
2025-07-29 10:42:02,147 - INFO - 裁剪最长场景ID=2619：从3.04秒裁剪至1.28秒
2025-07-29 10:42:02,147 - INFO - 调整后总时长: 4.28秒，与目标时长差异: 0.00秒
2025-07-29 10:42:02,147 - INFO - 方案 #1 调整/填充后最终总时长: 4.28秒
2025-07-29 10:42:02,147 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:42:02,147 - INFO - ========== 当前模式：字幕 #73 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:42:02,147 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:42:02,147 - INFO - ========== 新模式：字幕 #73 共生成 3 套有效场景方案 ==========
2025-07-29 10:42:02,147 - INFO - 
----- 处理字幕 #73 的方案 #1 -----
2025-07-29 10:42:02,147 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\73_1.mp4
2025-07-29 10:42:02,147 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzzhf5r2b
2025-07-29 10:42:02,148 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2613.mp4 (确认存在: True)
2025-07-29 10:42:02,148 - INFO - 添加场景ID=2613，时长=3.00秒，累计时长=3.00秒
2025-07-29 10:42:02,148 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2614.mp4 (确认存在: True)
2025-07-29 10:42:02,148 - INFO - 添加场景ID=2614，时长=3.08秒，累计时长=6.08秒
2025-07-29 10:42:02,148 - INFO - 准备合并 2 个场景文件，总时长约 6.08秒
2025-07-29 10:42:02,149 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2613.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2614.mp4'

2025-07-29 10:42:02,149 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzzhf5r2b\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzzhf5r2b\temp_combined.mp4
2025-07-29 10:42:02,307 - INFO - 合并后的视频时长: 6.13秒，目标音频时长: 4.28秒
2025-07-29 10:42:02,307 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzzhf5r2b\temp_combined.mp4 -ss 0 -to 4.278 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\73_1.mp4
2025-07-29 10:42:02,621 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:42:02,621 - INFO - 目标音频时长: 4.28秒
2025-07-29 10:42:02,621 - INFO - 实际视频时长: 4.30秒
2025-07-29 10:42:02,621 - INFO - 时长差异: 0.03秒 (0.58%)
2025-07-29 10:42:02,621 - INFO - ==========================================
2025-07-29 10:42:02,621 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:42:02,621 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\73_1.mp4
2025-07-29 10:42:02,622 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzzhf5r2b
2025-07-29 10:42:02,688 - INFO - 方案 #1 处理完成:
2025-07-29 10:42:02,688 - INFO -   - 音频时长: 4.28秒
2025-07-29 10:42:02,688 - INFO -   - 视频时长: 4.30秒
2025-07-29 10:42:02,688 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-07-29 10:42:02,688 - INFO - 
----- 处理字幕 #73 的方案 #2 -----
2025-07-29 10:42:02,688 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\73_2.mp4
2025-07-29 10:42:02,689 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1yg0at63
2025-07-29 10:42:02,689 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2619.mp4 (确认存在: True)
2025-07-29 10:42:02,689 - INFO - 添加场景ID=2619，时长=3.04秒，累计时长=3.04秒
2025-07-29 10:42:02,690 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2620.mp4 (确认存在: True)
2025-07-29 10:42:02,690 - INFO - 添加场景ID=2620，时长=3.88秒，累计时长=6.92秒
2025-07-29 10:42:02,690 - INFO - 场景总时长(6.92秒)已达到音频时长(4.28秒)的1.5倍，停止添加场景
2025-07-29 10:42:02,690 - INFO - 准备合并 2 个场景文件，总时长约 6.92秒
2025-07-29 10:42:02,690 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2619.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2620.mp4'

2025-07-29 10:42:02,690 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1yg0at63\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1yg0at63\temp_combined.mp4
2025-07-29 10:42:02,840 - INFO - 合并后的视频时长: 6.97秒，目标音频时长: 4.28秒
2025-07-29 10:42:02,840 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1yg0at63\temp_combined.mp4 -ss 0 -to 4.278 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\73_2.mp4
2025-07-29 10:42:03,144 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:42:03,144 - INFO - 目标音频时长: 4.28秒
2025-07-29 10:42:03,144 - INFO - 实际视频时长: 4.30秒
2025-07-29 10:42:03,145 - INFO - 时长差异: 0.03秒 (0.58%)
2025-07-29 10:42:03,145 - INFO - ==========================================
2025-07-29 10:42:03,145 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:42:03,145 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\73_2.mp4
2025-07-29 10:42:03,146 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1yg0at63
2025-07-29 10:42:03,202 - INFO - 方案 #2 处理完成:
2025-07-29 10:42:03,202 - INFO -   - 音频时长: 4.28秒
2025-07-29 10:42:03,202 - INFO -   - 视频时长: 4.30秒
2025-07-29 10:42:03,202 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-07-29 10:42:03,202 - INFO - 
----- 处理字幕 #73 的方案 #3 -----
2025-07-29 10:42:03,203 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\73_3.mp4
2025-07-29 10:42:03,203 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxm8bxko_
2025-07-29 10:42:03,204 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2613.mp4 (确认存在: True)
2025-07-29 10:42:03,204 - INFO - 添加场景ID=2613，时长=3.00秒，累计时长=3.00秒
2025-07-29 10:42:03,204 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2619.mp4 (确认存在: True)
2025-07-29 10:42:03,204 - INFO - 添加场景ID=2619，时长=3.04秒，累计时长=6.04秒
2025-07-29 10:42:03,204 - INFO - 准备合并 2 个场景文件，总时长约 6.04秒
2025-07-29 10:42:03,204 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2613.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2619.mp4'

2025-07-29 10:42:03,205 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxm8bxko_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxm8bxko_\temp_combined.mp4
2025-07-29 10:42:03,370 - INFO - 合并后的视频时长: 6.09秒，目标音频时长: 4.28秒
2025-07-29 10:42:03,370 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxm8bxko_\temp_combined.mp4 -ss 0 -to 4.278 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\73_3.mp4
