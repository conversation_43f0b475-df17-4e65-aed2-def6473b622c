2025-07-29 10:40:18,762 - INFO - ========== 字幕 #3 处理开始 ==========
2025-07-29 10:40:18,762 - INFO - 字幕内容: 她心头一紧，质问哥哥府里是否有个同名之人。
2025-07-29 10:40:18,762 - INFO - 字幕序号: [8, 11]
2025-07-29 10:40:18,762 - INFO - 音频文件详情:
2025-07-29 10:40:18,762 - INFO -   - 路径: output\3.wav
2025-07-29 10:40:18,762 - INFO -   - 时长: 3.51秒
2025-07-29 10:40:18,763 - INFO -   - 验证音频时长: 3.51秒
2025-07-29 10:40:18,763 - INFO - 字幕时间戳信息:
2025-07-29 10:40:18,763 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:18,763 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:18,763 - INFO -   - 根据生成的音频时长(3.51秒)已调整字幕时间戳
2025-07-29 10:40:18,763 - INFO - ========== 新模式：为字幕 #3 生成4套场景方案 ==========
2025-07-29 10:40:18,763 - INFO - 字幕序号列表: [8, 11]
2025-07-29 10:40:18,763 - INFO - 
--- 生成方案 #1：基于字幕序号 #8 ---
2025-07-29 10:40:18,763 - INFO - 开始为单个字幕序号 #8 匹配场景，目标时长: 3.51秒
2025-07-29 10:40:18,763 - INFO - 开始查找字幕序号 [8] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:18,763 - INFO - 找到related_overlap场景: scene_id=22, 字幕#8
2025-07-29 10:40:18,764 - INFO - 字幕 #8 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:18,764 - INFO - 字幕序号 #8 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:18,764 - INFO - 选择第一个overlap场景作为起点: scene_id=22
2025-07-29 10:40:18,764 - INFO - 添加起点场景: scene_id=22, 时长=4.48秒, 累计时长=4.48秒
2025-07-29 10:40:18,764 - INFO - 起点场景时长已满足要求，无需延伸
2025-07-29 10:40:18,764 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-07-29 10:40:18,764 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-07-29 10:40:18,764 - INFO - 
--- 生成方案 #2：基于字幕序号 #11 ---
2025-07-29 10:40:18,764 - INFO - 开始为单个字幕序号 #11 匹配场景，目标时长: 3.51秒
2025-07-29 10:40:18,764 - INFO - 开始查找字幕序号 [11] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:18,764 - INFO - 找到related_overlap场景: scene_id=23, 字幕#11
2025-07-29 10:40:18,764 - INFO - 找到related_overlap场景: scene_id=24, 字幕#11
2025-07-29 10:40:18,765 - INFO - 字幕 #11 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:18,765 - INFO - 字幕序号 #11 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:18,765 - INFO - 选择第一个overlap场景作为起点: scene_id=23
2025-07-29 10:40:18,765 - INFO - 添加起点场景: scene_id=23, 时长=2.88秒, 累计时长=2.88秒
2025-07-29 10:40:18,765 - INFO - 起点场景时长不足，需要延伸填充 0.63秒
2025-07-29 10:40:18,765 - INFO - 起点场景在原始列表中的索引: 22
2025-07-29 10:40:18,766 - INFO - 延伸添加场景: scene_id=24 (裁剪至 0.63秒)
2025-07-29 10:40:18,766 - INFO - 累计时长: 3.51秒
2025-07-29 10:40:18,766 - INFO - 字幕序号 #11 场景匹配完成，共选择 2 个场景，总时长: 3.51秒
2025-07-29 10:40:18,766 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:18,766 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:18,766 - INFO - ========== 当前模式：为字幕 #3 生成 1 套场景方案 ==========
2025-07-29 10:40:18,766 - INFO - 开始查找字幕序号 [8, 11] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:18,766 - INFO - 找到related_overlap场景: scene_id=22, 字幕#8
2025-07-29 10:40:18,766 - INFO - 找到related_overlap场景: scene_id=23, 字幕#11
2025-07-29 10:40:18,766 - INFO - 找到related_overlap场景: scene_id=24, 字幕#11
2025-07-29 10:40:18,767 - INFO - 字幕 #8 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:18,767 - INFO - 字幕 #11 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:18,767 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:40:18,767 - INFO - 开始生成方案 #1
2025-07-29 10:40:18,767 - INFO - 方案 #1: 为字幕#8选择初始化overlap场景id=22
2025-07-29 10:40:18,767 - INFO - 方案 #1: 为字幕#11选择初始化overlap场景id=23
2025-07-29 10:40:18,767 - INFO - 方案 #1: 初始选择后，当前总时长=7.36秒
2025-07-29 10:40:18,767 - INFO - 方案 #1: 额外between选择后，当前总时长=7.36秒
2025-07-29 10:40:18,767 - INFO - 方案 #1: 场景总时长(7.36秒)大于音频时长(3.51秒)，需要裁剪
2025-07-29 10:40:18,767 - INFO - 调整前总时长: 7.36秒, 目标时长: 3.51秒
2025-07-29 10:40:18,767 - INFO - 需要裁剪 3.85秒
2025-07-29 10:40:18,767 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-29 10:40:18,767 - INFO - 裁剪场景ID=22：从4.48秒裁剪至1.34秒
2025-07-29 10:40:18,767 - INFO - 裁剪场景ID=23：从2.88秒裁剪至2.17秒
2025-07-29 10:40:18,767 - INFO - 调整后总时长: 3.51秒，与目标时长差异: 0.00秒
2025-07-29 10:40:18,767 - INFO - 方案 #1 调整/填充后最终总时长: 3.51秒
2025-07-29 10:40:18,767 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:18,767 - INFO - ========== 当前模式：字幕 #3 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:18,767 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:18,767 - INFO - ========== 新模式：字幕 #3 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:18,767 - INFO - 
----- 处理字幕 #3 的方案 #1 -----
2025-07-29 10:40:18,767 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-29 10:40:18,768 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4rw97yc1
2025-07-29 10:40:18,768 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\22.mp4 (确认存在: True)
2025-07-29 10:40:18,768 - INFO - 添加场景ID=22，时长=4.48秒，累计时长=4.48秒
2025-07-29 10:40:18,768 - INFO - 准备合并 1 个场景文件，总时长约 4.48秒
2025-07-29 10:40:18,768 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/22.mp4'

2025-07-29 10:40:18,768 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp4rw97yc1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp4rw97yc1\temp_combined.mp4
2025-07-29 10:40:18,883 - INFO - 合并后的视频时长: 4.50秒，目标音频时长: 3.51秒
2025-07-29 10:40:18,883 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp4rw97yc1\temp_combined.mp4 -ss 0 -to 3.513 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-29 10:40:19,162 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:19,162 - INFO - 目标音频时长: 3.51秒
2025-07-29 10:40:19,162 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:40:19,162 - INFO - 时长差异: 0.03秒 (0.85%)
2025-07-29 10:40:19,162 - INFO - ==========================================
2025-07-29 10:40:19,162 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:19,162 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-29 10:40:19,163 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4rw97yc1
2025-07-29 10:40:19,205 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:19,205 - INFO -   - 音频时长: 3.51秒
2025-07-29 10:40:19,205 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:40:19,205 - INFO -   - 时长差异: 0.03秒 (0.85%)
2025-07-29 10:40:19,205 - INFO - 
----- 处理字幕 #3 的方案 #2 -----
2025-07-29 10:40:19,205 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-29 10:40:19,205 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi1_rio73
2025-07-29 10:40:19,206 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\23.mp4 (确认存在: True)
2025-07-29 10:40:19,206 - INFO - 添加场景ID=23，时长=2.88秒，累计时长=2.88秒
2025-07-29 10:40:19,206 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\24.mp4 (确认存在: True)
2025-07-29 10:40:19,206 - INFO - 添加场景ID=24，时长=1.52秒，累计时长=4.40秒
2025-07-29 10:40:19,206 - INFO - 准备合并 2 个场景文件，总时长约 4.40秒
2025-07-29 10:40:19,206 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/23.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/24.mp4'

2025-07-29 10:40:19,206 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpi1_rio73\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpi1_rio73\temp_combined.mp4
2025-07-29 10:40:19,332 - INFO - 合并后的视频时长: 4.45秒，目标音频时长: 3.51秒
2025-07-29 10:40:19,332 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpi1_rio73\temp_combined.mp4 -ss 0 -to 3.513 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-29 10:40:19,601 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:19,601 - INFO - 目标音频时长: 3.51秒
2025-07-29 10:40:19,601 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:40:19,601 - INFO - 时长差异: 0.03秒 (0.85%)
2025-07-29 10:40:19,601 - INFO - ==========================================
2025-07-29 10:40:19,602 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:19,602 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-29 10:40:19,602 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi1_rio73
2025-07-29 10:40:19,645 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:19,645 - INFO -   - 音频时长: 3.51秒
2025-07-29 10:40:19,645 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:40:19,645 - INFO -   - 时长差异: 0.03秒 (0.85%)
2025-07-29 10:40:19,645 - INFO - 
----- 处理字幕 #3 的方案 #3 -----
2025-07-29 10:40:19,645 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-29 10:40:19,645 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp15f1n5zt
2025-07-29 10:40:19,646 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\22.mp4 (确认存在: True)
2025-07-29 10:40:19,646 - INFO - 添加场景ID=22，时长=4.48秒，累计时长=4.48秒
2025-07-29 10:40:19,646 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\23.mp4 (确认存在: True)
2025-07-29 10:40:19,646 - INFO - 添加场景ID=23，时长=2.88秒，累计时长=7.36秒
2025-07-29 10:40:19,646 - INFO - 场景总时长(7.36秒)已达到音频时长(3.51秒)的1.5倍，停止添加场景
2025-07-29 10:40:19,646 - INFO - 准备合并 2 个场景文件，总时长约 7.36秒
2025-07-29 10:40:19,646 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/22.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/23.mp4'

2025-07-29 10:40:19,646 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp15f1n5zt\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp15f1n5zt\temp_combined.mp4
2025-07-29 10:40:19,772 - INFO - 合并后的视频时长: 7.41秒，目标音频时长: 3.51秒
2025-07-29 10:40:19,772 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp15f1n5zt\temp_combined.mp4 -ss 0 -to 3.513 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-29 10:40:20,055 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:20,055 - INFO - 目标音频时长: 3.51秒
2025-07-29 10:40:20,055 - INFO - 实际视频时长: 3.54秒
2025-07-29 10:40:20,055 - INFO - 时长差异: 0.03秒 (0.85%)
2025-07-29 10:40:20,055 - INFO - ==========================================
2025-07-29 10:40:20,055 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:20,055 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-29 10:40:20,055 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp15f1n5zt
2025-07-29 10:40:20,097 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:20,097 - INFO -   - 音频时长: 3.51秒
2025-07-29 10:40:20,097 - INFO -   - 视频时长: 3.54秒
2025-07-29 10:40:20,097 - INFO -   - 时长差异: 0.03秒 (0.85%)
2025-07-29 10:40:20,099 - INFO - 
字幕 #3 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:20,099 - INFO - 生成的视频文件:
2025-07-29 10:40:20,099 - INFO -   1. F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-29 10:40:20,099 - INFO -   2. F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-07-29 10:40:20,099 - INFO -   3. F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-07-29 10:40:20,099 - INFO - ========== 字幕 #3 处理结束 ==========

