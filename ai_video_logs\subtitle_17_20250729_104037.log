2025-07-29 10:40:37,631 - INFO - ========== 字幕 #17 处理开始 ==========
2025-07-29 10:40:37,631 - INFO - 字幕内容: 摄政王为她，竟命所有女眷戴上帷帽遮掩容貌。
2025-07-29 10:40:37,631 - INFO - 字幕序号: [79, 85]
2025-07-29 10:40:37,631 - INFO - 音频文件详情:
2025-07-29 10:40:37,631 - INFO -   - 路径: output\17.wav
2025-07-29 10:40:37,631 - INFO -   - 时长: 4.22秒
2025-07-29 10:40:37,632 - INFO -   - 验证音频时长: 4.22秒
2025-07-29 10:40:37,632 - INFO - 字幕时间戳信息:
2025-07-29 10:40:37,632 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:37,632 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:37,632 - INFO -   - 根据生成的音频时长(4.22秒)已调整字幕时间戳
2025-07-29 10:40:37,632 - INFO - ========== 新模式：为字幕 #17 生成4套场景方案 ==========
2025-07-29 10:40:37,632 - INFO - 字幕序号列表: [79, 85]
2025-07-29 10:40:37,632 - INFO - 
--- 生成方案 #1：基于字幕序号 #79 ---
2025-07-29 10:40:37,632 - INFO - 开始为单个字幕序号 #79 匹配场景，目标时长: 4.22秒
2025-07-29 10:40:37,632 - INFO - 开始查找字幕序号 [79] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:37,632 - INFO - 找到related_overlap场景: scene_id=109, 字幕#79
2025-07-29 10:40:37,633 - INFO - 找到related_between场景: scene_id=107, 字幕#79
2025-07-29 10:40:37,633 - INFO - 找到related_between场景: scene_id=108, 字幕#79
2025-07-29 10:40:37,633 - INFO - 找到related_between场景: scene_id=110, 字幕#79
2025-07-29 10:40:37,633 - INFO - 字幕 #79 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:40:37,634 - INFO - 字幕序号 #79 找到 1 个可用overlap场景, 3 个可用between场景
2025-07-29 10:40:37,634 - INFO - 选择第一个overlap场景作为起点: scene_id=109
2025-07-29 10:40:37,634 - INFO - 添加起点场景: scene_id=109, 时长=0.88秒, 累计时长=0.88秒
2025-07-29 10:40:37,634 - INFO - 起点场景时长不足，需要延伸填充 3.34秒
2025-07-29 10:40:37,634 - INFO - 起点场景在原始列表中的索引: 108
2025-07-29 10:40:37,634 - INFO - 延伸添加场景: scene_id=110 (完整时长 1.60秒)
2025-07-29 10:40:37,634 - INFO - 累计时长: 2.48秒
2025-07-29 10:40:37,634 - INFO - 延伸添加场景: scene_id=111 (完整时长 1.00秒)
2025-07-29 10:40:37,634 - INFO - 累计时长: 3.48秒
2025-07-29 10:40:37,634 - INFO - 延伸添加场景: scene_id=112 (裁剪至 0.74秒)
2025-07-29 10:40:37,634 - INFO - 累计时长: 4.22秒
2025-07-29 10:40:37,634 - INFO - 字幕序号 #79 场景匹配完成，共选择 4 个场景，总时长: 4.22秒
2025-07-29 10:40:37,634 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-07-29 10:40:37,634 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-07-29 10:40:37,634 - INFO - 
--- 生成方案 #2：基于字幕序号 #85 ---
2025-07-29 10:40:37,634 - INFO - 开始为单个字幕序号 #85 匹配场景，目标时长: 4.22秒
2025-07-29 10:40:37,634 - INFO - 开始查找字幕序号 [85] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:37,634 - INFO - 找到related_overlap场景: scene_id=117, 字幕#85
2025-07-29 10:40:37,634 - INFO - 找到related_overlap场景: scene_id=118, 字幕#85
2025-07-29 10:40:37,635 - INFO - 字幕 #85 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:37,635 - INFO - 字幕序号 #85 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:37,635 - INFO - 选择第一个overlap场景作为起点: scene_id=117
2025-07-29 10:40:37,635 - INFO - 添加起点场景: scene_id=117, 时长=1.44秒, 累计时长=1.44秒
2025-07-29 10:40:37,635 - INFO - 起点场景时长不足，需要延伸填充 2.78秒
2025-07-29 10:40:37,635 - INFO - 起点场景在原始列表中的索引: 116
2025-07-29 10:40:37,635 - INFO - 延伸添加场景: scene_id=118 (完整时长 2.00秒)
2025-07-29 10:40:37,635 - INFO - 累计时长: 3.44秒
2025-07-29 10:40:37,635 - INFO - 延伸添加场景: scene_id=119 (裁剪至 0.78秒)
2025-07-29 10:40:37,635 - INFO - 累计时长: 4.22秒
2025-07-29 10:40:37,635 - INFO - 字幕序号 #85 场景匹配完成，共选择 3 个场景，总时长: 4.22秒
2025-07-29 10:40:37,635 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:37,635 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:37,635 - INFO - ========== 当前模式：为字幕 #17 生成 1 套场景方案 ==========
2025-07-29 10:40:37,635 - INFO - 开始查找字幕序号 [79, 85] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:37,635 - INFO - 找到related_overlap场景: scene_id=109, 字幕#79
2025-07-29 10:40:37,635 - INFO - 找到related_overlap场景: scene_id=117, 字幕#85
2025-07-29 10:40:37,635 - INFO - 找到related_overlap场景: scene_id=118, 字幕#85
2025-07-29 10:40:37,636 - INFO - 找到related_between场景: scene_id=107, 字幕#79
2025-07-29 10:40:37,636 - INFO - 找到related_between场景: scene_id=108, 字幕#79
2025-07-29 10:40:37,636 - INFO - 找到related_between场景: scene_id=110, 字幕#79
2025-07-29 10:40:37,636 - INFO - 字幕 #79 找到 1 个overlap场景, 3 个between场景
2025-07-29 10:40:37,636 - INFO - 字幕 #85 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:37,636 - INFO - 共收集 3 个未使用的overlap场景和 3 个未使用的between场景
2025-07-29 10:40:37,636 - INFO - 开始生成方案 #1
2025-07-29 10:40:37,636 - INFO - 方案 #1: 为字幕#79选择初始化overlap场景id=109
2025-07-29 10:40:37,636 - INFO - 方案 #1: 为字幕#85选择初始化overlap场景id=117
2025-07-29 10:40:37,636 - INFO - 方案 #1: 初始选择后，当前总时长=2.32秒
2025-07-29 10:40:37,636 - INFO - 方案 #1: 额外添加overlap场景id=118, 当前总时长=4.32秒
2025-07-29 10:40:37,636 - INFO - 方案 #1: 额外between选择后，当前总时长=4.32秒
2025-07-29 10:40:37,636 - INFO - 方案 #1: 场景总时长(4.32秒)大于音频时长(4.22秒)，需要裁剪
2025-07-29 10:40:37,636 - INFO - 调整前总时长: 4.32秒, 目标时长: 4.22秒
2025-07-29 10:40:37,636 - INFO - 需要裁剪 0.10秒
2025-07-29 10:40:37,636 - INFO - 裁剪最长场景ID=118：从2.00秒裁剪至1.90秒
2025-07-29 10:40:37,637 - INFO - 调整后总时长: 4.22秒，与目标时长差异: 0.00秒
2025-07-29 10:40:37,637 - INFO - 方案 #1 调整/填充后最终总时长: 4.22秒
2025-07-29 10:40:37,637 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:37,637 - INFO - ========== 当前模式：字幕 #17 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:37,637 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:37,637 - INFO - ========== 新模式：字幕 #17 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:37,637 - INFO - 
----- 处理字幕 #17 的方案 #1 -----
2025-07-29 10:40:37,637 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-07-29 10:40:37,637 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqbpdjxb0
2025-07-29 10:40:37,637 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\109.mp4 (确认存在: True)
2025-07-29 10:40:37,637 - INFO - 添加场景ID=109，时长=0.88秒，累计时长=0.88秒
2025-07-29 10:40:37,637 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\110.mp4 (确认存在: True)
2025-07-29 10:40:37,637 - INFO - 添加场景ID=110，时长=1.60秒，累计时长=2.48秒
2025-07-29 10:40:37,637 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\111.mp4 (确认存在: True)
2025-07-29 10:40:37,637 - INFO - 添加场景ID=111，时长=1.00秒，累计时长=3.48秒
2025-07-29 10:40:37,637 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\112.mp4 (确认存在: True)
2025-07-29 10:40:37,637 - INFO - 添加场景ID=112，时长=1.64秒，累计时长=5.12秒
2025-07-29 10:40:37,637 - INFO - 准备合并 4 个场景文件，总时长约 5.12秒
2025-07-29 10:40:37,638 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/109.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/110.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/111.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/112.mp4'

2025-07-29 10:40:37,638 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqbpdjxb0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqbpdjxb0\temp_combined.mp4
2025-07-29 10:40:37,795 - INFO - 合并后的视频时长: 5.21秒，目标音频时长: 4.22秒
2025-07-29 10:40:37,795 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqbpdjxb0\temp_combined.mp4 -ss 0 -to 4.219 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-07-29 10:40:38,081 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:38,081 - INFO - 目标音频时长: 4.22秒
2025-07-29 10:40:38,081 - INFO - 实际视频时长: 4.26秒
2025-07-29 10:40:38,081 - INFO - 时长差异: 0.04秒 (1.04%)
2025-07-29 10:40:38,081 - INFO - ==========================================
2025-07-29 10:40:38,081 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:38,081 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-07-29 10:40:38,082 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqbpdjxb0
2025-07-29 10:40:38,132 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:38,132 - INFO -   - 音频时长: 4.22秒
2025-07-29 10:40:38,132 - INFO -   - 视频时长: 4.26秒
2025-07-29 10:40:38,132 - INFO -   - 时长差异: 0.04秒 (1.04%)
2025-07-29 10:40:38,132 - INFO - 
----- 处理字幕 #17 的方案 #2 -----
2025-07-29 10:40:38,132 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-07-29 10:40:38,132 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnndxvx78
2025-07-29 10:40:38,133 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\117.mp4 (确认存在: True)
2025-07-29 10:40:38,133 - INFO - 添加场景ID=117，时长=1.44秒，累计时长=1.44秒
2025-07-29 10:40:38,133 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\118.mp4 (确认存在: True)
2025-07-29 10:40:38,133 - INFO - 添加场景ID=118，时长=2.00秒，累计时长=3.44秒
2025-07-29 10:40:38,133 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\119.mp4 (确认存在: True)
2025-07-29 10:40:38,133 - INFO - 添加场景ID=119，时长=1.16秒，累计时长=4.60秒
2025-07-29 10:40:38,133 - INFO - 准备合并 3 个场景文件，总时长约 4.60秒
2025-07-29 10:40:38,133 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/117.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/118.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/119.mp4'

2025-07-29 10:40:38,133 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnndxvx78\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnndxvx78\temp_combined.mp4
2025-07-29 10:40:38,279 - INFO - 合并后的视频时长: 4.67秒，目标音频时长: 4.22秒
2025-07-29 10:40:38,279 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnndxvx78\temp_combined.mp4 -ss 0 -to 4.219 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-07-29 10:40:38,574 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:38,574 - INFO - 目标音频时长: 4.22秒
2025-07-29 10:40:38,574 - INFO - 实际视频时长: 4.26秒
2025-07-29 10:40:38,574 - INFO - 时长差异: 0.04秒 (1.04%)
2025-07-29 10:40:38,574 - INFO - ==========================================
2025-07-29 10:40:38,574 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:38,574 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-07-29 10:40:38,575 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnndxvx78
2025-07-29 10:40:38,617 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:38,617 - INFO -   - 音频时长: 4.22秒
2025-07-29 10:40:38,617 - INFO -   - 视频时长: 4.26秒
2025-07-29 10:40:38,617 - INFO -   - 时长差异: 0.04秒 (1.04%)
2025-07-29 10:40:38,617 - INFO - 
----- 处理字幕 #17 的方案 #3 -----
2025-07-29 10:40:38,617 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\17_3.mp4
2025-07-29 10:40:38,618 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8biusqs7
2025-07-29 10:40:38,618 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\109.mp4 (确认存在: True)
2025-07-29 10:40:38,618 - INFO - 添加场景ID=109，时长=0.88秒，累计时长=0.88秒
2025-07-29 10:40:38,618 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\117.mp4 (确认存在: True)
2025-07-29 10:40:38,618 - INFO - 添加场景ID=117，时长=1.44秒，累计时长=2.32秒
2025-07-29 10:40:38,618 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\118.mp4 (确认存在: True)
2025-07-29 10:40:38,618 - INFO - 添加场景ID=118，时长=2.00秒，累计时长=4.32秒
2025-07-29 10:40:38,618 - INFO - 准备合并 3 个场景文件，总时长约 4.32秒
2025-07-29 10:40:38,618 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/109.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/117.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/118.mp4'

2025-07-29 10:40:38,619 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8biusqs7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8biusqs7\temp_combined.mp4
2025-07-29 10:40:38,756 - INFO - 合并后的视频时长: 4.39秒，目标音频时长: 4.22秒
2025-07-29 10:40:38,756 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8biusqs7\temp_combined.mp4 -ss 0 -to 4.219 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\17_3.mp4
