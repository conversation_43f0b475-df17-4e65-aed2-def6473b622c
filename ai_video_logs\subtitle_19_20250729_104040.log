2025-07-29 10:40:40,337 - INFO - ========== 字幕 #19 处理开始 ==========
2025-07-29 10:40:40,337 - INFO - 字幕内容: 发现认错人，她羞愤地表示才不想知道他是谁。
2025-07-29 10:40:40,337 - INFO - 字幕序号: [91, 96]
2025-07-29 10:40:40,337 - INFO - 音频文件详情:
2025-07-29 10:40:40,337 - INFO -   - 路径: output\19.wav
2025-07-29 10:40:40,337 - INFO -   - 时长: 3.29秒
2025-07-29 10:40:40,337 - INFO -   - 验证音频时长: 3.29秒
2025-07-29 10:40:40,337 - INFO - 字幕时间戳信息:
2025-07-29 10:40:40,337 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:40,337 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:40,337 - INFO -   - 根据生成的音频时长(3.29秒)已调整字幕时间戳
2025-07-29 10:40:40,337 - INFO - ========== 新模式：为字幕 #19 生成4套场景方案 ==========
2025-07-29 10:40:40,337 - INFO - 字幕序号列表: [91, 96]
2025-07-29 10:40:40,337 - INFO - 
--- 生成方案 #1：基于字幕序号 #91 ---
2025-07-29 10:40:40,337 - INFO - 开始为单个字幕序号 #91 匹配场景，目标时长: 3.29秒
2025-07-29 10:40:40,337 - INFO - 开始查找字幕序号 [91] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:40,337 - INFO - 找到related_overlap场景: scene_id=138, 字幕#91
2025-07-29 10:40:40,339 - INFO - 找到related_between场景: scene_id=132, 字幕#91
2025-07-29 10:40:40,339 - INFO - 找到related_between场景: scene_id=133, 字幕#91
2025-07-29 10:40:40,339 - INFO - 找到related_between场景: scene_id=134, 字幕#91
2025-07-29 10:40:40,339 - INFO - 找到related_between场景: scene_id=135, 字幕#91
2025-07-29 10:40:40,339 - INFO - 找到related_between场景: scene_id=136, 字幕#91
2025-07-29 10:40:40,339 - INFO - 找到related_between场景: scene_id=137, 字幕#91
2025-07-29 10:40:40,339 - INFO - 字幕 #91 找到 1 个overlap场景, 6 个between场景
2025-07-29 10:40:40,339 - INFO - 字幕序号 #91 找到 1 个可用overlap场景, 6 个可用between场景
2025-07-29 10:40:40,339 - INFO - 选择第一个overlap场景作为起点: scene_id=138
2025-07-29 10:40:40,339 - INFO - 添加起点场景: scene_id=138, 时长=1.36秒, 累计时长=1.36秒
2025-07-29 10:40:40,339 - INFO - 起点场景时长不足，需要延伸填充 1.93秒
2025-07-29 10:40:40,339 - INFO - 起点场景在原始列表中的索引: 137
2025-07-29 10:40:40,339 - INFO - 延伸添加场景: scene_id=139 (完整时长 1.92秒)
2025-07-29 10:40:40,339 - INFO - 累计时长: 3.28秒
2025-07-29 10:40:40,340 - INFO - 延伸添加场景: scene_id=140 (裁剪至 0.01秒)
2025-07-29 10:40:40,340 - INFO - 累计时长: 3.29秒
2025-07-29 10:40:40,340 - INFO - 字幕序号 #91 场景匹配完成，共选择 3 个场景，总时长: 3.29秒
2025-07-29 10:40:40,340 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:40:40,340 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:40:40,340 - INFO - 
--- 生成方案 #2：基于字幕序号 #96 ---
2025-07-29 10:40:40,340 - INFO - 开始为单个字幕序号 #96 匹配场景，目标时长: 3.29秒
2025-07-29 10:40:40,340 - INFO - 开始查找字幕序号 [96] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:40,340 - INFO - 找到related_overlap场景: scene_id=144, 字幕#96
2025-07-29 10:40:40,340 - INFO - 找到related_between场景: scene_id=145, 字幕#96
2025-07-29 10:40:40,340 - INFO - 找到related_between场景: scene_id=146, 字幕#96
2025-07-29 10:40:40,340 - INFO - 找到related_between场景: scene_id=147, 字幕#96
2025-07-29 10:40:40,340 - INFO - 找到related_between场景: scene_id=148, 字幕#96
2025-07-29 10:40:40,341 - INFO - 字幕 #96 找到 1 个overlap场景, 4 个between场景
2025-07-29 10:40:40,341 - INFO - 字幕序号 #96 找到 1 个可用overlap场景, 4 个可用between场景
2025-07-29 10:40:40,341 - INFO - 选择第一个overlap场景作为起点: scene_id=144
2025-07-29 10:40:40,341 - INFO - 添加起点场景: scene_id=144, 时长=2.16秒, 累计时长=2.16秒
2025-07-29 10:40:40,341 - INFO - 起点场景时长不足，需要延伸填充 1.13秒
2025-07-29 10:40:40,341 - INFO - 起点场景在原始列表中的索引: 143
2025-07-29 10:40:40,341 - INFO - 延伸添加场景: scene_id=145 (裁剪至 1.13秒)
2025-07-29 10:40:40,341 - INFO - 累计时长: 3.29秒
2025-07-29 10:40:40,341 - INFO - 字幕序号 #96 场景匹配完成，共选择 2 个场景，总时长: 3.29秒
2025-07-29 10:40:40,341 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:40,341 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:40,341 - INFO - ========== 当前模式：为字幕 #19 生成 1 套场景方案 ==========
2025-07-29 10:40:40,341 - INFO - 开始查找字幕序号 [91, 96] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:40,341 - INFO - 找到related_overlap场景: scene_id=138, 字幕#91
2025-07-29 10:40:40,341 - INFO - 找到related_overlap场景: scene_id=144, 字幕#96
2025-07-29 10:40:40,342 - INFO - 找到related_between场景: scene_id=132, 字幕#91
2025-07-29 10:40:40,342 - INFO - 找到related_between场景: scene_id=133, 字幕#91
2025-07-29 10:40:40,342 - INFO - 找到related_between场景: scene_id=134, 字幕#91
2025-07-29 10:40:40,342 - INFO - 找到related_between场景: scene_id=135, 字幕#91
2025-07-29 10:40:40,342 - INFO - 找到related_between场景: scene_id=136, 字幕#91
2025-07-29 10:40:40,342 - INFO - 找到related_between场景: scene_id=137, 字幕#91
2025-07-29 10:40:40,342 - INFO - 找到related_between场景: scene_id=145, 字幕#96
2025-07-29 10:40:40,342 - INFO - 找到related_between场景: scene_id=146, 字幕#96
2025-07-29 10:40:40,342 - INFO - 找到related_between场景: scene_id=147, 字幕#96
2025-07-29 10:40:40,342 - INFO - 找到related_between场景: scene_id=148, 字幕#96
2025-07-29 10:40:40,342 - INFO - 字幕 #91 找到 1 个overlap场景, 6 个between场景
2025-07-29 10:40:40,342 - INFO - 字幕 #96 找到 1 个overlap场景, 4 个between场景
2025-07-29 10:40:40,342 - INFO - 共收集 2 个未使用的overlap场景和 10 个未使用的between场景
2025-07-29 10:40:40,342 - INFO - 开始生成方案 #1
2025-07-29 10:40:40,342 - INFO - 方案 #1: 为字幕#91选择初始化overlap场景id=138
2025-07-29 10:40:40,342 - INFO - 方案 #1: 为字幕#96选择初始化overlap场景id=144
2025-07-29 10:40:40,342 - INFO - 方案 #1: 初始选择后，当前总时长=3.52秒
2025-07-29 10:40:40,342 - INFO - 方案 #1: 额外between选择后，当前总时长=3.52秒
2025-07-29 10:40:40,342 - INFO - 方案 #1: 场景总时长(3.52秒)大于音频时长(3.29秒)，需要裁剪
2025-07-29 10:40:40,342 - INFO - 调整前总时长: 3.52秒, 目标时长: 3.29秒
2025-07-29 10:40:40,342 - INFO - 需要裁剪 0.23秒
2025-07-29 10:40:40,342 - INFO - 裁剪最长场景ID=144：从2.16秒裁剪至1.93秒
2025-07-29 10:40:40,342 - INFO - 调整后总时长: 3.29秒，与目标时长差异: 0.00秒
2025-07-29 10:40:40,342 - INFO - 方案 #1 调整/填充后最终总时长: 3.29秒
2025-07-29 10:40:40,342 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:40,342 - INFO - ========== 当前模式：字幕 #19 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:40,342 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:40,342 - INFO - ========== 新模式：字幕 #19 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:40,342 - INFO - 
----- 处理字幕 #19 的方案 #1 -----
2025-07-29 10:40:40,343 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-29 10:40:40,343 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbf7o_n4o
2025-07-29 10:40:40,343 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\138.mp4 (确认存在: True)
2025-07-29 10:40:40,344 - INFO - 添加场景ID=138，时长=1.36秒，累计时长=1.36秒
2025-07-29 10:40:40,344 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\139.mp4 (确认存在: True)
2025-07-29 10:40:40,344 - INFO - 添加场景ID=139，时长=1.92秒，累计时长=3.28秒
2025-07-29 10:40:40,344 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\140.mp4 (确认存在: True)
2025-07-29 10:40:40,344 - INFO - 添加场景ID=140，时长=1.88秒，累计时长=5.16秒
2025-07-29 10:40:40,344 - INFO - 场景总时长(5.16秒)已达到音频时长(3.29秒)的1.5倍，停止添加场景
2025-07-29 10:40:40,344 - INFO - 准备合并 3 个场景文件，总时长约 5.16秒
2025-07-29 10:40:40,344 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/138.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/139.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/140.mp4'

2025-07-29 10:40:40,344 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbf7o_n4o\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbf7o_n4o\temp_combined.mp4
2025-07-29 10:40:40,490 - INFO - 合并后的视频时长: 5.23秒，目标音频时长: 3.29秒
2025-07-29 10:40:40,490 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbf7o_n4o\temp_combined.mp4 -ss 0 -to 3.287 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-29 10:40:40,749 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:40,749 - INFO - 目标音频时长: 3.29秒
2025-07-29 10:40:40,749 - INFO - 实际视频时长: 3.34秒
2025-07-29 10:40:40,749 - INFO - 时长差异: 0.06秒 (1.70%)
2025-07-29 10:40:40,749 - INFO - ==========================================
2025-07-29 10:40:40,749 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:40,749 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-29 10:40:40,750 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbf7o_n4o
2025-07-29 10:40:40,793 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:40,793 - INFO -   - 音频时长: 3.29秒
2025-07-29 10:40:40,793 - INFO -   - 视频时长: 3.34秒
2025-07-29 10:40:40,793 - INFO -   - 时长差异: 0.06秒 (1.70%)
2025-07-29 10:40:40,793 - INFO - 
----- 处理字幕 #19 的方案 #2 -----
2025-07-29 10:40:40,793 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-29 10:40:40,794 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_nnh6p1q
2025-07-29 10:40:40,794 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\144.mp4 (确认存在: True)
2025-07-29 10:40:40,794 - INFO - 添加场景ID=144，时长=2.16秒，累计时长=2.16秒
2025-07-29 10:40:40,794 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\145.mp4 (确认存在: True)
2025-07-29 10:40:40,794 - INFO - 添加场景ID=145，时长=1.88秒，累计时长=4.04秒
2025-07-29 10:40:40,794 - INFO - 准备合并 2 个场景文件，总时长约 4.04秒
2025-07-29 10:40:40,795 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/144.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/145.mp4'

2025-07-29 10:40:40,795 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_nnh6p1q\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_nnh6p1q\temp_combined.mp4
2025-07-29 10:40:40,911 - INFO - 合并后的视频时长: 4.09秒，目标音频时长: 3.29秒
2025-07-29 10:40:40,911 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_nnh6p1q\temp_combined.mp4 -ss 0 -to 3.287 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-29 10:40:41,147 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:41,148 - INFO - 目标音频时长: 3.29秒
2025-07-29 10:40:41,148 - INFO - 实际视频时长: 3.34秒
2025-07-29 10:40:41,148 - INFO - 时长差异: 0.06秒 (1.70%)
2025-07-29 10:40:41,148 - INFO - ==========================================
2025-07-29 10:40:41,148 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:41,148 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-29 10:40:41,149 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_nnh6p1q
2025-07-29 10:40:41,202 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:41,202 - INFO -   - 音频时长: 3.29秒
2025-07-29 10:40:41,202 - INFO -   - 视频时长: 3.34秒
2025-07-29 10:40:41,202 - INFO -   - 时长差异: 0.06秒 (1.70%)
2025-07-29 10:40:41,202 - INFO - 
----- 处理字幕 #19 的方案 #3 -----
2025-07-29 10:40:41,202 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-07-29 10:40:41,202 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppx0u9d6c
2025-07-29 10:40:41,202 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\138.mp4 (确认存在: True)
2025-07-29 10:40:41,202 - INFO - 添加场景ID=138，时长=1.36秒，累计时长=1.36秒
2025-07-29 10:40:41,203 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\144.mp4 (确认存在: True)
2025-07-29 10:40:41,203 - INFO - 添加场景ID=144，时长=2.16秒，累计时长=3.52秒
2025-07-29 10:40:41,203 - INFO - 准备合并 2 个场景文件，总时长约 3.52秒
2025-07-29 10:40:41,203 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/138.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/144.mp4'

2025-07-29 10:40:41,203 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppx0u9d6c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppx0u9d6c\temp_combined.mp4
2025-07-29 10:40:41,325 - INFO - 合并后的视频时长: 3.57秒，目标音频时长: 3.29秒
2025-07-29 10:40:41,325 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppx0u9d6c\temp_combined.mp4 -ss 0 -to 3.287 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-07-29 10:40:41,573 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:41,573 - INFO - 目标音频时长: 3.29秒
2025-07-29 10:40:41,573 - INFO - 实际视频时长: 3.34秒
2025-07-29 10:40:41,573 - INFO - 时长差异: 0.06秒 (1.70%)
2025-07-29 10:40:41,573 - INFO - ==========================================
2025-07-29 10:40:41,573 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:41,573 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-07-29 10:40:41,574 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppx0u9d6c
2025-07-29 10:40:41,624 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:41,624 - INFO -   - 音频时长: 3.29秒
2025-07-29 10:40:41,624 - INFO -   - 视频时长: 3.34秒
2025-07-29 10:40:41,624 - INFO -   - 时长差异: 0.06秒 (1.70%)
2025-07-29 10:40:41,624 - INFO - 
字幕 #19 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:41,624 - INFO - 生成的视频文件:
2025-07-29 10:40:41,624 - INFO -   1. F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-29 10:40:41,624 - INFO -   2. F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-29 10:40:41,624 - INFO -   3. F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-07-29 10:40:41,624 - INFO - ========== 字幕 #19 处理结束 ==========

