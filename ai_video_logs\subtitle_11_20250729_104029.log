2025-07-29 10:40:29,584 - INFO - ========== 字幕 #11 处理开始 ==========
2025-07-29 10:40:29,584 - INFO - 字幕内容: 哥哥承诺带她去摄政王的接风宴，祖母才消气。
2025-07-29 10:40:29,584 - INFO - 字幕序号: [45, 49]
2025-07-29 10:40:29,584 - INFO - 音频文件详情:
2025-07-29 10:40:29,584 - INFO -   - 路径: output\11.wav
2025-07-29 10:40:29,584 - INFO -   - 时长: 4.11秒
2025-07-29 10:40:29,585 - INFO -   - 验证音频时长: 4.11秒
2025-07-29 10:40:29,585 - INFO - 字幕时间戳信息:
2025-07-29 10:40:29,585 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:29,585 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:29,585 - INFO -   - 根据生成的音频时长(4.11秒)已调整字幕时间戳
2025-07-29 10:40:29,585 - INFO - ========== 新模式：为字幕 #11 生成4套场景方案 ==========
2025-07-29 10:40:29,585 - INFO - 字幕序号列表: [45, 49]
2025-07-29 10:40:29,585 - INFO - 
--- 生成方案 #1：基于字幕序号 #45 ---
2025-07-29 10:40:29,585 - INFO - 开始为单个字幕序号 #45 匹配场景，目标时长: 4.11秒
2025-07-29 10:40:29,585 - INFO - 开始查找字幕序号 [45] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:29,585 - INFO - 找到related_overlap场景: scene_id=61, 字幕#45
2025-07-29 10:40:29,586 - INFO - 字幕 #45 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:29,586 - INFO - 字幕序号 #45 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:29,586 - INFO - 选择第一个overlap场景作为起点: scene_id=61
2025-07-29 10:40:29,587 - INFO - 添加起点场景: scene_id=61, 时长=2.24秒, 累计时长=2.24秒
2025-07-29 10:40:29,587 - INFO - 起点场景时长不足，需要延伸填充 1.87秒
2025-07-29 10:40:29,587 - INFO - 起点场景在原始列表中的索引: 60
2025-07-29 10:40:29,587 - INFO - 延伸添加场景: scene_id=62 (裁剪至 1.87秒)
2025-07-29 10:40:29,587 - INFO - 累计时长: 4.11秒
2025-07-29 10:40:29,587 - INFO - 字幕序号 #45 场景匹配完成，共选择 2 个场景，总时长: 4.11秒
2025-07-29 10:40:29,587 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:29,587 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:29,587 - INFO - 
--- 生成方案 #2：基于字幕序号 #49 ---
2025-07-29 10:40:29,587 - INFO - 开始为单个字幕序号 #49 匹配场景，目标时长: 4.11秒
2025-07-29 10:40:29,587 - INFO - 开始查找字幕序号 [49] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:29,587 - INFO - 找到related_overlap场景: scene_id=63, 字幕#49
2025-07-29 10:40:29,587 - INFO - 找到related_overlap场景: scene_id=64, 字幕#49
2025-07-29 10:40:29,587 - INFO - 字幕 #49 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:29,587 - INFO - 字幕序号 #49 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:29,587 - INFO - 选择第一个overlap场景作为起点: scene_id=63
2025-07-29 10:40:29,587 - INFO - 添加起点场景: scene_id=63, 时长=2.16秒, 累计时长=2.16秒
2025-07-29 10:40:29,587 - INFO - 起点场景时长不足，需要延伸填充 1.95秒
2025-07-29 10:40:29,587 - INFO - 起点场景在原始列表中的索引: 62
2025-07-29 10:40:29,587 - INFO - 延伸添加场景: scene_id=64 (裁剪至 1.95秒)
2025-07-29 10:40:29,587 - INFO - 累计时长: 4.11秒
2025-07-29 10:40:29,587 - INFO - 字幕序号 #49 场景匹配完成，共选择 2 个场景，总时长: 4.11秒
2025-07-29 10:40:29,587 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-07-29 10:40:29,587 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:29,587 - INFO - ========== 当前模式：为字幕 #11 生成 1 套场景方案 ==========
2025-07-29 10:40:29,587 - INFO - 开始查找字幕序号 [45, 49] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:29,587 - INFO - 找到related_overlap场景: scene_id=61, 字幕#45
2025-07-29 10:40:29,589 - INFO - 找到related_overlap场景: scene_id=63, 字幕#49
2025-07-29 10:40:29,589 - INFO - 找到related_overlap场景: scene_id=64, 字幕#49
2025-07-29 10:40:29,590 - INFO - 字幕 #45 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:29,590 - INFO - 字幕 #49 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:29,590 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:40:29,590 - INFO - 开始生成方案 #1
2025-07-29 10:40:29,590 - INFO - 方案 #1: 为字幕#45选择初始化overlap场景id=61
2025-07-29 10:40:29,590 - INFO - 方案 #1: 为字幕#49选择初始化overlap场景id=64
2025-07-29 10:40:29,590 - INFO - 方案 #1: 初始选择后，当前总时长=4.36秒
2025-07-29 10:40:29,590 - INFO - 方案 #1: 额外between选择后，当前总时长=4.36秒
2025-07-29 10:40:29,590 - INFO - 方案 #1: 场景总时长(4.36秒)大于音频时长(4.11秒)，需要裁剪
2025-07-29 10:40:29,590 - INFO - 调整前总时长: 4.36秒, 目标时长: 4.11秒
2025-07-29 10:40:29,590 - INFO - 需要裁剪 0.25秒
2025-07-29 10:40:29,590 - INFO - 裁剪最长场景ID=61：从2.24秒裁剪至1.99秒
2025-07-29 10:40:29,590 - INFO - 调整后总时长: 4.11秒，与目标时长差异: 0.00秒
2025-07-29 10:40:29,590 - INFO - 方案 #1 调整/填充后最终总时长: 4.11秒
2025-07-29 10:40:29,590 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:29,590 - INFO - ========== 当前模式：字幕 #11 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:29,590 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:29,590 - INFO - ========== 新模式：字幕 #11 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:29,590 - INFO - 
----- 处理字幕 #11 的方案 #1 -----
2025-07-29 10:40:29,590 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-29 10:40:29,590 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt7ftgfj3
2025-07-29 10:40:29,591 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\61.mp4 (确认存在: True)
2025-07-29 10:40:29,591 - INFO - 添加场景ID=61，时长=2.24秒，累计时长=2.24秒
2025-07-29 10:40:29,591 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\62.mp4 (确认存在: True)
2025-07-29 10:40:29,591 - INFO - 添加场景ID=62，时长=2.20秒，累计时长=4.44秒
2025-07-29 10:40:29,591 - INFO - 准备合并 2 个场景文件，总时长约 4.44秒
2025-07-29 10:40:29,591 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/61.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/62.mp4'

2025-07-29 10:40:29,591 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpt7ftgfj3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpt7ftgfj3\temp_combined.mp4
2025-07-29 10:40:29,712 - INFO - 合并后的视频时长: 4.49秒，目标音频时长: 4.11秒
2025-07-29 10:40:29,713 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpt7ftgfj3\temp_combined.mp4 -ss 0 -to 4.105 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-29 10:40:29,977 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:29,977 - INFO - 目标音频时长: 4.11秒
2025-07-29 10:40:29,977 - INFO - 实际视频时长: 4.14秒
2025-07-29 10:40:29,977 - INFO - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:40:29,977 - INFO - ==========================================
2025-07-29 10:40:29,977 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:29,977 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-29 10:40:29,979 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt7ftgfj3
2025-07-29 10:40:30,021 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:30,021 - INFO -   - 音频时长: 4.11秒
2025-07-29 10:40:30,021 - INFO -   - 视频时长: 4.14秒
2025-07-29 10:40:30,021 - INFO -   - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:40:30,021 - INFO - 
----- 处理字幕 #11 的方案 #2 -----
2025-07-29 10:40:30,021 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-29 10:40:30,021 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyu4j6l71
2025-07-29 10:40:30,022 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\63.mp4 (确认存在: True)
2025-07-29 10:40:30,022 - INFO - 添加场景ID=63，时长=2.16秒，累计时长=2.16秒
2025-07-29 10:40:30,022 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\64.mp4 (确认存在: True)
2025-07-29 10:40:30,022 - INFO - 添加场景ID=64，时长=2.12秒，累计时长=4.28秒
2025-07-29 10:40:30,022 - INFO - 准备合并 2 个场景文件，总时长约 4.28秒
2025-07-29 10:40:30,023 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/63.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/64.mp4'

2025-07-29 10:40:30,023 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpyu4j6l71\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpyu4j6l71\temp_combined.mp4
2025-07-29 10:40:30,137 - INFO - 合并后的视频时长: 4.33秒，目标音频时长: 4.11秒
2025-07-29 10:40:30,138 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpyu4j6l71\temp_combined.mp4 -ss 0 -to 4.105 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-29 10:40:30,401 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:30,401 - INFO - 目标音频时长: 4.11秒
2025-07-29 10:40:30,401 - INFO - 实际视频时长: 4.14秒
2025-07-29 10:40:30,401 - INFO - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:40:30,402 - INFO - ==========================================
2025-07-29 10:40:30,402 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:30,402 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-29 10:40:30,402 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyu4j6l71
2025-07-29 10:40:30,443 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:30,443 - INFO -   - 音频时长: 4.11秒
2025-07-29 10:40:30,443 - INFO -   - 视频时长: 4.14秒
2025-07-29 10:40:30,443 - INFO -   - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:40:30,443 - INFO - 
----- 处理字幕 #11 的方案 #3 -----
2025-07-29 10:40:30,443 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\11_3.mp4
2025-07-29 10:40:30,444 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmph3wftqe_
2025-07-29 10:40:30,444 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\61.mp4 (确认存在: True)
2025-07-29 10:40:30,444 - INFO - 添加场景ID=61，时长=2.24秒，累计时长=2.24秒
2025-07-29 10:40:30,444 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\64.mp4 (确认存在: True)
2025-07-29 10:40:30,444 - INFO - 添加场景ID=64，时长=2.12秒，累计时长=4.36秒
2025-07-29 10:40:30,444 - INFO - 准备合并 2 个场景文件，总时长约 4.36秒
2025-07-29 10:40:30,444 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/61.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/64.mp4'

2025-07-29 10:40:30,444 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmph3wftqe_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmph3wftqe_\temp_combined.mp4
2025-07-29 10:40:30,565 - INFO - 合并后的视频时长: 4.41秒，目标音频时长: 4.11秒
2025-07-29 10:40:30,565 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmph3wftqe_\temp_combined.mp4 -ss 0 -to 4.105 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\11_3.mp4
2025-07-29 10:40:30,857 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:30,857 - INFO - 目标音频时长: 4.11秒
2025-07-29 10:40:30,857 - INFO - 实际视频时长: 4.14秒
2025-07-29 10:40:30,857 - INFO - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:40:30,857 - INFO - ==========================================
2025-07-29 10:40:30,857 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:30,857 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\11_3.mp4
2025-07-29 10:40:30,858 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmph3wftqe_
2025-07-29 10:40:30,901 - INFO - 方案 #3 处理完成:
2025-07-29 10:40:30,901 - INFO -   - 音频时长: 4.11秒
2025-07-29 10:40:30,901 - INFO -   - 视频时长: 4.14秒
2025-07-29 10:40:30,901 - INFO -   - 时长差异: 0.04秒 (0.93%)
2025-07-29 10:40:30,901 - INFO - 
字幕 #11 处理完成，成功生成 3/3 套方案
2025-07-29 10:40:30,901 - INFO - 生成的视频文件:
2025-07-29 10:40:30,901 - INFO -   1. F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-29 10:40:30,902 - INFO -   2. F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-29 10:40:30,902 - INFO -   3. F:/github/aicut_auto/newcut_ai\11_3.mp4
2025-07-29 10:40:30,902 - INFO - ========== 字幕 #11 处理结束 ==========

