2025-07-29 10:40:59,585 - INFO - ========== 字幕 #34 处理开始 ==========
2025-07-29 10:40:59,585 - INFO - 字幕内容: 她惊觉酒有问题，歹人却妄想生米煮成熟饭。
2025-07-29 10:40:59,585 - INFO - 字幕序号: [194, 201]
2025-07-29 10:40:59,585 - INFO - 音频文件详情:
2025-07-29 10:40:59,585 - INFO -   - 路径: output\34.wav
2025-07-29 10:40:59,585 - INFO -   - 时长: 3.27秒
2025-07-29 10:40:59,585 - INFO -   - 验证音频时长: 3.27秒
2025-07-29 10:40:59,586 - INFO - 字幕时间戳信息:
2025-07-29 10:40:59,586 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:59,586 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:59,586 - INFO -   - 根据生成的音频时长(3.27秒)已调整字幕时间戳
2025-07-29 10:40:59,586 - INFO - ========== 新模式：为字幕 #34 生成4套场景方案 ==========
2025-07-29 10:40:59,586 - INFO - 字幕序号列表: [194, 201]
2025-07-29 10:40:59,586 - INFO - 
--- 生成方案 #1：基于字幕序号 #194 ---
2025-07-29 10:40:59,586 - INFO - 开始为单个字幕序号 #194 匹配场景，目标时长: 3.27秒
2025-07-29 10:40:59,586 - INFO - 开始查找字幕序号 [194] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:59,586 - INFO - 找到related_overlap场景: scene_id=276, 字幕#194
2025-07-29 10:40:59,586 - INFO - 找到related_overlap场景: scene_id=277, 字幕#194
2025-07-29 10:40:59,586 - INFO - 找到related_between场景: scene_id=271, 字幕#194
2025-07-29 10:40:59,586 - INFO - 找到related_between场景: scene_id=272, 字幕#194
2025-07-29 10:40:59,586 - INFO - 找到related_between场景: scene_id=273, 字幕#194
2025-07-29 10:40:59,586 - INFO - 找到related_between场景: scene_id=274, 字幕#194
2025-07-29 10:40:59,586 - INFO - 找到related_between场景: scene_id=275, 字幕#194
2025-07-29 10:40:59,588 - INFO - 字幕 #194 找到 2 个overlap场景, 5 个between场景
2025-07-29 10:40:59,588 - INFO - 字幕序号 #194 找到 2 个可用overlap场景, 5 个可用between场景
2025-07-29 10:40:59,588 - INFO - 选择第一个overlap场景作为起点: scene_id=276
2025-07-29 10:40:59,588 - INFO - 添加起点场景: scene_id=276, 时长=0.76秒, 累计时长=0.76秒
2025-07-29 10:40:59,588 - INFO - 起点场景时长不足，需要延伸填充 2.51秒
2025-07-29 10:40:59,588 - INFO - 起点场景在原始列表中的索引: 275
2025-07-29 10:40:59,588 - INFO - 延伸添加场景: scene_id=277 (裁剪至 2.51秒)
2025-07-29 10:40:59,588 - INFO - 累计时长: 3.27秒
2025-07-29 10:40:59,588 - INFO - 字幕序号 #194 场景匹配完成，共选择 2 个场景，总时长: 3.27秒
2025-07-29 10:40:59,588 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-07-29 10:40:59,588 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-07-29 10:40:59,588 - INFO - 
--- 生成方案 #2：基于字幕序号 #201 ---
2025-07-29 10:40:59,588 - INFO - 开始为单个字幕序号 #201 匹配场景，目标时长: 3.27秒
2025-07-29 10:40:59,588 - INFO - 开始查找字幕序号 [201] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:59,588 - INFO - 找到related_overlap场景: scene_id=280, 字幕#201
2025-07-29 10:40:59,588 - INFO - 找到related_overlap场景: scene_id=281, 字幕#201
2025-07-29 10:40:59,589 - INFO - 字幕 #201 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:59,589 - INFO - 字幕序号 #201 找到 2 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:59,589 - INFO - 选择第一个overlap场景作为起点: scene_id=280
2025-07-29 10:40:59,589 - INFO - 添加起点场景: scene_id=280, 时长=1.00秒, 累计时长=1.00秒
2025-07-29 10:40:59,589 - INFO - 起点场景时长不足，需要延伸填充 2.27秒
2025-07-29 10:40:59,589 - INFO - 起点场景在原始列表中的索引: 279
2025-07-29 10:40:59,589 - INFO - 延伸添加场景: scene_id=281 (完整时长 2.00秒)
2025-07-29 10:40:59,589 - INFO - 累计时长: 3.00秒
2025-07-29 10:40:59,589 - INFO - 延伸添加场景: scene_id=282 (裁剪至 0.27秒)
2025-07-29 10:40:59,589 - INFO - 累计时长: 3.27秒
2025-07-29 10:40:59,589 - INFO - 字幕序号 #201 场景匹配完成，共选择 3 个场景，总时长: 3.27秒
2025-07-29 10:40:59,589 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:40:59,589 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:40:59,589 - INFO - ========== 当前模式：为字幕 #34 生成 1 套场景方案 ==========
2025-07-29 10:40:59,589 - INFO - 开始查找字幕序号 [194, 201] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:59,589 - INFO - 找到related_overlap场景: scene_id=276, 字幕#194
2025-07-29 10:40:59,589 - INFO - 找到related_overlap场景: scene_id=277, 字幕#194
2025-07-29 10:40:59,589 - INFO - 找到related_overlap场景: scene_id=280, 字幕#201
2025-07-29 10:40:59,589 - INFO - 找到related_overlap场景: scene_id=281, 字幕#201
2025-07-29 10:40:59,590 - INFO - 找到related_between场景: scene_id=271, 字幕#194
2025-07-29 10:40:59,590 - INFO - 找到related_between场景: scene_id=272, 字幕#194
2025-07-29 10:40:59,590 - INFO - 找到related_between场景: scene_id=273, 字幕#194
2025-07-29 10:40:59,590 - INFO - 找到related_between场景: scene_id=274, 字幕#194
2025-07-29 10:40:59,590 - INFO - 找到related_between场景: scene_id=275, 字幕#194
2025-07-29 10:40:59,591 - INFO - 字幕 #194 找到 2 个overlap场景, 5 个between场景
2025-07-29 10:40:59,591 - INFO - 字幕 #201 找到 2 个overlap场景, 0 个between场景
2025-07-29 10:40:59,591 - INFO - 共收集 4 个未使用的overlap场景和 5 个未使用的between场景
2025-07-29 10:40:59,591 - INFO - 开始生成方案 #1
2025-07-29 10:40:59,591 - INFO - 方案 #1: 为字幕#194选择初始化overlap场景id=276
2025-07-29 10:40:59,591 - INFO - 方案 #1: 为字幕#201选择初始化overlap场景id=280
2025-07-29 10:40:59,591 - INFO - 方案 #1: 初始选择后，当前总时长=1.76秒
2025-07-29 10:40:59,591 - INFO - 方案 #1: 额外添加overlap场景id=277, 当前总时长=4.80秒
2025-07-29 10:40:59,591 - INFO - 方案 #1: 额外between选择后，当前总时长=4.80秒
2025-07-29 10:40:59,591 - INFO - 方案 #1: 场景总时长(4.80秒)大于音频时长(3.27秒)，需要裁剪
2025-07-29 10:40:59,591 - INFO - 调整前总时长: 4.80秒, 目标时长: 3.27秒
2025-07-29 10:40:59,591 - INFO - 需要裁剪 1.53秒
2025-07-29 10:40:59,591 - INFO - 裁剪最长场景ID=277：从3.04秒裁剪至1.51秒
2025-07-29 10:40:59,591 - INFO - 调整后总时长: 3.27秒，与目标时长差异: 0.00秒
2025-07-29 10:40:59,591 - INFO - 方案 #1 调整/填充后最终总时长: 3.27秒
2025-07-29 10:40:59,591 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:59,591 - INFO - ========== 当前模式：字幕 #34 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:59,591 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:40:59,591 - INFO - ========== 新模式：字幕 #34 共生成 3 套有效场景方案 ==========
2025-07-29 10:40:59,591 - INFO - 
----- 处理字幕 #34 的方案 #1 -----
2025-07-29 10:40:59,591 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-29 10:40:59,591 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_5yxapp4
2025-07-29 10:40:59,592 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\276.mp4 (确认存在: True)
2025-07-29 10:40:59,592 - INFO - 添加场景ID=276，时长=0.76秒，累计时长=0.76秒
2025-07-29 10:40:59,592 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\277.mp4 (确认存在: True)
2025-07-29 10:40:59,592 - INFO - 添加场景ID=277，时长=3.04秒，累计时长=3.80秒
2025-07-29 10:40:59,592 - INFO - 准备合并 2 个场景文件，总时长约 3.80秒
2025-07-29 10:40:59,592 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/276.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/277.mp4'

2025-07-29 10:40:59,592 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_5yxapp4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_5yxapp4\temp_combined.mp4
2025-07-29 10:40:59,700 - INFO - 合并后的视频时长: 3.85秒，目标音频时长: 3.27秒
2025-07-29 10:40:59,700 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_5yxapp4\temp_combined.mp4 -ss 0 -to 3.271 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-29 10:40:59,959 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:59,959 - INFO - 目标音频时长: 3.27秒
2025-07-29 10:40:59,959 - INFO - 实际视频时长: 3.30秒
2025-07-29 10:40:59,959 - INFO - 时长差异: 0.03秒 (0.98%)
2025-07-29 10:40:59,959 - INFO - ==========================================
2025-07-29 10:40:59,959 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:59,959 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-29 10:40:59,960 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_5yxapp4
2025-07-29 10:41:00,001 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:00,001 - INFO -   - 音频时长: 3.27秒
2025-07-29 10:41:00,001 - INFO -   - 视频时长: 3.30秒
2025-07-29 10:41:00,001 - INFO -   - 时长差异: 0.03秒 (0.98%)
2025-07-29 10:41:00,001 - INFO - 
----- 处理字幕 #34 的方案 #2 -----
2025-07-29 10:41:00,001 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-29 10:41:00,002 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmy082b2i
2025-07-29 10:41:00,002 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\280.mp4 (确认存在: True)
2025-07-29 10:41:00,002 - INFO - 添加场景ID=280，时长=1.00秒，累计时长=1.00秒
2025-07-29 10:41:00,002 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\281.mp4 (确认存在: True)
2025-07-29 10:41:00,002 - INFO - 添加场景ID=281，时长=2.00秒，累计时长=3.00秒
2025-07-29 10:41:00,002 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\282.mp4 (确认存在: True)
2025-07-29 10:41:00,002 - INFO - 添加场景ID=282，时长=1.68秒，累计时长=4.68秒
2025-07-29 10:41:00,002 - INFO - 准备合并 3 个场景文件，总时长约 4.68秒
2025-07-29 10:41:00,003 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/280.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/281.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/282.mp4'

2025-07-29 10:41:00,003 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmy082b2i\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmy082b2i\temp_combined.mp4
2025-07-29 10:41:00,164 - INFO - 合并后的视频时长: 4.75秒，目标音频时长: 3.27秒
2025-07-29 10:41:00,164 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmy082b2i\temp_combined.mp4 -ss 0 -to 3.271 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-29 10:41:00,456 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:00,456 - INFO - 目标音频时长: 3.27秒
2025-07-29 10:41:00,456 - INFO - 实际视频时长: 3.30秒
2025-07-29 10:41:00,456 - INFO - 时长差异: 0.03秒 (0.98%)
2025-07-29 10:41:00,456 - INFO - ==========================================
2025-07-29 10:41:00,456 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:00,456 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-29 10:41:00,457 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmy082b2i
2025-07-29 10:41:00,501 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:00,501 - INFO -   - 音频时长: 3.27秒
2025-07-29 10:41:00,501 - INFO -   - 视频时长: 3.30秒
2025-07-29 10:41:00,501 - INFO -   - 时长差异: 0.03秒 (0.98%)
2025-07-29 10:41:00,501 - INFO - 
----- 处理字幕 #34 的方案 #3 -----
2025-07-29 10:41:00,501 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-07-29 10:41:00,501 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp231ekura
2025-07-29 10:41:00,502 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\276.mp4 (确认存在: True)
2025-07-29 10:41:00,502 - INFO - 添加场景ID=276，时长=0.76秒，累计时长=0.76秒
2025-07-29 10:41:00,502 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\280.mp4 (确认存在: True)
2025-07-29 10:41:00,502 - INFO - 添加场景ID=280，时长=1.00秒，累计时长=1.76秒
2025-07-29 10:41:00,502 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\277.mp4 (确认存在: True)
2025-07-29 10:41:00,502 - INFO - 添加场景ID=277，时长=3.04秒，累计时长=4.80秒
2025-07-29 10:41:00,502 - INFO - 准备合并 3 个场景文件，总时长约 4.80秒
2025-07-29 10:41:00,502 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/276.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/280.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/277.mp4'

2025-07-29 10:41:00,502 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp231ekura\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp231ekura\temp_combined.mp4
2025-07-29 10:41:00,639 - INFO - 合并后的视频时长: 4.87秒，目标音频时长: 3.27秒
2025-07-29 10:41:00,639 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp231ekura\temp_combined.mp4 -ss 0 -to 3.271 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-07-29 10:41:00,919 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:00,919 - INFO - 目标音频时长: 3.27秒
2025-07-29 10:41:00,919 - INFO - 实际视频时长: 3.30秒
2025-07-29 10:41:00,919 - INFO - 时长差异: 0.03秒 (0.98%)
2025-07-29 10:41:00,919 - INFO - ==========================================
2025-07-29 10:41:00,919 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:00,919 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-07-29 10:41:00,920 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp231ekura
2025-07-29 10:41:00,961 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:00,961 - INFO -   - 音频时长: 3.27秒
2025-07-29 10:41:00,961 - INFO -   - 视频时长: 3.30秒
2025-07-29 10:41:00,961 - INFO -   - 时长差异: 0.03秒 (0.98%)
2025-07-29 10:41:00,961 - INFO - 
字幕 #34 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:00,961 - INFO - 生成的视频文件:
2025-07-29 10:41:00,961 - INFO -   1. F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-29 10:41:00,961 - INFO -   2. F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-29 10:41:00,961 - INFO -   3. F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-07-29 10:41:00,961 - INFO - ========== 字幕 #34 处理结束 ==========

