2025-07-29 10:40:54,467 - INFO - ========== 字幕 #30 处理开始 ==========
2025-07-29 10:40:54,467 - INFO - 字幕内容: 祖母坚持要亲自调查人品，才能放心孙女婚事。
2025-07-29 10:40:54,467 - INFO - 字幕序号: [167, 171]
2025-07-29 10:40:54,468 - INFO - 音频文件详情:
2025-07-29 10:40:54,468 - INFO -   - 路径: output\30.wav
2025-07-29 10:40:54,468 - INFO -   - 时长: 5.09秒
2025-07-29 10:40:54,468 - INFO -   - 验证音频时长: 5.09秒
2025-07-29 10:40:54,468 - INFO - 字幕时间戳信息:
2025-07-29 10:40:54,468 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:40:54,468 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:40:54,468 - INFO -   - 根据生成的音频时长(5.09秒)已调整字幕时间戳
2025-07-29 10:40:54,468 - INFO - ========== 新模式：为字幕 #30 生成4套场景方案 ==========
2025-07-29 10:40:54,468 - INFO - 字幕序号列表: [167, 171]
2025-07-29 10:40:54,468 - INFO - 
--- 生成方案 #1：基于字幕序号 #167 ---
2025-07-29 10:40:54,468 - INFO - 开始为单个字幕序号 #167 匹配场景，目标时长: 5.09秒
2025-07-29 10:40:54,468 - INFO - 开始查找字幕序号 [167] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:54,468 - INFO - 找到related_overlap场景: scene_id=245, 字幕#167
2025-07-29 10:40:54,470 - INFO - 字幕 #167 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:54,470 - INFO - 字幕序号 #167 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:54,470 - INFO - 选择第一个overlap场景作为起点: scene_id=245
2025-07-29 10:40:54,470 - INFO - 添加起点场景: scene_id=245, 时长=1.44秒, 累计时长=1.44秒
2025-07-29 10:40:54,470 - INFO - 起点场景时长不足，需要延伸填充 3.65秒
2025-07-29 10:40:54,470 - INFO - 起点场景在原始列表中的索引: 244
2025-07-29 10:40:54,470 - INFO - 延伸添加场景: scene_id=246 (完整时长 3.44秒)
2025-07-29 10:40:54,470 - INFO - 累计时长: 4.88秒
2025-07-29 10:40:54,470 - INFO - 延伸添加场景: scene_id=247 (裁剪至 0.21秒)
2025-07-29 10:40:54,470 - INFO - 累计时长: 5.09秒
2025-07-29 10:40:54,470 - INFO - 字幕序号 #167 场景匹配完成，共选择 3 个场景，总时长: 5.09秒
2025-07-29 10:40:54,470 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:40:54,470 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:40:54,470 - INFO - 
--- 生成方案 #2：基于字幕序号 #171 ---
2025-07-29 10:40:54,470 - INFO - 开始为单个字幕序号 #171 匹配场景，目标时长: 5.09秒
2025-07-29 10:40:54,470 - INFO - 开始查找字幕序号 [171] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:54,470 - INFO - 找到related_overlap场景: scene_id=247, 字幕#171
2025-07-29 10:40:54,471 - INFO - 字幕 #171 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:54,471 - INFO - 字幕序号 #171 找到 0 个可用overlap场景, 0 个可用between场景
2025-07-29 10:40:54,471 - ERROR - 字幕序号 #171 没有找到任何可用的匹配场景
2025-07-29 10:40:54,471 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-07-29 10:40:54,471 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-07-29 10:40:54,471 - INFO - ========== 当前模式：为字幕 #30 生成 1 套场景方案 ==========
2025-07-29 10:40:54,471 - INFO - 开始查找字幕序号 [167, 171] 对应的场景，共有 3443 个场景可选
2025-07-29 10:40:54,472 - INFO - 找到related_overlap场景: scene_id=245, 字幕#167
2025-07-29 10:40:54,472 - INFO - 找到related_overlap场景: scene_id=247, 字幕#171
2025-07-29 10:40:54,473 - INFO - 字幕 #167 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:54,473 - INFO - 字幕 #171 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:40:54,473 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-29 10:40:54,473 - INFO - 开始生成方案 #1
2025-07-29 10:40:54,473 - INFO - 方案 #1: 为字幕#167选择初始化overlap场景id=245
2025-07-29 10:40:54,473 - INFO - 方案 #1: 为字幕#171选择初始化overlap场景id=247
2025-07-29 10:40:54,473 - INFO - 方案 #1: 初始选择后，当前总时长=4.84秒
2025-07-29 10:40:54,473 - INFO - 方案 #1: 额外between选择后，当前总时长=4.84秒
2025-07-29 10:40:54,473 - INFO - 方案 #1: 场景总时长(4.84秒)小于音频时长(5.09秒)，需要延伸填充
2025-07-29 10:40:54,473 - INFO - 方案 #1: 最后一个场景ID: 247
2025-07-29 10:40:54,473 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 246
2025-07-29 10:40:54,473 - INFO - 方案 #1: 需要填充时长: 0.25秒
2025-07-29 10:40:54,473 - INFO - 方案 #1: 追加场景 scene_id=248 (裁剪至 0.25秒)
2025-07-29 10:40:54,473 - INFO - 方案 #1: 成功填充至目标时长
2025-07-29 10:40:54,473 - INFO - 方案 #1 调整/填充后最终总时长: 5.09秒
2025-07-29 10:40:54,473 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:40:54,473 - INFO - ========== 当前模式：字幕 #30 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:40:54,473 - INFO - 方案 #2 (传统模式) 生成成功
2025-07-29 10:40:54,473 - INFO - ========== 新模式：字幕 #30 共生成 2 套有效场景方案 ==========
2025-07-29 10:40:54,473 - INFO - 
----- 处理字幕 #30 的方案 #1 -----
2025-07-29 10:40:54,473 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-29 10:40:54,473 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9rb9bkct
2025-07-29 10:40:54,474 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\245.mp4 (确认存在: True)
2025-07-29 10:40:54,474 - INFO - 添加场景ID=245，时长=1.44秒，累计时长=1.44秒
2025-07-29 10:40:54,474 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\246.mp4 (确认存在: True)
2025-07-29 10:40:54,474 - INFO - 添加场景ID=246，时长=3.44秒，累计时长=4.88秒
2025-07-29 10:40:54,474 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\247.mp4 (确认存在: True)
2025-07-29 10:40:54,474 - INFO - 添加场景ID=247，时长=3.40秒，累计时长=8.28秒
2025-07-29 10:40:54,474 - INFO - 场景总时长(8.28秒)已达到音频时长(5.09秒)的1.5倍，停止添加场景
2025-07-29 10:40:54,474 - INFO - 准备合并 3 个场景文件，总时长约 8.28秒
2025-07-29 10:40:54,474 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/245.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/246.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/247.mp4'

2025-07-29 10:40:54,475 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9rb9bkct\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9rb9bkct\temp_combined.mp4
2025-07-29 10:40:54,617 - INFO - 合并后的视频时长: 8.35秒，目标音频时长: 5.09秒
2025-07-29 10:40:54,617 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9rb9bkct\temp_combined.mp4 -ss 0 -to 5.088 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-29 10:40:54,915 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:54,916 - INFO - 目标音频时长: 5.09秒
2025-07-29 10:40:54,916 - INFO - 实际视频时长: 5.14秒
2025-07-29 10:40:54,916 - INFO - 时长差异: 0.05秒 (1.08%)
2025-07-29 10:40:54,916 - INFO - ==========================================
2025-07-29 10:40:54,916 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:54,916 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-29 10:40:54,916 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9rb9bkct
2025-07-29 10:40:54,959 - INFO - 方案 #1 处理完成:
2025-07-29 10:40:54,959 - INFO -   - 音频时长: 5.09秒
2025-07-29 10:40:54,959 - INFO -   - 视频时长: 5.14秒
2025-07-29 10:40:54,959 - INFO -   - 时长差异: 0.05秒 (1.08%)
2025-07-29 10:40:54,959 - INFO - 
----- 处理字幕 #30 的方案 #2 -----
2025-07-29 10:40:54,959 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-29 10:40:54,960 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdn1ykp9w
2025-07-29 10:40:54,960 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\245.mp4 (确认存在: True)
2025-07-29 10:40:54,960 - INFO - 添加场景ID=245，时长=1.44秒，累计时长=1.44秒
2025-07-29 10:40:54,960 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\247.mp4 (确认存在: True)
2025-07-29 10:40:54,960 - INFO - 添加场景ID=247，时长=3.40秒，累计时长=4.84秒
2025-07-29 10:40:54,960 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\248.mp4 (确认存在: True)
2025-07-29 10:40:54,960 - INFO - 添加场景ID=248，时长=3.80秒，累计时长=8.64秒
2025-07-29 10:40:54,960 - INFO - 场景总时长(8.64秒)已达到音频时长(5.09秒)的1.5倍，停止添加场景
2025-07-29 10:40:54,961 - INFO - 准备合并 3 个场景文件，总时长约 8.64秒
2025-07-29 10:40:54,961 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/245.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/247.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/248.mp4'

2025-07-29 10:40:54,961 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpdn1ykp9w\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpdn1ykp9w\temp_combined.mp4
2025-07-29 10:40:55,092 - INFO - 合并后的视频时长: 8.71秒，目标音频时长: 5.09秒
2025-07-29 10:40:55,092 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpdn1ykp9w\temp_combined.mp4 -ss 0 -to 5.088 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-29 10:40:55,409 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:40:55,409 - INFO - 目标音频时长: 5.09秒
2025-07-29 10:40:55,409 - INFO - 实际视频时长: 5.14秒
2025-07-29 10:40:55,409 - INFO - 时长差异: 0.05秒 (1.08%)
2025-07-29 10:40:55,409 - INFO - ==========================================
2025-07-29 10:40:55,409 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:40:55,409 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-29 10:40:55,409 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdn1ykp9w
2025-07-29 10:40:55,451 - INFO - 方案 #2 处理完成:
2025-07-29 10:40:55,451 - INFO -   - 音频时长: 5.09秒
2025-07-29 10:40:55,451 - INFO -   - 视频时长: 5.14秒
2025-07-29 10:40:55,451 - INFO -   - 时长差异: 0.05秒 (1.08%)
2025-07-29 10:40:55,452 - INFO - 
字幕 #30 处理完成，成功生成 2/2 套方案
2025-07-29 10:40:55,452 - INFO - 生成的视频文件:
2025-07-29 10:40:55,452 - INFO -   1. F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-29 10:40:55,452 - INFO -   2. F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-29 10:40:55,452 - INFO - ========== 字幕 #30 处理结束 ==========

