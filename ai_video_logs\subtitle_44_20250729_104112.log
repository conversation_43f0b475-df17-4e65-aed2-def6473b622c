2025-07-29 10:41:12,984 - INFO - ========== 字幕 #44 处理开始 ==========
2025-07-29 10:41:12,984 - INFO - 字幕内容: 事后九姨娘假意关心，称此事有损女人的名节。
2025-07-29 10:41:12,984 - INFO - 字幕序号: [252, 256]
2025-07-29 10:41:12,984 - INFO - 音频文件详情:
2025-07-29 10:41:12,984 - INFO -   - 路径: output\44.wav
2025-07-29 10:41:12,984 - INFO -   - 时长: 4.02秒
2025-07-29 10:41:12,985 - INFO -   - 验证音频时长: 4.02秒
2025-07-29 10:41:12,985 - INFO - 字幕时间戳信息:
2025-07-29 10:41:12,985 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-29 10:41:12,985 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-29 10:41:12,985 - INFO -   - 根据生成的音频时长(4.02秒)已调整字幕时间戳
2025-07-29 10:41:12,985 - INFO - ========== 新模式：为字幕 #44 生成4套场景方案 ==========
2025-07-29 10:41:12,985 - INFO - 字幕序号列表: [252, 256]
2025-07-29 10:41:12,985 - INFO - 
--- 生成方案 #1：基于字幕序号 #252 ---
2025-07-29 10:41:12,985 - INFO - 开始为单个字幕序号 #252 匹配场景，目标时长: 4.02秒
2025-07-29 10:41:12,985 - INFO - 开始查找字幕序号 [252] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:12,985 - INFO - 找到related_overlap场景: scene_id=384, 字幕#252
2025-07-29 10:41:12,986 - INFO - 找到related_between场景: scene_id=383, 字幕#252
2025-07-29 10:41:12,986 - INFO - 字幕 #252 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:12,986 - INFO - 字幕序号 #252 找到 1 个可用overlap场景, 1 个可用between场景
2025-07-29 10:41:12,986 - INFO - 选择第一个overlap场景作为起点: scene_id=384
2025-07-29 10:41:12,986 - INFO - 添加起点场景: scene_id=384, 时长=2.88秒, 累计时长=2.88秒
2025-07-29 10:41:12,986 - INFO - 起点场景时长不足，需要延伸填充 1.14秒
2025-07-29 10:41:12,986 - INFO - 起点场景在原始列表中的索引: 383
2025-07-29 10:41:12,986 - INFO - 延伸添加场景: scene_id=385 (完整时长 1.12秒)
2025-07-29 10:41:12,986 - INFO - 累计时长: 4.00秒
2025-07-29 10:41:12,986 - INFO - 延伸添加场景: scene_id=386 (裁剪至 0.02秒)
2025-07-29 10:41:12,986 - INFO - 累计时长: 4.02秒
2025-07-29 10:41:12,986 - INFO - 字幕序号 #252 场景匹配完成，共选择 3 个场景，总时长: 4.02秒
2025-07-29 10:41:12,986 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-07-29 10:41:12,986 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-07-29 10:41:12,986 - INFO - 
--- 生成方案 #2：基于字幕序号 #256 ---
2025-07-29 10:41:12,986 - INFO - 开始为单个字幕序号 #256 匹配场景，目标时长: 4.02秒
2025-07-29 10:41:12,986 - INFO - 开始查找字幕序号 [256] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:12,987 - INFO - 找到related_overlap场景: scene_id=389, 字幕#256
2025-07-29 10:41:12,988 - INFO - 字幕 #256 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:12,988 - INFO - 字幕序号 #256 找到 1 个可用overlap场景, 0 个可用between场景
2025-07-29 10:41:12,988 - INFO - 选择第一个overlap场景作为起点: scene_id=389
2025-07-29 10:41:12,988 - INFO - 添加起点场景: scene_id=389, 时长=1.28秒, 累计时长=1.28秒
2025-07-29 10:41:12,988 - INFO - 起点场景时长不足，需要延伸填充 2.74秒
2025-07-29 10:41:12,988 - INFO - 起点场景在原始列表中的索引: 388
2025-07-29 10:41:12,988 - INFO - 延伸添加场景: scene_id=390 (完整时长 1.80秒)
2025-07-29 10:41:12,988 - INFO - 累计时长: 3.08秒
2025-07-29 10:41:12,988 - INFO - 延伸添加场景: scene_id=391 (裁剪至 0.94秒)
2025-07-29 10:41:12,988 - INFO - 累计时长: 4.02秒
2025-07-29 10:41:12,988 - INFO - 字幕序号 #256 场景匹配完成，共选择 3 个场景，总时长: 4.02秒
2025-07-29 10:41:12,988 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-07-29 10:41:12,988 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-07-29 10:41:12,988 - INFO - ========== 当前模式：为字幕 #44 生成 1 套场景方案 ==========
2025-07-29 10:41:12,988 - INFO - 开始查找字幕序号 [252, 256] 对应的场景，共有 3443 个场景可选
2025-07-29 10:41:12,988 - INFO - 找到related_overlap场景: scene_id=384, 字幕#252
2025-07-29 10:41:12,988 - INFO - 找到related_overlap场景: scene_id=389, 字幕#256
2025-07-29 10:41:12,989 - INFO - 找到related_between场景: scene_id=383, 字幕#252
2025-07-29 10:41:12,989 - INFO - 字幕 #252 找到 1 个overlap场景, 1 个between场景
2025-07-29 10:41:12,989 - INFO - 字幕 #256 找到 1 个overlap场景, 0 个between场景
2025-07-29 10:41:12,989 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-29 10:41:12,989 - INFO - 开始生成方案 #1
2025-07-29 10:41:12,989 - INFO - 方案 #1: 为字幕#252选择初始化overlap场景id=384
2025-07-29 10:41:12,989 - INFO - 方案 #1: 为字幕#256选择初始化overlap场景id=389
2025-07-29 10:41:12,989 - INFO - 方案 #1: 初始选择后，当前总时长=4.16秒
2025-07-29 10:41:12,989 - INFO - 方案 #1: 额外between选择后，当前总时长=4.16秒
2025-07-29 10:41:12,989 - INFO - 方案 #1: 场景总时长(4.16秒)大于音频时长(4.02秒)，需要裁剪
2025-07-29 10:41:12,989 - INFO - 调整前总时长: 4.16秒, 目标时长: 4.02秒
2025-07-29 10:41:12,989 - INFO - 需要裁剪 0.14秒
2025-07-29 10:41:12,989 - INFO - 裁剪最长场景ID=384：从2.88秒裁剪至2.74秒
2025-07-29 10:41:12,989 - INFO - 调整后总时长: 4.02秒，与目标时长差异: 0.00秒
2025-07-29 10:41:12,989 - INFO - 方案 #1 调整/填充后最终总时长: 4.02秒
2025-07-29 10:41:12,989 - INFO - 方案 #1 添加到方案列表
2025-07-29 10:41:12,989 - INFO - ========== 当前模式：字幕 #44 的 1 套有效场景方案生成完成 ==========
2025-07-29 10:41:12,989 - INFO - 方案 #3 (传统模式) 生成成功
2025-07-29 10:41:12,989 - INFO - ========== 新模式：字幕 #44 共生成 3 套有效场景方案 ==========
2025-07-29 10:41:12,990 - INFO - 
----- 处理字幕 #44 的方案 #1 -----
2025-07-29 10:41:12,990 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-29 10:41:12,990 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzuspn3d1
2025-07-29 10:41:12,990 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\384.mp4 (确认存在: True)
2025-07-29 10:41:12,991 - INFO - 添加场景ID=384，时长=2.88秒，累计时长=2.88秒
2025-07-29 10:41:12,991 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\385.mp4 (确认存在: True)
2025-07-29 10:41:12,991 - INFO - 添加场景ID=385，时长=1.12秒，累计时长=4.00秒
2025-07-29 10:41:12,991 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\386.mp4 (确认存在: True)
2025-07-29 10:41:12,991 - INFO - 添加场景ID=386，时长=1.04秒，累计时长=5.04秒
2025-07-29 10:41:12,991 - INFO - 准备合并 3 个场景文件，总时长约 5.04秒
2025-07-29 10:41:12,991 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/384.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/385.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/386.mp4'

2025-07-29 10:41:12,991 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzuspn3d1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzuspn3d1\temp_combined.mp4
2025-07-29 10:41:13,137 - INFO - 合并后的视频时长: 5.11秒，目标音频时长: 4.02秒
2025-07-29 10:41:13,137 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzuspn3d1\temp_combined.mp4 -ss 0 -to 4.016 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-29 10:41:13,411 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:13,411 - INFO - 目标音频时长: 4.02秒
2025-07-29 10:41:13,411 - INFO - 实际视频时长: 4.06秒
2025-07-29 10:41:13,411 - INFO - 时长差异: 0.05秒 (1.17%)
2025-07-29 10:41:13,411 - INFO - ==========================================
2025-07-29 10:41:13,411 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:13,411 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-29 10:41:13,411 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzuspn3d1
2025-07-29 10:41:13,457 - INFO - 方案 #1 处理完成:
2025-07-29 10:41:13,457 - INFO -   - 音频时长: 4.02秒
2025-07-29 10:41:13,457 - INFO -   - 视频时长: 4.06秒
2025-07-29 10:41:13,457 - INFO -   - 时长差异: 0.05秒 (1.17%)
2025-07-29 10:41:13,457 - INFO - 
----- 处理字幕 #44 的方案 #2 -----
2025-07-29 10:41:13,457 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-07-29 10:41:13,458 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpicql09t1
2025-07-29 10:41:13,458 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\389.mp4 (确认存在: True)
2025-07-29 10:41:13,458 - INFO - 添加场景ID=389，时长=1.28秒，累计时长=1.28秒
2025-07-29 10:41:13,458 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\390.mp4 (确认存在: True)
2025-07-29 10:41:13,458 - INFO - 添加场景ID=390，时长=1.80秒，累计时长=3.08秒
2025-07-29 10:41:13,458 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\391.mp4 (确认存在: True)
2025-07-29 10:41:13,458 - INFO - 添加场景ID=391，时长=3.04秒，累计时长=6.12秒
2025-07-29 10:41:13,458 - INFO - 场景总时长(6.12秒)已达到音频时长(4.02秒)的1.5倍，停止添加场景
2025-07-29 10:41:13,458 - INFO - 准备合并 3 个场景文件，总时长约 6.12秒
2025-07-29 10:41:13,458 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/389.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/390.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/391.mp4'

2025-07-29 10:41:13,459 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpicql09t1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpicql09t1\temp_combined.mp4
2025-07-29 10:41:13,617 - INFO - 合并后的视频时长: 6.19秒，目标音频时长: 4.02秒
2025-07-29 10:41:13,617 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpicql09t1\temp_combined.mp4 -ss 0 -to 4.016 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-07-29 10:41:13,906 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:13,906 - INFO - 目标音频时长: 4.02秒
2025-07-29 10:41:13,906 - INFO - 实际视频时长: 4.06秒
2025-07-29 10:41:13,906 - INFO - 时长差异: 0.05秒 (1.17%)
2025-07-29 10:41:13,906 - INFO - ==========================================
2025-07-29 10:41:13,906 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:13,906 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-07-29 10:41:13,906 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpicql09t1
2025-07-29 10:41:13,960 - INFO - 方案 #2 处理完成:
2025-07-29 10:41:13,960 - INFO -   - 音频时长: 4.02秒
2025-07-29 10:41:13,960 - INFO -   - 视频时长: 4.06秒
2025-07-29 10:41:13,960 - INFO -   - 时长差异: 0.05秒 (1.17%)
2025-07-29 10:41:13,960 - INFO - 
----- 处理字幕 #44 的方案 #3 -----
2025-07-29 10:41:13,960 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\44_3.mp4
2025-07-29 10:41:13,960 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_n84rzjv
2025-07-29 10:41:13,961 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\384.mp4 (确认存在: True)
2025-07-29 10:41:13,961 - INFO - 添加场景ID=384，时长=2.88秒，累计时长=2.88秒
2025-07-29 10:41:13,961 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\389.mp4 (确认存在: True)
2025-07-29 10:41:13,961 - INFO - 添加场景ID=389，时长=1.28秒，累计时长=4.16秒
2025-07-29 10:41:13,961 - INFO - 准备合并 2 个场景文件，总时长约 4.16秒
2025-07-29 10:41:13,961 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/384.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/389.mp4'

2025-07-29 10:41:13,961 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_n84rzjv\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_n84rzjv\temp_combined.mp4
2025-07-29 10:41:14,104 - INFO - 合并后的视频时长: 4.21秒，目标音频时长: 4.02秒
2025-07-29 10:41:14,104 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_n84rzjv\temp_combined.mp4 -ss 0 -to 4.016 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\44_3.mp4
2025-07-29 10:41:14,405 - INFO - ============ 视频与音频时长比较 ============
2025-07-29 10:41:14,405 - INFO - 目标音频时长: 4.02秒
2025-07-29 10:41:14,405 - INFO - 实际视频时长: 4.06秒
2025-07-29 10:41:14,405 - INFO - 时长差异: 0.05秒 (1.17%)
2025-07-29 10:41:14,405 - INFO - ==========================================
2025-07-29 10:41:14,405 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-29 10:41:14,405 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\44_3.mp4
2025-07-29 10:41:14,405 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_n84rzjv
2025-07-29 10:41:14,459 - INFO - 方案 #3 处理完成:
2025-07-29 10:41:14,459 - INFO -   - 音频时长: 4.02秒
2025-07-29 10:41:14,459 - INFO -   - 视频时长: 4.06秒
2025-07-29 10:41:14,459 - INFO -   - 时长差异: 0.05秒 (1.17%)
2025-07-29 10:41:14,459 - INFO - 
字幕 #44 处理完成，成功生成 3/3 套方案
2025-07-29 10:41:14,459 - INFO - 生成的视频文件:
2025-07-29 10:41:14,459 - INFO -   1. F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-29 10:41:14,459 - INFO -   2. F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-07-29 10:41:14,459 - INFO -   3. F:/github/aicut_auto/newcut_ai\44_3.mp4
2025-07-29 10:41:14,459 - INFO - ========== 字幕 #44 处理结束 ==========

